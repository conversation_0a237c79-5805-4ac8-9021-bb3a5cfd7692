# Machina OBD Diagnostic System Environment Configuration

# =============================================================================
# API Keys and External Services
# =============================================================================

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.3

# Alternative AI Services (Optional)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_AI_API_KEY=AIzaSyAxOwoCFs5KoJL3tKufL3GakXs3Yrbiess

# =============================================================================
# Database Configuration
# =============================================================================

# SQLite Database (Default)
DATABASE_URL=sqlite:///./machina_diagnostic.db
DATABASE_ECHO=false

# PostgreSQL Database (Production)
# DATABASE_URL=postgresql://username:password@localhost:5432/machina_diagnostic
# DATABASE_POOL_SIZE=10
# DATABASE_MAX_OVERFLOW=20

# =============================================================================
# OBD Interface Configuration
# =============================================================================

# OBD Adapter Settings
OBD_PORT=/dev/ttyUSB0
OBD_BAUDRATE=38400
OBD_TIMEOUT=30
OBD_PROTOCOL=auto

# Bluetooth OBD Settings
OBD_BLUETOOTH_ADDRESS=00:1D:A5:68:98:8B
OBD_BLUETOOTH_CHANNEL=1

# =============================================================================
# Application Configuration
# =============================================================================

# FastAPI Settings
APP_NAME=Machina OBD Diagnostic System
APP_VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Security
SECRET_KEY=your_secret_key_here_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE"]
ALLOWED_HEADERS=["*"]

# =============================================================================
# AI Engine Configuration
# =============================================================================

# AI Provider Selection
USE_OPENAI=true
USE_LOCAL_LLM=false
USE_ANTHROPIC=false

# Local LLM Configuration
LOCAL_LLM_MODEL_PATH=./models/llama-2-7b-chat.gguf
LOCAL_LLM_CONTEXT_SIZE=4096
LOCAL_LLM_THREADS=4

# AI Response Settings
AI_MAX_RETRIES=3
AI_TIMEOUT_SECONDS=30
AI_CACHE_RESPONSES=true
AI_CACHE_TTL_HOURS=24

# =============================================================================
# Brand Profile Configuration
# =============================================================================

# Enabled Brand Profiles
ENABLE_TOYOTA_PROFILE=true
ENABLE_RENAULT_PROFILE=true
ENABLE_VAG_PROFILE=true
ENABLE_BMW_PROFILE=true

# Brand-Specific API Keys (if needed)
TOYOTA_TECHSTREAM_API_KEY=your_toyota_api_key_here
BMW_ISTA_API_KEY=your_bmw_api_key_here
VAG_VCDS_API_KEY=your_vag_api_key_here

# =============================================================================
# Logging Configuration
# =============================================================================

# Log Levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=logs/machina.log
LOG_MAX_SIZE_MB=10
LOG_BACKUP_COUNT=5

# Component-Specific Logging
OBD_LOG_LEVEL=DEBUG
AI_LOG_LEVEL=INFO
DATABASE_LOG_LEVEL=WARNING

# =============================================================================
# Testing Configuration
# =============================================================================

# Test Database
TEST_DATABASE_URL=sqlite:///./test_machina.db

# Mock Data Settings
USE_MOCK_OBD_DATA=true
MOCK_DATA_DELAY_MS=100
GENERATE_RANDOM_DTCS=false

# Test Vehicle Data
TEST_VEHICLE_VIN=1HGBH41JXMN109186
TEST_VEHICLE_MAKE=Toyota
TEST_VEHICLE_MODEL=Prius
TEST_VEHICLE_YEAR=2015
TEST_VEHICLE_MILEAGE=85000

# =============================================================================
# Performance Configuration
# =============================================================================

# Caching
ENABLE_REDIS_CACHE=false
REDIS_URL=redis://localhost:6379/0
CACHE_DEFAULT_TTL=3600

# Rate Limiting
ENABLE_RATE_LIMITING=true
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Background Tasks
ENABLE_BACKGROUND_TASKS=true
TASK_QUEUE_SIZE=100
WORKER_THREADS=4

# =============================================================================
# Monitoring and Analytics
# =============================================================================

# Application Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Error Tracking
SENTRY_DSN=your_sentry_dsn_here
ENABLE_ERROR_TRACKING=false

# Analytics
ENABLE_USAGE_ANALYTICS=false
ANALYTICS_API_KEY=your_analytics_key_here

# =============================================================================
# External Integrations
# =============================================================================

# Email Notifications
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here
SMTP_USE_TLS=true

# SMS Notifications
TWILIO_ACCOUNT_SID=your_twilio_sid_here
TWILIO_AUTH_TOKEN=your_twilio_token_here
TWILIO_PHONE_NUMBER=+**********

# Cloud Storage
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
S3_BUCKET_NAME=machina-diagnostic-data

# =============================================================================
# Development Configuration
# =============================================================================

# Development Tools
ENABLE_SWAGGER_UI=true
ENABLE_REDOC=true
ENABLE_DEBUG_TOOLBAR=true

# Hot Reload
RELOAD_ON_CHANGE=true
WATCH_DIRECTORIES=["app", "tests"]

# Development Database
DEV_POPULATE_SAMPLE_DATA=true
DEV_RESET_DB_ON_START=false

# =============================================================================
# Production Configuration
# =============================================================================

# Production Security
PRODUCTION_MODE=false
SECURE_COOKIES=true
HTTPS_ONLY=false

# Load Balancing
ENABLE_LOAD_BALANCER=false
LOAD_BALANCER_HEALTH_CHECK=/health

# Backup Configuration
ENABLE_AUTO_BACKUP=false
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# =============================================================================
# Feature Flags
# =============================================================================

# Experimental Features
ENABLE_PREDICTIVE_ANALYSIS=false
ENABLE_MACHINE_LEARNING=false
ENABLE_VOICE_INTERFACE=false
ENABLE_MOBILE_APP_SYNC=false

# Beta Features
ENABLE_ADVANCED_DIAGNOSTICS=true
ENABLE_REAL_TIME_MONITORING=false
ENABLE_FLEET_MANAGEMENT=false

# =============================================================================
# Localization
# =============================================================================

# Language Settings
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=["en", "tr", "de", "fr", "es"]
TIMEZONE=UTC

# Currency Settings
DEFAULT_CURRENCY=USD
SUPPORTED_CURRENCIES=["USD", "EUR", "TRY", "GBP"]

# =============================================================================
# Custom Configuration
# =============================================================================

# Workshop Integration
WORKSHOP_NAME=Machina Diagnostic Center
WORKSHOP_ADDRESS=123 Main St, City, Country
WORKSHOP_PHONE=******-0123
WORKSHOP_EMAIL=<EMAIL>

# Pricing Configuration
DIAGNOSTIC_FEE=50.00
HOURLY_LABOR_RATE=100.00
MARKUP_PERCENTAGE=20.0

# Customer Configuration
ENABLE_CUSTOMER_PORTAL=true
CUSTOMER_NOTIFICATION_EMAIL=true
CUSTOMER_NOTIFICATION_SMS=false
