"""
Google Gemini AI Integration
Replaces OpenAI with Google Gemini 2.5 Flash model for diagnostic analysis
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import json
import os

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger.warning("Google Generative AI not available. Install with: pip install google-generativeai")

from ..config import settings

logger = logging.getLogger(__name__)


@dataclass
class GeminiResponse:
    """Response from Gemini AI"""
    content: str
    model_used: str
    tokens_used: Optional[int] = None
    cost_estimate: Optional[float] = None
    confidence_score: Optional[float] = None
    processing_time: Optional[float] = None


class GeminiIntegration:
    """
    Google Gemini AI integration for automotive diagnostics
    """
    
    def __init__(self):
        self.client = None
        self.model = None
        self.api_key = None
        self.model_name = "gemini-1.5-flash"
        self.is_initialized = False
        
        # Configuration
        self.generation_config = {
            "temperature": 0.3,
            "top_p": 0.8,
            "top_k": 40,
            "max_output_tokens": 2048,
        }
        
        # Safety settings for automotive diagnostics
        self.safety_settings = [
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            }
        ]
    
    async def initialize(self) -> bool:
        """Initialize Gemini AI client"""
        if not GEMINI_AVAILABLE:
            logger.error("Google Generative AI library not available")
            return False
        
        try:
            # Get API key from environment or settings
            self.api_key = os.getenv('GOOGLE_AI_API_KEY') or settings.google_ai_api_key
            
            if not self.api_key:
                logger.error("Google AI API key not found in environment or settings")
                return False
            
            # Configure the client
            genai.configure(api_key=self.api_key)
            
            # Initialize the model
            self.model = genai.GenerativeModel(
                model_name=self.model_name,
                generation_config=self.generation_config,
                safety_settings=self.safety_settings
            )
            
            # Test the connection
            test_response = await self._test_connection()
            if test_response:
                self.is_initialized = True
                logger.info(f"Gemini AI initialized successfully with model: {self.model_name}")
                return True
            else:
                logger.error("Failed to test Gemini AI connection")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing Gemini AI: {e}")
            return False
    
    async def _test_connection(self) -> bool:
        """Test Gemini AI connection"""
        try:
            response = await asyncio.to_thread(
                self.model.generate_content,
                "Test connection for automotive diagnostics"
            )
            return response and response.text
        except Exception as e:
            logger.error(f"Gemini connection test failed: {e}")
            return False
    
    async def analyze_dtc_codes(
        self,
        dtc_codes: List[str],
        vehicle_info: Dict[str, Any],
        parameters: Optional[List[Dict[str, Any]]] = None,
        brand_specific_info: Optional[Dict[str, Any]] = None
    ) -> GeminiResponse:
        """
        Analyze DTC codes using Gemini AI
        """
        if not self.is_initialized:
            raise RuntimeError("Gemini AI not initialized")
        
        prompt = self._build_diagnostic_prompt(
            dtc_codes, vehicle_info, parameters, brand_specific_info
        )
        
        try:
            start_time = datetime.now()
            
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            if response and response.text:
                return GeminiResponse(
                    content=response.text,
                    model_used=self.model_name,
                    processing_time=processing_time,
                    confidence_score=self._calculate_confidence(response)
                )
            else:
                raise Exception("Empty response from Gemini")
                
        except Exception as e:
            logger.error(f"Error analyzing DTC codes with Gemini: {e}")
            raise
    
    async def explain_live_data(
        self,
        sensor_data: Dict[str, Any],
        vehicle_info: Dict[str, Any],
        context: Optional[str] = None
    ) -> GeminiResponse:
        """
        Explain live sensor data using Gemini AI
        """
        if not self.is_initialized:
            raise RuntimeError("Gemini AI not initialized")
        
        prompt = self._build_live_data_prompt(sensor_data, vehicle_info, context)
        
        try:
            start_time = datetime.now()
            
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            if response and response.text:
                return GeminiResponse(
                    content=response.text,
                    model_used=self.model_name,
                    processing_time=processing_time,
                    confidence_score=self._calculate_confidence(response)
                )
            else:
                raise Exception("Empty response from Gemini")
                
        except Exception as e:
            logger.error(f"Error explaining live data with Gemini: {e}")
            raise
    
    async def generate_repair_recommendations(
        self,
        diagnostic_context: Dict[str, Any]
    ) -> GeminiResponse:
        """
        Generate repair recommendations using Gemini AI
        """
        if not self.is_initialized:
            raise RuntimeError("Gemini AI not initialized")
        
        prompt = self._build_repair_prompt(diagnostic_context)
        
        try:
            start_time = datetime.now()
            
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            if response and response.text:
                return GeminiResponse(
                    content=response.text,
                    model_used=self.model_name,
                    processing_time=processing_time,
                    confidence_score=self._calculate_confidence(response)
                )
            else:
                raise Exception("Empty response from Gemini")
                
        except Exception as e:
            logger.error(f"Error generating repair recommendations with Gemini: {e}")
            raise
    
    def _build_diagnostic_prompt(
        self,
        dtc_codes: List[str],
        vehicle_info: Dict[str, Any],
        parameters: Optional[List[Dict[str, Any]]],
        brand_specific_info: Optional[Dict[str, Any]]
    ) -> str:
        """Build diagnostic analysis prompt"""
        prompt = f"""
You are an expert automotive diagnostic technician with deep knowledge of OBD2 systems and vehicle diagnostics.

Vehicle Information:
- Make: {vehicle_info.get('make', 'Unknown')}
- Model: {vehicle_info.get('model', 'Unknown')}
- Year: {vehicle_info.get('year', 'Unknown')}
- Engine: {vehicle_info.get('engine_type', 'Unknown')}
- Mileage: {vehicle_info.get('mileage', 'Unknown')}

DTC Codes Found:
{', '.join(dtc_codes)}

"""
        
        if parameters:
            prompt += "\nLive Parameters:\n"
            for param in parameters:
                prompt += f"- {param.get('name', 'Unknown')}: {param.get('value', 'N/A')} {param.get('unit', '')}\n"
        
        if brand_specific_info:
            prompt += f"\nBrand-Specific Information:\n{json.dumps(brand_specific_info, indent=2)}\n"
        
        prompt += """
Please provide a comprehensive diagnostic analysis including:

1. **DTC Code Analysis**: Explain each code in detail
2. **Root Cause Analysis**: Identify the most likely causes
3. **System Impact**: How these issues affect vehicle performance
4. **Diagnostic Steps**: Recommended testing procedures
5. **Repair Recommendations**: Prioritized repair actions
6. **Cost Estimates**: Approximate repair costs (parts + labor)
7. **Safety Considerations**: Any immediate safety concerns
8. **Prevention**: How to prevent similar issues

Format your response in clear sections with actionable recommendations.
"""
        
        return prompt
    
    def _build_live_data_prompt(
        self,
        sensor_data: Dict[str, Any],
        vehicle_info: Dict[str, Any],
        context: Optional[str]
    ) -> str:
        """Build live data analysis prompt"""
        prompt = f"""
You are an expert automotive technician analyzing live sensor data from a vehicle.

Vehicle Information:
- Make: {vehicle_info.get('make', 'Unknown')}
- Model: {vehicle_info.get('model', 'Unknown')}
- Year: {vehicle_info.get('year', 'Unknown')}

Current Sensor Readings:
"""
        
        for sensor, data in sensor_data.items():
            prompt += f"- {sensor}: {data.get('value', 'N/A')} {data.get('unit', '')}\n"
        
        if context:
            prompt += f"\nContext: {context}\n"
        
        prompt += """
Please analyze these live sensor readings and provide:

1. **Normal Range Assessment**: Are values within normal ranges?
2. **Performance Analysis**: What do these readings indicate about engine performance?
3. **Fuel Mixture Analysis**: Rich/lean mixture conditions
4. **Engine Load Analysis**: Current engine stress and efficiency
5. **Emission System Status**: How emissions systems are performing
6. **Trends and Patterns**: Any concerning trends in the data
7. **Recommendations**: Immediate actions or monitoring suggestions

Focus on practical insights that help understand current vehicle condition.
"""
        
        return prompt
    
    def _build_repair_prompt(self, diagnostic_context: Dict[str, Any]) -> str:
        """Build repair recommendation prompt"""
        prompt = f"""
You are an expert automotive repair advisor providing detailed repair recommendations.

Diagnostic Context:
{json.dumps(diagnostic_context, indent=2)}

Please provide detailed repair recommendations including:

1. **Priority Ranking**: Order repairs by urgency and importance
2. **Detailed Procedures**: Step-by-step repair instructions
3. **Parts Required**: Specific part numbers and specifications
4. **Tools Needed**: Required tools and equipment
5. **Labor Time**: Estimated time for each repair
6. **Cost Breakdown**: Parts cost + labor cost estimates
7. **Difficulty Level**: Skill level required (DIY, Professional, Specialist)
8. **Safety Precautions**: Important safety considerations
9. **Testing Procedures**: How to verify successful repair
10. **Warranty Information**: Typical warranty periods for parts/labor

Provide practical, actionable guidance for both DIY enthusiasts and professional technicians.
"""
        
        return prompt
    
    def _calculate_confidence(self, response) -> float:
        """Calculate confidence score for Gemini response"""
        # Basic confidence calculation based on response characteristics
        if not response or not response.text:
            return 0.0
        
        text_length = len(response.text)
        
        # Longer, more detailed responses generally indicate higher confidence
        if text_length > 1000:
            return 0.9
        elif text_length > 500:
            return 0.8
        elif text_length > 200:
            return 0.7
        else:
            return 0.6
    
    async def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        return {
            'model_name': self.model_name,
            'is_initialized': self.is_initialized,
            'api_key_configured': bool(self.api_key),
            'generation_config': self.generation_config
        }


# Global instance
gemini_integration = GeminiIntegration()
