"""
CAN Dump Parser

Parses CAN dump files (candump format) and extracts diagnostic information.

Source Attribution:
- CAN dump format parsing based on linux-can-utils candump format
- Message interpretation inspired by OpenDBC project structures
- Nissan Leaf specific knowledge from open-source EV diagnostic tools
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import json

logger = logging.getLogger(__name__)


@dataclass
class CANMessage:
    """Represents a single CAN message from dump file"""
    timestamp: float
    interface: str
    can_id: str
    data: str
    dlc: int
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'timestamp': self.timestamp,
            'interface': self.interface,
            'can_id': self.can_id,
            'data': self.data,
            'dlc': self.dlc
        }


@dataclass
class OBDResponse:
    """Simulated OBD response from CAN data"""
    pid: str
    value: Any
    unit: str
    timestamp: float
    raw_data: str


class CANDumpParser:
    """
    Parser for CAN dump files in candump format
    
    Supports parsing of:
    - Standard candump format: (timestamp) interface can_id#data
    - Extraction of OBD2-relevant messages
    - Conversion to mock OBD responses
    """
    
    def __init__(self):
        # CAN ID patterns for OBD2 communication
        self.obd2_request_ids = {
            '7DF',  # Functional addressing
            '7E0', '7E1', '7E2', '7E3', '7E4', '7E5', '7E6', '7E7'  # Physical addressing
        }
        
        self.obd2_response_ids = {
            '7E8', '7E9', '7EA', '7EB', '7EC', '7ED', '7EE', '7EF'  # Response IDs
        }
        
        # Nissan Leaf specific CAN IDs (from analysis of dump file)
        self.nissan_leaf_ids = {
            '1CA': 'Battery Management',
            '1CB': 'Battery Status',
            '1D5': 'Vehicle Speed',
            '180': 'Motor Data',
            '245': 'Power Electronics',
            '292': 'Charging System',
            '5C5': 'Climate Control',
            '54A': 'Battery Temperature',
            '54B': 'Battery Voltage',
            '5FA': 'Energy Management',
            '5FB': 'Regeneration',
            '5FC': 'Power Distribution'
        }
        
        # Standard OBD2 PID mappings
        self.pid_mappings = {
            '01': 'Monitor status since DTCs cleared',
            '02': 'Freeze DTC',
            '03': 'Fuel system status',
            '04': 'Calculated engine load',
            '05': 'Engine coolant temperature',
            '06': 'Short term fuel trim—Bank 1',
            '07': 'Long term fuel trim—Bank 1',
            '0C': 'Engine RPM',
            '0D': 'Vehicle speed',
            '0E': 'Timing advance',
            '0F': 'Intake air temperature',
            '10': 'MAF air flow rate',
            '11': 'Throttle position',
            '14': 'Oxygen sensor 1',
            '15': 'Oxygen sensor 2',
            '1F': 'Run time since engine start',
            '21': 'Distance traveled with malfunction indicator lamp on',
            '2F': 'Fuel tank level input',
            '33': 'Absolute barometric pressure',
            '42': 'Control module voltage',
            '43': 'Absolute load value',
            '44': 'Fuel–Air commanded equivalence ratio',
            '45': 'Relative throttle position',
            '46': 'Ambient air temperature',
            '47': 'Absolute throttle position B',
            '49': 'Accelerator pedal position D',
            '4A': 'Accelerator pedal position E',
            '4C': 'Commanded throttle actuator',
            '51': 'Fuel Type',
            '52': 'Ethanol fuel %'
        }
    
    def parse_candump_file(self, file_path: str) -> List[CANMessage]:
        """
        Parse a candump format file
        
        Format: (timestamp) interface can_id#data
        Example: (1579876676.199507) slcan0 2DE#0000000000000050
        """
        messages = []
        
        try:
            with open(file_path, 'r') as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    try:
                        message = self._parse_candump_line(line)
                        if message:
                            messages.append(message)
                    except Exception as e:
                        logger.warning(f"Error parsing line {line_num}: {line} - {e}")
                        continue
        
        except FileNotFoundError:
            logger.error(f"CAN dump file not found: {file_path}")
            return []
        except Exception as e:
            logger.error(f"Error reading CAN dump file: {e}")
            return []
        
        logger.info(f"Parsed {len(messages)} CAN messages from {file_path}")
        return messages
    
    def _parse_candump_line(self, line: str) -> Optional[CANMessage]:
        """Parse a single line from candump format"""
        # Regex pattern for candump format
        pattern = r'\((\d+\.\d+)\)\s+(\w+)\s+([0-9A-Fa-f]+)#([0-9A-Fa-f]*)'
        match = re.match(pattern, line)
        
        if not match:
            return None
        
        timestamp, interface, can_id, data = match.groups()
        
        return CANMessage(
            timestamp=float(timestamp),
            interface=interface,
            can_id=can_id.upper(),
            data=data.upper(),
            dlc=len(data) // 2 if data else 0
        )
    
    def extract_obd_responses(self, messages: List[CANMessage]) -> List[OBDResponse]:
        """Extract OBD2-like responses from CAN messages"""
        obd_responses = []
        
        for message in messages:
            # Check if this is an OBD2 response ID
            if message.can_id in self.obd2_response_ids:
                response = self._decode_obd_response(message)
                if response:
                    obd_responses.append(response)
            
            # Also check Nissan Leaf specific IDs that can provide OBD-like data
            elif message.can_id in self.nissan_leaf_ids:
                response = self._decode_nissan_leaf_message(message)
                if response:
                    obd_responses.append(response)
        
        return obd_responses
    
    def _decode_obd_response(self, message: CANMessage) -> Optional[OBDResponse]:
        """Decode standard OBD2 response message"""
        if len(message.data) < 6:  # Minimum OBD2 response length
            return None
        
        try:
            # OBD2 response format: [length][mode+0x40][PID][data...]
            length = int(message.data[0:2], 16)
            mode = int(message.data[2:4], 16)
            pid = message.data[4:6]
            data = message.data[6:]
            
            # Check if this is a valid OBD2 response (mode 01 response = 0x41)
            if mode == 0x41 and pid in self.pid_mappings:
                value = self._calculate_pid_value(pid, data)
                return OBDResponse(
                    pid=f"0x{pid}",
                    value=value['value'],
                    unit=value['unit'],
                    timestamp=message.timestamp,
                    raw_data=message.data
                )
        
        except (ValueError, IndexError) as e:
            logger.debug(f"Error decoding OBD response: {e}")
        
        return None
    
    def _decode_nissan_leaf_message(self, message: CANMessage) -> Optional[OBDResponse]:
        """Decode Nissan Leaf specific CAN messages to OBD-like responses"""
        can_id = message.can_id
        data = message.data
        
        try:
            if can_id == '1D5':  # Vehicle speed
                if len(data) >= 10:
                    # Extract speed from bytes (this is vehicle-specific)
                    speed_raw = int(data[8:10], 16)
                    speed_kmh = speed_raw * 0.5  # Conversion factor for Nissan Leaf
                    return OBDResponse(
                        pid='0x0D',  # Standard vehicle speed PID
                        value=speed_kmh,
                        unit='km/h',
                        timestamp=message.timestamp,
                        raw_data=data
                    )
            
            elif can_id == '54A':  # Battery temperature
                if len(data) >= 16:
                    temp_raw = int(data[14:16], 16)
                    temp_c = temp_raw - 40  # Temperature conversion
                    return OBDResponse(
                        pid='0xEV_BATT_TEMP',  # Custom EV PID
                        value=temp_c,
                        unit='°C',
                        timestamp=message.timestamp,
                        raw_data=data
                    )
            
            elif can_id == '54B':  # Battery voltage
                if len(data) >= 8:
                    voltage_raw = int(data[4:8], 16)
                    voltage_v = voltage_raw * 0.1  # Voltage conversion
                    return OBDResponse(
                        pid='0xEV_BATT_VOLT',  # Custom EV PID
                        value=voltage_v,
                        unit='V',
                        timestamp=message.timestamp,
                        raw_data=data
                    )
        
        except (ValueError, IndexError) as e:
            logger.debug(f"Error decoding Nissan Leaf message {can_id}: {e}")
        
        return None
    
    def _calculate_pid_value(self, pid: str, data: str) -> Dict[str, Any]:
        """Calculate actual value from PID data"""
        try:
            if pid == '0C':  # Engine RPM
                if len(data) >= 4:
                    a = int(data[0:2], 16)
                    b = int(data[2:4], 16)
                    rpm = ((a * 256) + b) / 4
                    return {'value': rpm, 'unit': 'rpm'}
            
            elif pid == '0D':  # Vehicle speed
                if len(data) >= 2:
                    speed = int(data[0:2], 16)
                    return {'value': speed, 'unit': 'km/h'}
            
            elif pid == '05':  # Engine coolant temperature
                if len(data) >= 2:
                    temp = int(data[0:2], 16) - 40
                    return {'value': temp, 'unit': '°C'}
            
            elif pid == '10':  # MAF air flow rate
                if len(data) >= 4:
                    a = int(data[0:2], 16)
                    b = int(data[2:4], 16)
                    maf = ((a * 256) + b) / 100
                    return {'value': maf, 'unit': 'g/s'}
            
            # Add more PID calculations as needed
            
        except (ValueError, IndexError):
            pass
        
        # Default: return raw hex value
        return {'value': data, 'unit': 'hex'}
    
    def get_statistics(self, messages: List[CANMessage]) -> Dict[str, Any]:
        """Get statistics about parsed CAN messages"""
        if not messages:
            return {}
        
        can_id_counts = {}
        for message in messages:
            can_id_counts[message.can_id] = can_id_counts.get(message.can_id, 0) + 1
        
        return {
            'total_messages': len(messages),
            'unique_can_ids': len(can_id_counts),
            'time_span': messages[-1].timestamp - messages[0].timestamp if messages else 0,
            'most_frequent_ids': sorted(can_id_counts.items(), key=lambda x: x[1], reverse=True)[:10],
            'obd2_messages': sum(1 for msg in messages if msg.can_id in self.obd2_response_ids),
            'nissan_leaf_messages': sum(1 for msg in messages if msg.can_id in self.nissan_leaf_ids)
        }
