"""
Mock OBD Connection using CAN Dump Data

Provides a mock OBD2 connection that uses parsed CAN dump data
to simulate real vehicle responses for testing purposes.

Source Attribution:
- Mock connection patterns inspired by python-OBD library structure
- CAN message simulation based on real vehicle data analysis
"""

import logging
import time
import random
from typing import Dict, List, Optional, Any, Iterator
from dataclasses import dataclass
from threading import Lock

from .can_parser import CANDumpParser, CANMessage, OBDResponse

logger = logging.getLogger(__name__)


@dataclass
class MockOBDCommand:
    """Represents an OBD command for mock responses"""
    pid: str
    name: str
    description: str
    unit: str
    formula: str


class MockOBDConnection:
    """
    Mock OBD2 connection that uses CAN dump data to simulate vehicle responses
    
    This class provides the same interface as a real OBD connection but uses
    pre-recorded CAN dump data to generate realistic responses for testing.
    """
    
    def __init__(self, can_dump_file: str = None):
        self.is_connected = False
        self.can_dump_file = can_dump_file
        self.can_messages: List[CANMessage] = []
        self.obd_responses: List[OBDResponse] = []
        self.current_index = 0
        self.parser = CANDumpParser()
        self._lock = Lock()
        
        # Supported OBD commands (mock)
        self.supported_commands = {
            '01': MockOBDCommand('01', 'Monitor Status', 'DTC Status', 'bitmap', 'raw'),
            '03': MockOBDCommand('03', 'Get DTCs', 'Stored DTCs', 'codes', 'raw'),
            '04': MockOBDCommand('04', 'Clear DTCs', 'Clear fault codes', 'none', 'raw'),
            '0C': MockOBDCommand('0C', 'Engine RPM', 'Engine speed', 'rpm', '((A*256)+B)/4'),
            '0D': MockOBDCommand('0D', 'Vehicle Speed', 'Vehicle speed', 'km/h', 'A'),
            '05': MockOBDCommand('05', 'Coolant Temp', 'Engine coolant temperature', '°C', 'A-40'),
            '0F': MockOBDCommand('0F', 'Intake Temp', 'Intake air temperature', '°C', 'A-40'),
            '10': MockOBDCommand('10', 'MAF Rate', 'Mass air flow rate', 'g/s', '((A*256)+B)/100'),
            '11': MockOBDCommand('11', 'Throttle Pos', 'Throttle position', '%', 'A*100/255'),
            '1F': MockOBDCommand('1F', 'Runtime', 'Run time since start', 's', '(A*256)+B'),
            '21': MockOBDCommand('21', 'MIL Distance', 'Distance with MIL on', 'km', '(A*256)+B'),
            '2F': MockOBDCommand('2F', 'Fuel Level', 'Fuel tank level', '%', 'A*100/255'),
            '42': MockOBDCommand('42', 'Control Voltage', 'Control module voltage', 'V', '((A*256)+B)/1000'),
            '46': MockOBDCommand('46', 'Ambient Temp', 'Ambient air temperature', '°C', 'A-40'),
        }
        
        # Mock DTCs for testing
        self.mock_dtcs = [
            'P0171',  # System too lean
            'P0300',  # Random misfire
            'P0420',  # Catalyst efficiency
        ]
        
        # Load CAN dump data if provided
        if can_dump_file:
            self.load_can_dump(can_dump_file)
    
    def load_can_dump(self, file_path: str) -> bool:
        """Load CAN dump data from file"""
        try:
            self.can_messages = self.parser.parse_candump_file(file_path)
            self.obd_responses = self.parser.extract_obd_responses(self.can_messages)
            
            if self.can_messages:
                logger.info(f"Loaded {len(self.can_messages)} CAN messages from {file_path}")
                logger.info(f"Extracted {len(self.obd_responses)} OBD responses")
                return True
            else:
                logger.warning(f"No CAN messages found in {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error loading CAN dump: {e}")
            return False
    
    def connect(self, port: str = None, baudrate: int = 38400, timeout: float = 30.0) -> bool:
        """Mock connection to vehicle"""
        logger.info(f"Mock OBD connecting to {port or 'mock_port'}")
        
        # Simulate connection delay
        time.sleep(0.5)
        
        if self.can_messages:
            self.is_connected = True
            logger.info("Mock OBD connection established using CAN dump data")
            return True
        else:
            # Create some default mock data if no CAN dump available
            self._create_default_mock_data()
            self.is_connected = True
            logger.info("Mock OBD connection established using default data")
            return True
    
    def disconnect(self) -> bool:
        """Disconnect from mock vehicle"""
        self.is_connected = False
        logger.info("Mock OBD connection closed")
        return True
    
    def is_connected_status(self) -> bool:
        """Check if mock connection is active"""
        return self.is_connected
    
    def query_command(self, command: str) -> Optional[Any]:
        """Query a specific OBD command"""
        if not self.is_connected:
            logger.warning("Mock OBD not connected")
            return None
        
        with self._lock:
            if command in self.supported_commands:
                return self._get_mock_response(command)
            else:
                logger.warning(f"Unsupported mock command: {command}")
                return None
    
    def get_dtcs(self) -> List[str]:
        """Get stored DTCs from mock vehicle"""
        if not self.is_connected:
            return []
        
        # Return some mock DTCs for testing
        return self.mock_dtcs.copy()
    
    def clear_dtcs(self) -> bool:
        """Clear DTCs from mock vehicle"""
        if not self.is_connected:
            return False
        
        logger.info("Mock DTCs cleared")
        self.mock_dtcs.clear()
        return True
    
    def get_supported_pids(self) -> List[str]:
        """Get list of supported PIDs"""
        return list(self.supported_commands.keys())
    
    def _get_mock_response(self, command: str) -> Any:
        """Generate mock response for OBD command"""
        cmd_info = self.supported_commands.get(command)
        if not cmd_info:
            return None
        
        # Try to get response from CAN dump data first
        if self.obd_responses:
            # Find matching PID in OBD responses
            pid_hex = f"0x{command}"
            matching_responses = [r for r in self.obd_responses if r.pid == pid_hex]
            
            if matching_responses:
                # Return a response from the dump data
                response = random.choice(matching_responses)
                return {
                    'value': response.value,
                    'unit': response.unit,
                    'timestamp': time.time(),
                    'source': 'can_dump'
                }
        
        # Generate synthetic response if no dump data available
        return self._generate_synthetic_response(command)
    
    def _generate_synthetic_response(self, command: str) -> Dict[str, Any]:
        """Generate synthetic response for testing"""
        synthetic_values = {
            '0C': random.randint(800, 3000),      # RPM
            '0D': random.randint(0, 120),         # Speed km/h
            '05': random.randint(80, 95),         # Coolant temp °C
            '0F': random.randint(15, 35),         # Intake temp °C
            '10': random.uniform(2.0, 15.0),      # MAF g/s
            '11': random.randint(0, 100),         # Throttle %
            '1F': random.randint(0, 3600),        # Runtime seconds
            '21': random.randint(0, 1000),        # MIL distance km
            '2F': random.randint(10, 100),        # Fuel level %
            '42': random.uniform(12.0, 14.5),     # Control voltage V
            '46': random.randint(-10, 40),        # Ambient temp °C
        }
        
        cmd_info = self.supported_commands[command]
        value = synthetic_values.get(command, 0)
        
        return {
            'value': value,
            'unit': cmd_info.unit,
            'timestamp': time.time(),
            'source': 'synthetic'
        }
    
    def _create_default_mock_data(self):
        """Create default mock data when no CAN dump is available"""
        # Create some synthetic OBD responses for testing
        current_time = time.time()
        
        self.obd_responses = [
            OBDResponse('0x0C', 1500, 'rpm', current_time, '410C0BB8'),
            OBDResponse('0x0D', 60, 'km/h', current_time, '410D3C'),
            OBDResponse('0x05', 85, '°C', current_time, '41057D'),
            OBDResponse('0x0F', 25, '°C', current_time, '410F41'),
            OBDResponse('0x10', 8.5, 'g/s', current_time, '41100355'),
        ]
        
        logger.info("Created default mock OBD data")
    
    def get_live_data_stream(self) -> Iterator[Dict[str, Any]]:
        """Get continuous stream of live data from mock vehicle"""
        if not self.is_connected:
            return
        
        while self.is_connected:
            # Simulate live data by cycling through available responses
            if self.obd_responses:
                with self._lock:
                    response = self.obd_responses[self.current_index % len(self.obd_responses)]
                    self.current_index += 1
                    
                    yield {
                        'pid': response.pid,
                        'value': response.value,
                        'unit': response.unit,
                        'timestamp': time.time(),
                        'source': 'can_dump_stream'
                    }
            else:
                # Generate synthetic live data
                for pid in ['0C', '0D', '05', '10']:
                    if pid in self.supported_commands:
                        response = self._generate_synthetic_response(pid)
                        response['pid'] = f"0x{pid}"
                        yield response
            
            time.sleep(0.1)  # 10Hz update rate
    
    def get_vehicle_info(self) -> Dict[str, str]:
        """Get mock vehicle information"""
        return {
            'vin': 'MOCK1234567890123',
            'make': 'Nissan',
            'model': 'Leaf',
            'year': '2020',
            'engine': 'Electric Motor',
            'fuel_type': 'Electric',
            'protocol': 'CAN (Mock)',
            'data_source': 'CAN Dump' if self.can_messages else 'Synthetic'
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about mock connection and data"""
        stats = {
            'connected': self.is_connected,
            'can_messages_loaded': len(self.can_messages),
            'obd_responses_available': len(self.obd_responses),
            'supported_commands': len(self.supported_commands),
            'mock_dtcs': len(self.mock_dtcs)
        }
        
        if self.can_messages:
            can_stats = self.parser.get_statistics(self.can_messages)
            stats.update(can_stats)
        
        return stats
