"""
Database Migration Manager

Handles migration of extracted diagnostic data to SQLite database.

Source Attribution:
- Database schema design based on automotive diagnostic standards
- Migration patterns inspired by SQLAlchemy best practices
- Data normalization following automotive industry standards
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.exc import IntegrityError

from .database import db_manager
from .models import (
    DTCCode, VehicleProfile, DiagnosticSession, 
    MaintenanceRecord, SystemConfiguration
)
from ..reference_integration.opendbc_extractor import ExtractedDiagnosticData
from ..reference_integration.reference_extractor import ExtractedDTC, ExtractedPID

logger = logging.getLogger(__name__)


class MigrationManager:
    """
    Manages migration of extracted diagnostic data to SQLite database
    
    Handles:
    - DTC code migration from reference projects
    - PID mapping migration from OpenDBC
    - Brand-specific diagnostic data
    - Data deduplication and normalization
    """
    
    def __init__(self):
        self.db = db_manager
        self.migration_stats = {
            'dtcs_inserted': 0,
            'dtcs_updated': 0,
            'dtcs_skipped': 0,
            'pids_inserted': 0,
            'errors': 0
        }
    
    def migrate_extracted_data(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Migrate all extracted diagnostic data to database
        
        Args:
            extracted_data: Dictionary containing DTCs, PIDs, and procedures
            
        Returns:
            Migration statistics and results
        """
        logger.info("Starting database migration of extracted data")
        
        try:
            # Migrate DTCs
            if 'dtcs' in extracted_data:
                self._migrate_dtcs(extracted_data['dtcs'])
            
            # Migrate PIDs (stored as system configurations)
            if 'pids' in extracted_data:
                self._migrate_pids(extracted_data['pids'])
            
            # Migrate procedures (stored as system configurations)
            if 'procedures' in extracted_data:
                self._migrate_procedures(extracted_data['procedures'])
            
            logger.info(f"Migration completed: {self.migration_stats}")
            return {
                'success': True,
                'stats': self.migration_stats,
                'message': 'Data migration completed successfully'
            }
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            self.migration_stats['errors'] += 1
            return {
                'success': False,
                'stats': self.migration_stats,
                'error': str(e)
            }
    
    def migrate_opendbc_data(self, opendbc_data: Dict[str, ExtractedDiagnosticData]) -> Dict[str, Any]:
        """
        Migrate OpenDBC extracted data to database
        
        Args:
            opendbc_data: Dictionary of brand -> ExtractedDiagnosticData
            
        Returns:
            Migration results
        """
        logger.info("Migrating OpenDBC data to database")
        
        try:
            for brand, data in opendbc_data.items():
                self._migrate_brand_data(brand, data)
            
            return {
                'success': True,
                'brands_migrated': len(opendbc_data),
                'stats': self.migration_stats
            }
            
        except Exception as e:
            logger.error(f"OpenDBC migration failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _migrate_dtcs(self, dtcs_data: List[Dict[str, Any]]):
        """Migrate DTC codes to database"""
        logger.info(f"Migrating {len(dtcs_data)} DTC codes")
        
        with self.db.get_session() as session:
            for dtc_data in dtcs_data:
                try:
                    # Check if DTC already exists
                    existing_dtc = session.query(DTCCode).filter(
                        DTCCode.code == dtc_data['code']
                    ).first()
                    
                    if existing_dtc:
                        # Update existing DTC with new information
                        self._update_existing_dtc(existing_dtc, dtc_data)
                        self.migration_stats['dtcs_updated'] += 1
                    else:
                        # Create new DTC
                        new_dtc = self._create_dtc_from_data(dtc_data)
                        session.add(new_dtc)
                        self.migration_stats['dtcs_inserted'] += 1
                    
                except IntegrityError as e:
                    logger.warning(f"Integrity error for DTC {dtc_data.get('code', 'unknown')}: {e}")
                    session.rollback()
                    self.migration_stats['dtcs_skipped'] += 1
                    continue
                except Exception as e:
                    logger.warning(f"Error migrating DTC {dtc_data.get('code', 'unknown')}: {e}")
                    self.migration_stats['errors'] += 1
                    continue
            
            session.commit()
    
    def _migrate_pids(self, pids_data: List[Dict[str, Any]]):
        """Migrate PID data as system configurations"""
        logger.info(f"Migrating {len(pids_data)} PID definitions")
        
        with self.db.get_session() as session:
            for pid_data in pids_data:
                try:
                    # Store PID as system configuration
                    config_key = f"pid_{pid_data['pid'].replace('0x', '').lower()}"
                    
                    existing_config = session.query(SystemConfiguration).filter(
                        SystemConfiguration.key == config_key
                    ).first()
                    
                    if not existing_config:
                        pid_config = SystemConfiguration(
                            key=config_key,
                            value=json.dumps({
                                'name': pid_data['name'],
                                'description': pid_data['description'],
                                'unit': pid_data['unit'],
                                'formula': pid_data['formula'],
                                'brand': pid_data['brand'],
                                'source': pid_data['source_project']
                            }),
                            category='pid_definitions',
                            description=f"PID {pid_data['pid']} - {pid_data['name']}"
                        )
                        session.add(pid_config)
                        self.migration_stats['pids_inserted'] += 1
                
                except Exception as e:
                    logger.warning(f"Error migrating PID {pid_data.get('pid', 'unknown')}: {e}")
                    self.migration_stats['errors'] += 1
                    continue
            
            session.commit()
    
    def _migrate_procedures(self, procedures_data: List[Dict[str, Any]]):
        """Migrate diagnostic procedures as system configurations"""
        logger.info(f"Migrating {len(procedures_data)} diagnostic procedures")
        
        with self.db.get_session() as session:
            for proc_data in procedures_data:
                try:
                    config_key = f"procedure_{proc_data['name'].lower().replace(' ', '_')}"
                    
                    existing_config = session.query(SystemConfiguration).filter(
                        SystemConfiguration.key == config_key
                    ).first()
                    
                    if not existing_config:
                        proc_config = SystemConfiguration(
                            key=config_key,
                            value=json.dumps({
                                'name': proc_data['name'],
                                'description': proc_data['description'],
                                'steps': proc_data['steps'],
                                'brand': proc_data['brand'],
                                'system': proc_data['system'],
                                'source': proc_data['source_project'],
                                'required_tools': proc_data.get('required_tools', [])
                            }),
                            category='diagnostic_procedures',
                            description=f"Diagnostic procedure: {proc_data['name']}"
                        )
                        session.add(proc_config)
                
                except Exception as e:
                    logger.warning(f"Error migrating procedure {proc_data.get('name', 'unknown')}: {e}")
                    self.migration_stats['errors'] += 1
                    continue
            
            session.commit()
    
    def _migrate_brand_data(self, brand: str, data: ExtractedDiagnosticData):
        """Migrate brand-specific data from OpenDBC"""
        logger.info(f"Migrating {brand} data from OpenDBC")
        
        # Migrate diagnostic PIDs
        for pid_key, pid_info in data.diagnostic_pids.items():
            pid_data = {
                'pid': pid_key,
                'name': pid_info['name'],
                'description': pid_info['description'],
                'unit': pid_info['unit'],
                'formula': pid_info['formula'],
                'brand': brand,
                'source_project': 'opendbc'
            }
            self._migrate_pids([pid_data])
        
        # Migrate DTC mappings
        for dtc_code, description in data.dtc_mappings.items():
            dtc_data = {
                'code': dtc_code,
                'description': description,
                'system': 'Unknown',
                'severity': 'medium',
                'brand': brand,
                'possible_causes': [],
                'repair_hints': [],
                'source_project': 'opendbc'
            }
            self._migrate_dtcs([dtc_data])
        
        # Store ECU addresses as system configuration
        if data.ecu_addresses:
            self._store_ecu_addresses(brand, data.ecu_addresses)
    
    def _create_dtc_from_data(self, dtc_data: Dict[str, Any]) -> DTCCode:
        """Create DTCCode instance from extracted data"""
        # Determine category from DTC code prefix
        code = dtc_data['code']
        if code.startswith('P'):
            category = 'Powertrain'
        elif code.startswith('B'):
            category = 'Body'
        elif code.startswith('C'):
            category = 'Chassis'
        elif code.startswith('U'):
            category = 'Network'
        else:
            category = 'Unknown'
        
        return DTCCode(
            code=code,
            description=dtc_data.get('description', ''),
            category=category,
            severity=dtc_data.get('severity', 'medium'),
            system=dtc_data.get('system', 'Unknown'),
            possible_causes=dtc_data.get('possible_causes', []),
            repair_hints=dtc_data.get('repair_hints', []),
            related_pids=[],
            freeze_frame_required=dtc_data.get('freeze_frame_required', False),
            obd2_standard=not dtc_data.get('brand', '').lower() in ['renault', 'bmw', 'vag'],
            brand_specific_info={
                'brand': dtc_data.get('brand', ''),
                'source_project': dtc_data.get('source_project', ''),
                'ecu_address': dtc_data.get('ecu_address', '')
            }
        )
    
    def _update_existing_dtc(self, existing_dtc: DTCCode, new_data: Dict[str, Any]):
        """Update existing DTC with new information"""
        # Merge possible causes and repair hints
        if new_data.get('possible_causes'):
            existing_causes = existing_dtc.possible_causes or []
            new_causes = new_data['possible_causes']
            existing_dtc.possible_causes = list(set(existing_causes + new_causes))
        
        if new_data.get('repair_hints'):
            existing_hints = existing_dtc.repair_hints or []
            new_hints = new_data['repair_hints']
            existing_dtc.repair_hints = list(set(existing_hints + new_hints))
        
        # Update brand-specific info
        if existing_dtc.brand_specific_info is None:
            existing_dtc.brand_specific_info = {}
        
        brand = new_data.get('brand', '')
        if brand and brand not in str(existing_dtc.brand_specific_info):
            existing_dtc.brand_specific_info[f"{brand}_info"] = {
                'source_project': new_data.get('source_project', ''),
                'ecu_address': new_data.get('ecu_address', '')
            }
        
        existing_dtc.updated_at = datetime.utcnow()
    
    def _store_ecu_addresses(self, brand: str, ecu_addresses: Dict[str, str]):
        """Store ECU addresses as system configuration"""
        with self.db.get_session() as session:
            config_key = f"ecu_addresses_{brand.lower()}"
            
            existing_config = session.query(SystemConfiguration).filter(
                SystemConfiguration.key == config_key
            ).first()
            
            if not existing_config:
                ecu_config = SystemConfiguration(
                    key=config_key,
                    value=json.dumps(ecu_addresses),
                    category='ecu_addresses',
                    description=f"ECU addresses for {brand}"
                )
                session.add(ecu_config)
                session.commit()
    
    def get_migration_statistics(self) -> Dict[str, Any]:
        """Get current migration statistics"""
        return self.migration_stats.copy()
    
    def reset_statistics(self):
        """Reset migration statistics"""
        self.migration_stats = {
            'dtcs_inserted': 0,
            'dtcs_updated': 0,
            'dtcs_skipped': 0,
            'pids_inserted': 0,
            'errors': 0
        }
    
    def verify_migration(self) -> Dict[str, Any]:
        """Verify migration results"""
        with self.db.get_session() as session:
            total_dtcs = session.query(DTCCode).count()
            total_configs = session.query(SystemConfiguration).count()
            
            # Count by category
            pid_configs = session.query(SystemConfiguration).filter(
                SystemConfiguration.category == 'pid_definitions'
            ).count()
            
            procedure_configs = session.query(SystemConfiguration).filter(
                SystemConfiguration.category == 'diagnostic_procedures'
            ).count()
            
            ecu_configs = session.query(SystemConfiguration).filter(
                SystemConfiguration.category == 'ecu_addresses'
            ).count()
            
            return {
                'total_dtcs': total_dtcs,
                'total_configurations': total_configs,
                'pid_definitions': pid_configs,
                'diagnostic_procedures': procedure_configs,
                'ecu_addresses': ecu_configs,
                'migration_stats': self.migration_stats
            }


# Global instance
migration_manager = MigrationManager()
