"""
Enhanced Live Data Monitor
Comprehensive sensor monitoring with fuel mixture, engine load, and consumption tracking
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time
import statistics

from ..obd_interface.obd_reader import OBDReader
from ..ai_engine.gemini_integration import gemini_integration

logger = logging.getLogger(__name__)


class SensorType(Enum):
    """Enhanced sensor types for comprehensive monitoring"""
    # Engine parameters
    ENGINE_RPM = "engine_rpm"
    ENGINE_LOAD = "engine_load"
    ENGINE_COOLANT_TEMP = "engine_coolant_temp"
    INTAKE_AIR_TEMP = "intake_air_temp"
    
    # Fuel system
    FUEL_PRESSURE = "fuel_pressure"
    FUEL_TRIM_SHORT_BANK1 = "fuel_trim_short_bank1"
    FUEL_TRIM_LONG_BANK1 = "fuel_trim_long_bank1"
    FUEL_TRIM_SHORT_BANK2 = "fuel_trim_short_bank2"
    FUEL_TRIM_LONG_BANK2 = "fuel_trim_long_bank2"
    FUEL_CONSUMPTION_RATE = "fuel_consumption_rate"
    
    # Air/fuel mixture
    MAF_FLOW_RATE = "maf_flow_rate"
    THROTTLE_POSITION = "throttle_position"
    OXYGEN_SENSOR_BANK1 = "oxygen_sensor_bank1"
    OXYGEN_SENSOR_BANK2 = "oxygen_sensor_bank2"
    
    # Vehicle dynamics
    VEHICLE_SPEED = "vehicle_speed"
    ACCELERATION = "acceleration"
    TIMING_ADVANCE = "timing_advance"
    
    # Emission system
    CATALYST_TEMP_BANK1 = "catalyst_temp_bank1"
    CATALYST_TEMP_BANK2 = "catalyst_temp_bank2"
    EGR_POSITION = "egr_position"
    
    # Environmental
    AMBIENT_AIR_TEMP = "ambient_air_temp"
    BAROMETRIC_PRESSURE = "barometric_pressure"
    
    # Calculated values
    FUEL_ECONOMY_INSTANT = "fuel_economy_instant"
    FUEL_ECONOMY_AVERAGE = "fuel_economy_average"
    MIXTURE_RATIO = "mixture_ratio"
    ENGINE_EFFICIENCY = "engine_efficiency"


@dataclass
class SensorReading:
    """Enhanced sensor reading with analysis"""
    sensor_type: SensorType
    value: float
    unit: str
    timestamp: datetime
    quality: float = 1.0
    status: str = "normal"  # normal, warning, critical
    trend: str = "stable"   # increasing, decreasing, stable
    analysis: Optional[str] = None


@dataclass
class FuelMixtureAnalysis:
    """Fuel mixture analysis results"""
    mixture_status: str  # rich, lean, optimal
    short_term_trim: float
    long_term_trim: float
    oxygen_sensor_voltage: float
    recommendations: List[str] = field(default_factory=list)


@dataclass
class EngineLoadAnalysis:
    """Engine load analysis results"""
    current_load: float
    load_status: str  # low, normal, high, critical
    efficiency_rating: float
    stress_level: str  # minimal, moderate, high, excessive
    recommendations: List[str] = field(default_factory=list)


class EnhancedLiveMonitor:
    """
    Enhanced live data monitor with comprehensive analysis
    """
    
    def __init__(self, obd_reader: OBDReader):
        self.obd_reader = obd_reader
        self.is_monitoring = False
        self.monitoring_interval = 0.1  # 100ms as requested
        
        # Data storage
        self.current_readings: Dict[SensorType, SensorReading] = {}
        self.reading_history: Dict[SensorType, List[SensorReading]] = {}
        self.max_history_size = 1000
        
        # Analysis components
        self.fuel_analyzer = FuelMixtureAnalyzer()
        self.load_analyzer = EngineLoadAnalyzer()
        self.consumption_calculator = FuelConsumptionCalculator()
        
        # Monitoring tasks
        self.monitoring_tasks: List[asyncio.Task] = []
        
        # Callbacks
        self.data_callbacks: List[Callable[[Dict[SensorType, SensorReading]], None]] = []
        self.alert_callbacks: List[Callable[[str, SensorReading], None]] = []
        
        # Statistics
        self.stats = {
            'readings_collected': 0,
            'monitoring_start_time': None,
            'last_update': None,
            'errors': 0
        }
    
    async def start_monitoring(self, sensors: Optional[List[SensorType]] = None):
        """Start enhanced live monitoring"""
        if self.is_monitoring:
            logger.warning("Monitoring already active")
            return
        
        if not self.obd_reader.is_connected:
            raise ConnectionError("OBD reader not connected")
        
        # Default to all sensors if none specified
        if sensors is None:
            sensors = list(SensorType)
        
        self.is_monitoring = True
        self.stats['monitoring_start_time'] = datetime.now()
        
        # Start monitoring tasks
        for sensor in sensors:
            task = asyncio.create_task(self._monitor_sensor(sensor))
            self.monitoring_tasks.append(task)
        
        # Start analysis task
        analysis_task = asyncio.create_task(self._continuous_analysis())
        self.monitoring_tasks.append(analysis_task)
        
        logger.info(f"Started enhanced monitoring for {len(sensors)} sensors")
    
    async def stop_monitoring(self):
        """Stop live monitoring"""
        self.is_monitoring = False
        
        # Cancel all monitoring tasks
        for task in self.monitoring_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
        self.monitoring_tasks.clear()
        
        logger.info("Enhanced monitoring stopped")
    
    async def _monitor_sensor(self, sensor_type: SensorType):
        """Monitor individual sensor with 100ms intervals"""
        pid_map = self._get_pid_for_sensor(sensor_type)
        
        while self.is_monitoring:
            try:
                # Read parameter with timeout protection
                param = await asyncio.wait_for(
                    self.obd_reader.read_parameter(pid_map),
                    timeout=5.0
                )
                
                if param:
                    # Create sensor reading
                    reading = SensorReading(
                        sensor_type=sensor_type,
                        value=param.value,
                        unit=param.unit,
                        timestamp=param.timestamp,
                        quality=1.0 if param.status == "success" else 0.5
                    )
                    
                    # Analyze reading
                    await self._analyze_reading(reading)
                    
                    # Store reading
                    self.current_readings[sensor_type] = reading
                    self._store_reading_history(reading)
                    
                    self.stats['readings_collected'] += 1
                    self.stats['last_update'] = datetime.now()
                
                # Wait 100ms as requested for ELM327 timeout prevention
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.TimeoutError:
                logger.warning(f"Timeout reading sensor {sensor_type}")
                self.stats['errors'] += 1
                await asyncio.sleep(0.5)  # Longer delay on timeout
                
            except Exception as e:
                logger.error(f"Error monitoring sensor {sensor_type}: {e}")
                self.stats['errors'] += 1
                await asyncio.sleep(0.2)
    
    async def _analyze_reading(self, reading: SensorReading):
        """Analyze individual sensor reading"""
        try:
            # Determine status based on value ranges
            reading.status = self._determine_status(reading)
            
            # Calculate trend if we have history
            reading.trend = self._calculate_trend(reading)
            
            # Generate AI analysis for critical readings
            if reading.status in ["warning", "critical"]:
                reading.analysis = await self._get_ai_analysis(reading)
            
        except Exception as e:
            logger.error(f"Error analyzing reading for {reading.sensor_type}: {e}")
    
    async def _continuous_analysis(self):
        """Continuous analysis of all sensor data"""
        while self.is_monitoring:
            try:
                if len(self.current_readings) > 5:  # Need minimum data
                    # Analyze fuel mixture
                    fuel_analysis = await self.fuel_analyzer.analyze(self.current_readings)
                    
                    # Analyze engine load
                    load_analysis = await self.load_analyzer.analyze(self.current_readings)
                    
                    # Calculate fuel consumption
                    consumption = await self.consumption_calculator.calculate(self.current_readings)
                    
                    # Trigger callbacks
                    for callback in self.data_callbacks:
                        try:
                            callback(self.current_readings)
                        except Exception as e:
                            logger.error(f"Data callback error: {e}")
                
                await asyncio.sleep(1.0)  # Analysis every second
                
            except Exception as e:
                logger.error(f"Error in continuous analysis: {e}")
                await asyncio.sleep(2.0)
    
    def _get_pid_for_sensor(self, sensor_type: SensorType) -> str:
        """Map sensor type to OBD PID"""
        pid_mapping = {
            SensorType.ENGINE_RPM: "0x0C",
            SensorType.ENGINE_LOAD: "0x04",
            SensorType.ENGINE_COOLANT_TEMP: "0x05",
            SensorType.INTAKE_AIR_TEMP: "0x0F",
            SensorType.FUEL_PRESSURE: "0x0A",
            SensorType.FUEL_TRIM_SHORT_BANK1: "0x06",
            SensorType.FUEL_TRIM_LONG_BANK1: "0x07",
            SensorType.FUEL_TRIM_SHORT_BANK2: "0x08",
            SensorType.FUEL_TRIM_LONG_BANK2: "0x09",
            SensorType.MAF_FLOW_RATE: "0x10",
            SensorType.THROTTLE_POSITION: "0x11",
            SensorType.OXYGEN_SENSOR_BANK1: "0x14",
            SensorType.OXYGEN_SENSOR_BANK2: "0x15",
            SensorType.VEHICLE_SPEED: "0x0D",
            SensorType.TIMING_ADVANCE: "0x0E",
            SensorType.BAROMETRIC_PRESSURE: "0x33",
            SensorType.AMBIENT_AIR_TEMP: "0x46"
        }
        return pid_mapping.get(sensor_type, "0x00")
    
    def _determine_status(self, reading: SensorReading) -> str:
        """Determine sensor status based on value ranges"""
        # Define normal ranges for each sensor
        normal_ranges = {
            SensorType.ENGINE_RPM: (600, 6000),
            SensorType.ENGINE_LOAD: (0, 85),
            SensorType.ENGINE_COOLANT_TEMP: (80, 105),
            SensorType.FUEL_TRIM_SHORT_BANK1: (-10, 10),
            SensorType.FUEL_TRIM_LONG_BANK1: (-10, 10),
            SensorType.THROTTLE_POSITION: (0, 100),
            SensorType.VEHICLE_SPEED: (0, 200)
        }
        
        if reading.sensor_type in normal_ranges:
            min_val, max_val = normal_ranges[reading.sensor_type]
            
            if reading.value < min_val * 0.8 or reading.value > max_val * 1.2:
                return "critical"
            elif reading.value < min_val * 0.9 or reading.value > max_val * 1.1:
                return "warning"
        
        return "normal"
    
    def _calculate_trend(self, reading: SensorReading) -> str:
        """Calculate trend from recent history"""
        if reading.sensor_type not in self.reading_history:
            return "stable"
        
        history = self.reading_history[reading.sensor_type]
        if len(history) < 5:
            return "stable"
        
        # Get last 5 readings
        recent_values = [r.value for r in history[-5:]]
        
        # Simple trend calculation
        if recent_values[-1] > recent_values[0] * 1.05:
            return "increasing"
        elif recent_values[-1] < recent_values[0] * 0.95:
            return "decreasing"
        else:
            return "stable"
    
    async def _get_ai_analysis(self, reading: SensorReading) -> str:
        """Get AI analysis for concerning readings"""
        try:
            if gemini_integration.is_initialized:
                context = {
                    'sensor': reading.sensor_type.value,
                    'value': reading.value,
                    'unit': reading.unit,
                    'status': reading.status,
                    'trend': reading.trend
                }
                
                response = await gemini_integration.explain_live_data(
                    {'current_reading': context},
                    {'make': 'Unknown', 'model': 'Unknown'},
                    f"Sensor {reading.sensor_type.value} showing {reading.status} status"
                )
                
                return response.content[:200]  # Truncate for storage
        except Exception as e:
            logger.error(f"Error getting AI analysis: {e}")
        
        return f"Sensor {reading.sensor_type.value} reading {reading.value} {reading.unit} is {reading.status}"
    
    def _store_reading_history(self, reading: SensorReading):
        """Store reading in history with size limit"""
        if reading.sensor_type not in self.reading_history:
            self.reading_history[reading.sensor_type] = []
        
        history = self.reading_history[reading.sensor_type]
        history.append(reading)
        
        # Limit history size
        if len(history) > self.max_history_size:
            self.reading_history[reading.sensor_type] = history[-self.max_history_size:]
    
    def get_current_readings(self) -> Dict[SensorType, SensorReading]:
        """Get current sensor readings"""
        return self.current_readings.copy()
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """Get monitoring statistics"""
        return self.stats.copy()
    
    def add_data_callback(self, callback: Callable[[Dict[SensorType, SensorReading]], None]):
        """Add data callback"""
        self.data_callbacks.append(callback)
    
    def add_alert_callback(self, callback: Callable[[str, SensorReading], None]):
        """Add alert callback"""
        self.alert_callbacks.append(callback)


class FuelMixtureAnalyzer:
    """Analyzes fuel mixture from sensor data"""
    
    async def analyze(self, readings: Dict[SensorType, SensorReading]) -> FuelMixtureAnalysis:
        """Analyze fuel mixture status"""
        # Get relevant readings
        short_trim = readings.get(SensorType.FUEL_TRIM_SHORT_BANK1)
        long_trim = readings.get(SensorType.FUEL_TRIM_LONG_BANK1)
        o2_sensor = readings.get(SensorType.OXYGEN_SENSOR_BANK1)
        
        if not all([short_trim, long_trim, o2_sensor]):
            return FuelMixtureAnalysis(
                mixture_status="unknown",
                short_term_trim=0,
                long_term_trim=0,
                oxygen_sensor_voltage=0,
                recommendations=["Insufficient sensor data for analysis"]
            )
        
        # Analyze mixture
        short_val = short_trim.value
        long_val = long_trim.value
        o2_val = o2_sensor.value
        
        # Determine mixture status
        if short_val > 5 or long_val > 5:
            status = "lean"
            recommendations = [
                "Check for vacuum leaks",
                "Inspect fuel pressure",
                "Clean MAF sensor"
            ]
        elif short_val < -5 or long_val < -5:
            status = "rich"
            recommendations = [
                "Check air filter",
                "Inspect fuel injectors",
                "Check oxygen sensors"
            ]
        else:
            status = "optimal"
            recommendations = ["Fuel mixture is within normal range"]
        
        return FuelMixtureAnalysis(
            mixture_status=status,
            short_term_trim=short_val,
            long_term_trim=long_val,
            oxygen_sensor_voltage=o2_val,
            recommendations=recommendations
        )


class EngineLoadAnalyzer:
    """Analyzes engine load and efficiency"""
    
    async def analyze(self, readings: Dict[SensorType, SensorReading]) -> EngineLoadAnalysis:
        """Analyze engine load status"""
        load_reading = readings.get(SensorType.ENGINE_LOAD)
        rpm_reading = readings.get(SensorType.ENGINE_RPM)
        throttle_reading = readings.get(SensorType.THROTTLE_POSITION)
        
        if not load_reading:
            return EngineLoadAnalysis(
                current_load=0,
                load_status="unknown",
                efficiency_rating=0,
                stress_level="unknown",
                recommendations=["Engine load data not available"]
            )
        
        load_value = load_reading.value
        
        # Determine load status
        if load_value < 20:
            load_status = "low"
            stress_level = "minimal"
        elif load_value < 60:
            load_status = "normal"
            stress_level = "moderate"
        elif load_value < 85:
            load_status = "high"
            stress_level = "high"
        else:
            load_status = "critical"
            stress_level = "excessive"
        
        # Calculate efficiency (simplified)
        efficiency = 100 - (load_value * 0.5) if load_value < 80 else 60
        
        # Generate recommendations
        recommendations = []
        if load_status == "critical":
            recommendations.extend([
                "Reduce engine load immediately",
                "Check for mechanical issues",
                "Inspect air intake system"
            ])
        elif load_status == "high":
            recommendations.extend([
                "Monitor engine temperature",
                "Consider reducing load",
                "Check engine performance"
            ])
        
        return EngineLoadAnalysis(
            current_load=load_value,
            load_status=load_status,
            efficiency_rating=efficiency,
            stress_level=stress_level,
            recommendations=recommendations
        )


class FuelConsumptionCalculator:
    """Calculates fuel consumption metrics"""
    
    def __init__(self):
        self.consumption_history = []
    
    async def calculate(self, readings: Dict[SensorType, SensorReading]) -> Dict[str, float]:
        """Calculate fuel consumption metrics"""
        maf_reading = readings.get(SensorType.MAF_FLOW_RATE)
        speed_reading = readings.get(SensorType.VEHICLE_SPEED)
        
        if not all([maf_reading, speed_reading]):
            return {
                'instant_consumption': 0,
                'average_consumption': 0,
                'efficiency_rating': 0
            }
        
        # Simplified fuel consumption calculation
        # Real calculation would need more parameters
        maf_rate = maf_reading.value  # g/s
        speed = speed_reading.value   # km/h
        
        # Estimate fuel consumption (L/100km)
        if speed > 0:
            instant_consumption = (maf_rate * 3.6) / (speed * 14.7 * 0.74)  # Simplified
        else:
            instant_consumption = 0
        
        # Store for average calculation
        self.consumption_history.append(instant_consumption)
        if len(self.consumption_history) > 100:
            self.consumption_history = self.consumption_history[-100:]
        
        average_consumption = statistics.mean(self.consumption_history) if self.consumption_history else 0
        
        return {
            'instant_consumption': instant_consumption,
            'average_consumption': average_consumption,
            'efficiency_rating': max(0, 100 - (average_consumption * 2))
        }
