"""
Enhanced OBD Connection Manager
Implements retry/reset mechanisms, timeout prevention, and python-obd failover
"""
import asyncio
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from enum import Enum
import time

try:
    import obd
    from obd import OBDStatus
    import serial
    import serial.tools.list_ports
    OBD_AVAILABLE = True
except ImportError:
    OBD_AVAILABLE = False
    logger.warning("OBD libraries not available")

from ..config import settings

logger = logging.getLogger(__name__)


class ConnectionState(Enum):
    """Connection states"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    RETRYING = "retrying"
    FAILOVER = "failover"


class ConnectionMethod(Enum):
    """Connection methods"""
    PRIMARY = "primary"
    PYTHON_OBD = "python_obd"
    MOCK = "mock"


class ConnectionManager:
    """
    Enhanced OBD connection manager with retry/reset mechanisms
    """
    
    def __init__(self):
        self.state = ConnectionState.DISCONNECTED
        self.current_method = None
        self.connection = None
        self.port = None
        self.baudrate = 38400
        
        # Retry configuration
        self.max_retries = 3
        self.retry_delay = 2.0
        self.reset_delay = 5.0
        self.connection_timeout = 30.0
        
        # Query timing (100ms as requested)
        self.query_delay = 0.1  # 100ms between queries
        self.last_query_time = 0
        
        # Connection statistics
        self.stats = {
            'connection_attempts': 0,
            'successful_connections': 0,
            'failed_connections': 0,
            'retries_performed': 0,
            'resets_performed': 0,
            'queries_sent': 0,
            'query_timeouts': 0,
            'last_connection_time': None,
            'uptime_start': None
        }
        
        # Error tracking
        self.recent_errors = []
        self.max_error_history = 50
        
        # Failover configuration
        self.failover_enabled = True
        self.failover_threshold = 3  # Number of failures before failover
        self.consecutive_failures = 0
    
    async def connect(self, port: Optional[str] = None, baudrate: int = 38400) -> bool:
        """
        Connect to OBD adapter with retry and failover mechanisms
        """
        self.port = port or settings.obd_port
        self.baudrate = baudrate
        self.state = ConnectionState.CONNECTING
        self.stats['connection_attempts'] += 1
        
        logger.info(f"Attempting OBD connection to {self.port or 'auto-detect'}")
        
        # Try primary connection method first
        success = await self._try_connection_method(ConnectionMethod.PRIMARY)
        
        if not success and self.failover_enabled:
            logger.info("Primary connection failed, trying python-obd failover")
            success = await self._try_connection_method(ConnectionMethod.PYTHON_OBD)
        
        if not success:
            logger.warning("All connection methods failed, using mock connection")
            success = await self._try_connection_method(ConnectionMethod.MOCK)
        
        if success:
            self.state = ConnectionState.CONNECTED
            self.stats['successful_connections'] += 1
            self.stats['last_connection_time'] = datetime.now()
            self.stats['uptime_start'] = datetime.now()
            self.consecutive_failures = 0
            logger.info(f"OBD connection established using {self.current_method.value}")
        else:
            self.state = ConnectionState.ERROR
            self.stats['failed_connections'] += 1
            self.consecutive_failures += 1
            logger.error("Failed to establish OBD connection")
        
        return success
    
    async def _try_connection_method(self, method: ConnectionMethod) -> bool:
        """Try specific connection method with retries"""
        for attempt in range(self.max_retries):
            try:
                if method == ConnectionMethod.PRIMARY:
                    success = await self._connect_primary()
                elif method == ConnectionMethod.PYTHON_OBD:
                    success = await self._connect_python_obd()
                elif method == ConnectionMethod.MOCK:
                    success = await self._connect_mock()
                else:
                    success = False
                
                if success:
                    self.current_method = method
                    return True
                
            except Exception as e:
                self._log_error(f"Connection attempt {attempt + 1} failed: {e}")
                self.stats['retries_performed'] += 1
            
            if attempt < self.max_retries - 1:
                logger.info(f"Retrying connection in {self.retry_delay} seconds...")
                await asyncio.sleep(self.retry_delay)
        
        return False
    
    async def _connect_primary(self) -> bool:
        """Primary connection method"""
        if not OBD_AVAILABLE:
            return False
        
        try:
            if self.port:
                self.connection = await asyncio.wait_for(
                    asyncio.to_thread(obd.OBD, self.port, baudrate=self.baudrate),
                    timeout=self.connection_timeout
                )
            else:
                self.connection = await asyncio.wait_for(
                    asyncio.to_thread(obd.OBD),
                    timeout=self.connection_timeout
                )
            
            return self.connection and self.connection.status() == OBDStatus.CAR_CONNECTED
            
        except asyncio.TimeoutError:
            self._log_error("Primary connection timeout")
            return False
        except Exception as e:
            self._log_error(f"Primary connection error: {e}")
            return False
    
    async def _connect_python_obd(self) -> bool:
        """Python-OBD failover connection"""
        if not OBD_AVAILABLE:
            return False
        
        try:
            # Try with different settings for failover
            self.connection = await asyncio.wait_for(
                asyncio.to_thread(
                    obd.OBD,
                    self.port,
                    baudrate=self.baudrate,
                    protocol=None,  # Auto-detect protocol
                    fast=False,     # Slower but more reliable
                    timeout=10      # Longer timeout
                ),
                timeout=self.connection_timeout
            )
            
            return self.connection and self.connection.status() == OBDStatus.CAR_CONNECTED
            
        except Exception as e:
            self._log_error(f"Python-OBD failover error: {e}")
            return False
    
    async def _connect_mock(self) -> bool:
        """Mock connection for testing"""
        try:
            from ..obd_interface.mock_obd import MockOBDConnection
            self.connection = MockOBDConnection()
            return True
        except Exception as e:
            self._log_error(f"Mock connection error: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from OBD adapter"""
        if self.connection:
            try:
                if hasattr(self.connection, 'close'):
                    self.connection.close()
                logger.info("OBD connection closed")
            except Exception as e:
                self._log_error(f"Error closing connection: {e}")
            finally:
                self.connection = None
                self.state = ConnectionState.DISCONNECTED
                self.current_method = None
    
    async def query_with_retry(self, command, max_retries: int = 2) -> Any:
        """
        Query OBD with retry mechanism and 100ms delay
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to OBD adapter")
        
        # Implement 100ms delay between queries
        current_time = time.time()
        time_since_last_query = current_time - self.last_query_time
        if time_since_last_query < self.query_delay:
            await asyncio.sleep(self.query_delay - time_since_last_query)
        
        self.last_query_time = time.time()
        self.stats['queries_sent'] += 1
        
        for attempt in range(max_retries + 1):
            try:
                # Query with timeout
                response = await asyncio.wait_for(
                    asyncio.to_thread(self.connection.query, command),
                    timeout=5.0
                )
                
                if response and not response.is_null():
                    return response
                else:
                    self._log_error(f"Empty response for command {command}")
                
            except asyncio.TimeoutError:
                self.stats['query_timeouts'] += 1
                self._log_error(f"Query timeout for command {command}")
                
                # Try connection reset on timeout
                if attempt == max_retries:
                    await self._reset_connection()
                
            except Exception as e:
                self._log_error(f"Query error for command {command}: {e}")
                
                # Check if we need to reconnect
                if not self.is_connected():
                    await self._handle_connection_loss()
                    break
            
            if attempt < max_retries:
                await asyncio.sleep(0.2)  # Short delay before retry
        
        return None
    
    async def _reset_connection(self):
        """Reset OBD connection"""
        logger.info("Resetting OBD connection")
        self.stats['resets_performed'] += 1
        
        try:
            await self.disconnect()
            await asyncio.sleep(self.reset_delay)
            await self.connect(self.port, self.baudrate)
        except Exception as e:
            self._log_error(f"Connection reset failed: {e}")
    
    async def _handle_connection_loss(self):
        """Handle unexpected connection loss"""
        logger.warning("Connection lost, attempting to reconnect")
        self.state = ConnectionState.RETRYING
        
        success = await self.connect(self.port, self.baudrate)
        if not success:
            self.state = ConnectionState.ERROR
            self.consecutive_failures += 1
    
    def is_connected(self) -> bool:
        """Check if connected to OBD adapter"""
        if not self.connection:
            return False
        
        try:
            if hasattr(self.connection, 'status'):
                return self.connection.status() == OBDStatus.CAR_CONNECTED
            elif hasattr(self.connection, 'is_connected'):
                return self.connection.is_connected()
            else:
                return True  # Mock connection
        except Exception:
            return False
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information"""
        info = {
            'state': self.state.value,
            'method': self.current_method.value if self.current_method else None,
            'port': self.port,
            'baudrate': self.baudrate,
            'is_connected': self.is_connected(),
            'consecutive_failures': self.consecutive_failures
        }
        
        if self.connection and hasattr(self.connection, 'port_name'):
            info['actual_port'] = self.connection.port_name()
        
        if self.connection and hasattr(self.connection, 'protocol_name'):
            info['protocol'] = self.connection.protocol_name()
        
        return info
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get connection statistics"""
        stats = self.stats.copy()
        
        # Calculate uptime
        if stats['uptime_start']:
            uptime = datetime.now() - stats['uptime_start']
            stats['uptime_seconds'] = uptime.total_seconds()
        
        # Calculate success rate
        total_attempts = stats['connection_attempts']
        if total_attempts > 0:
            stats['success_rate'] = stats['successful_connections'] / total_attempts
        else:
            stats['success_rate'] = 0
        
        # Query statistics
        if stats['queries_sent'] > 0:
            stats['timeout_rate'] = stats['query_timeouts'] / stats['queries_sent']
        else:
            stats['timeout_rate'] = 0
        
        return stats
    
    def get_recent_errors(self) -> List[Dict[str, Any]]:
        """Get recent error history"""
        return self.recent_errors.copy()
    
    def _log_error(self, message: str):
        """Log error with timestamp"""
        error_entry = {
            'timestamp': datetime.now(),
            'message': message,
            'state': self.state.value,
            'method': self.current_method.value if self.current_method else None
        }
        
        self.recent_errors.append(error_entry)
        
        # Limit error history size
        if len(self.recent_errors) > self.max_error_history:
            self.recent_errors = self.recent_errors[-self.max_error_history:]
        
        logger.error(message)
    
    @staticmethod
    def list_available_ports() -> List[str]:
        """List available serial ports"""
        try:
            ports = serial.tools.list_ports.comports()
            return [port.device for port in ports]
        except Exception as e:
            logger.error(f"Error listing ports: {e}")
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform connection health check"""
        health = {
            'status': 'healthy',
            'issues': [],
            'recommendations': []
        }
        
        # Check connection state
        if not self.is_connected():
            health['status'] = 'unhealthy'
            health['issues'].append('Not connected to OBD adapter')
            health['recommendations'].append('Check OBD adapter connection')
        
        # Check error rate
        if self.consecutive_failures > self.failover_threshold:
            health['status'] = 'degraded'
            health['issues'].append(f'High failure rate: {self.consecutive_failures} consecutive failures')
            health['recommendations'].append('Check OBD adapter and vehicle connection')
        
        # Check query timeout rate
        stats = self.get_statistics()
        if stats.get('timeout_rate', 0) > 0.1:  # More than 10% timeouts
            health['status'] = 'degraded'
            health['issues'].append('High query timeout rate')
            health['recommendations'].append('Check communication speed and adapter quality')
        
        return health


# Global connection manager instance
connection_manager = ConnectionManager()
