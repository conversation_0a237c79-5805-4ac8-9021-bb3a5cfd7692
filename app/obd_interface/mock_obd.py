"""
Mock OBD Interface for Testing
Provides realistic OBD data for testing without physical hardware
"""
import asyncio
import random
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass

from ..api.models import OBDParameter


@dataclass
class MockVehicleState:
    """Mock vehicle state for realistic data generation"""
    engine_running: bool = True
    rpm: float = 800.0
    speed: float = 0.0
    coolant_temp: float = 90.0
    load: float = 15.0
    throttle: float = 0.0
    fuel_trim_short: float = 2.0
    fuel_trim_long: float = 1.5
    maf_rate: float = 3.2
    o2_voltage: float = 0.45
    
    # Simulation parameters
    acceleration: float = 0.0
    target_speed: float = 0.0
    engine_warming: bool = False


class MockOBDConnection:
    """Mock OBD connection for testing"""
    
    def __init__(self):
        self.connected = False
        self.protocol = "ISO 15765-4 (CAN 11/500)"
        self.port_name = "MOCK_PORT"
    
    def status(self):
        return "CAR_CONNECTED" if self.connected else "NOT_CONNECTED"
    
    def is_connected(self):
        return self.connected
    
    def close(self):
        self.connected = False
    
    def port_name(self):
        return self.port_name
    
    def protocol_name(self):
        return self.protocol
    
    def query(self, command):
        """Mock query response"""
        class MockResponse:
            def __init__(self, value, raw=None):
                self.value = value
                self.raw = raw
            
            def is_null(self):
                return self.value is None
        
        # Simulate some delay
        time.sleep(0.01)
        
        # Return mock data based on command
        if hasattr(command, 'pid'):
            pid = command.pid
        else:
            pid = str(command)
        
        # Mock responses for common PIDs
        mock_data = {
            '0C': 2500,  # RPM
            '04': 45,    # Engine load
            '05': 90,    # Coolant temp
            '0D': 60,    # Vehicle speed
            '10': 4.5,   # MAF rate
            '11': 25,    # Throttle position
            '06': 2.5,   # Short term fuel trim
            '07': 1.8,   # Long term fuel trim
            '14': 0.45,  # O2 sensor
            '0F': 25,    # Intake air temp
        }
        
        value = mock_data.get(pid, 0)
        return MockResponse(value)


class MockOBDReader:
    """Mock OBD reader for testing"""
    
    def __init__(self):
        self.connection = None
        self.is_connected = False
        self.vehicle_state = MockVehicleState()
        self.simulation_running = False
        self.simulation_task = None
    
    async def connect(self) -> bool:
        """Mock connection"""
        await asyncio.sleep(0.1)  # Simulate connection delay
        self.connection = MockOBDConnection()
        self.connection.connected = True
        self.is_connected = True
        
        # Start vehicle simulation
        await self.start_simulation()
        
        return True
    
    async def disconnect(self):
        """Mock disconnection"""
        if self.connection:
            self.connection.close()
        self.is_connected = False
        
        # Stop simulation
        await self.stop_simulation()
    
    async def start_simulation(self):
        """Start vehicle state simulation"""
        if not self.simulation_running:
            self.simulation_running = True
            self.simulation_task = asyncio.create_task(self._simulate_vehicle())
    
    async def stop_simulation(self):
        """Stop vehicle state simulation"""
        self.simulation_running = False
        if self.simulation_task:
            self.simulation_task.cancel()
            try:
                await self.simulation_task
            except asyncio.CancelledError:
                pass
    
    async def _simulate_vehicle(self):
        """Simulate realistic vehicle behavior"""
        while self.simulation_running:
            try:
                # Simulate engine warming up
                if self.vehicle_state.coolant_temp < 90:
                    self.vehicle_state.coolant_temp += 0.5
                
                # Simulate RPM variations
                if self.vehicle_state.engine_running:
                    base_rpm = 800 + (self.vehicle_state.throttle * 50)
                    self.vehicle_state.rpm = base_rpm + random.uniform(-50, 50)
                else:
                    self.vehicle_state.rpm = 0
                
                # Simulate speed changes
                if self.vehicle_state.acceleration != 0:
                    self.vehicle_state.speed += self.vehicle_state.acceleration
                    self.vehicle_state.speed = max(0, self.vehicle_state.speed)
                
                # Simulate load based on throttle and speed
                self.vehicle_state.load = (
                    self.vehicle_state.throttle * 0.8 + 
                    (self.vehicle_state.speed / 100) * 0.2 + 
                    random.uniform(-5, 5)
                )
                self.vehicle_state.load = max(0, min(100, self.vehicle_state.load))
                
                # Simulate fuel trims (slight variations)
                self.vehicle_state.fuel_trim_short += random.uniform(-0.2, 0.2)
                self.vehicle_state.fuel_trim_short = max(-25, min(25, self.vehicle_state.fuel_trim_short))
                
                self.vehicle_state.fuel_trim_long += random.uniform(-0.1, 0.1)
                self.vehicle_state.fuel_trim_long = max(-25, min(25, self.vehicle_state.fuel_trim_long))
                
                # Simulate MAF rate based on load and RPM
                self.vehicle_state.maf_rate = (
                    (self.vehicle_state.rpm / 1000) * 2 + 
                    (self.vehicle_state.load / 100) * 3 + 
                    random.uniform(-0.5, 0.5)
                )
                self.vehicle_state.maf_rate = max(0, self.vehicle_state.maf_rate)
                
                # Simulate O2 sensor (cycling between rich and lean)
                cycle_time = time.time() % 4  # 4 second cycle
                if cycle_time < 2:
                    self.vehicle_state.o2_voltage = 0.1 + (cycle_time / 2) * 0.8
                else:
                    self.vehicle_state.o2_voltage = 0.9 - ((cycle_time - 2) / 2) * 0.8
                
                await asyncio.sleep(0.1)  # Update every 100ms
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Simulation error: {e}")
                await asyncio.sleep(1)
    
    async def read_parameter(self, pid: str) -> Optional[OBDParameter]:
        """Mock parameter reading"""
        if not self.is_connected:
            return None
        
        # Add small delay to simulate real OBD communication
        await asyncio.sleep(0.05)
        
        # Map PIDs to vehicle state
        pid_mapping = {
            '0C': ('Engine RPM', self.vehicle_state.rpm, 'rpm'),
            '04': ('Engine Load', self.vehicle_state.load, '%'),
            '05': ('Coolant Temperature', self.vehicle_state.coolant_temp, '°C'),
            '0D': ('Vehicle Speed', self.vehicle_state.speed, 'km/h'),
            '10': ('MAF Rate', self.vehicle_state.maf_rate, 'g/s'),
            '11': ('Throttle Position', self.vehicle_state.throttle, '%'),
            '06': ('Short Term Fuel Trim Bank 1', self.vehicle_state.fuel_trim_short, '%'),
            '07': ('Long Term Fuel Trim Bank 1', self.vehicle_state.fuel_trim_long, '%'),
            '14': ('O2 Sensor Bank 1', self.vehicle_state.o2_voltage, 'V'),
            '0F': ('Intake Air Temperature', 25 + random.uniform(-5, 5), '°C'),
            '33': ('Barometric Pressure', 101.3 + random.uniform(-2, 2), 'kPa'),
            '46': ('Ambient Air Temperature', 20 + random.uniform(-3, 3), '°C'),
        }
        
        if pid in pid_mapping:
            name, value, unit = pid_mapping[pid]
            
            # Add some realistic noise
            if isinstance(value, (int, float)):
                noise_factor = 0.02  # 2% noise
                value += value * random.uniform(-noise_factor, noise_factor)
            
            return OBDParameter(
                pid=pid,
                name=name,
                value=round(value, 2) if isinstance(value, float) else value,
                unit=unit,
                timestamp=datetime.now(),
                status="success"
            )
        
        return None
    
    async def read_dtc_codes(self) -> List[str]:
        """Mock DTC code reading"""
        if not self.is_connected:
            return []
        
        # Simulate some random DTCs based on vehicle state
        possible_dtcs = []
        
        # Engine-related DTCs
        if self.vehicle_state.coolant_temp > 105:
            possible_dtcs.append('P0217')  # Engine overheating
        
        if abs(self.vehicle_state.fuel_trim_short) > 10:
            possible_dtcs.append('P0171')  # System too lean
        
        if self.vehicle_state.maf_rate < 1.0:
            possible_dtcs.append('P0100')  # MAF circuit malfunction
        
        # Randomly add some DTCs for testing
        random_dtcs = ['P0300', 'P0420', 'P0171', 'P0174']
        if random.random() < 0.3:  # 30% chance of having DTCs
            possible_dtcs.extend(random.sample(random_dtcs, random.randint(1, 2)))
        
        return list(set(possible_dtcs))  # Remove duplicates
    
    async def clear_dtc_codes(self) -> bool:
        """Mock DTC clearing"""
        if not self.is_connected:
            return False
        
        await asyncio.sleep(0.5)  # Simulate clearing delay
        return True
    
    def get_vehicle_info(self) -> Dict[str, Any]:
        """Mock vehicle information"""
        return {
            'make': 'Toyota',
            'model': 'Prius',
            'year': 2015,
            'vin': '1HGBH41JXMN109186',
            'engine_type': '1.8L Hybrid',
            'fuel_type': 'Gasoline/Electric'
        }
    
    def simulate_driving_scenario(self, scenario: str):
        """Simulate different driving scenarios"""
        if scenario == "idle":
            self.vehicle_state.throttle = 0
            self.vehicle_state.acceleration = 0
            self.vehicle_state.target_speed = 0
        
        elif scenario == "acceleration":
            self.vehicle_state.throttle = 50
            self.vehicle_state.acceleration = 2
            self.vehicle_state.target_speed = 60
        
        elif scenario == "highway":
            self.vehicle_state.throttle = 30
            self.vehicle_state.acceleration = 0
            self.vehicle_state.target_speed = 100
        
        elif scenario == "city":
            self.vehicle_state.throttle = 20
            self.vehicle_state.acceleration = random.uniform(-1, 1)
            self.vehicle_state.target_speed = 50
        
        elif scenario == "engine_problem":
            # Simulate engine issues
            self.vehicle_state.fuel_trim_short = 15  # Rich condition
            self.vehicle_state.maf_rate *= 0.7  # Reduced airflow
    
    def get_simulation_state(self) -> Dict[str, Any]:
        """Get current simulation state"""
        return {
            'engine_running': self.vehicle_state.engine_running,
            'rpm': self.vehicle_state.rpm,
            'speed': self.vehicle_state.speed,
            'coolant_temp': self.vehicle_state.coolant_temp,
            'load': self.vehicle_state.load,
            'throttle': self.vehicle_state.throttle,
            'fuel_trim_short': self.vehicle_state.fuel_trim_short,
            'fuel_trim_long': self.vehicle_state.fuel_trim_long,
            'maf_rate': self.vehicle_state.maf_rate,
            'o2_voltage': self.vehicle_state.o2_voltage
        }
