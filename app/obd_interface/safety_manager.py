"""
OBD Safety Manager
Implements UI protection against critical write commands and Mode 04 safety
"""
import logging
from typing import Dict, List, Set, Optional, Any
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class CommandType(Enum):
    """OBD command types"""
    READ_ONLY = "read_only"
    WRITE = "write"
    CLEAR_CODES = "clear_codes"
    RESET_ECU = "reset_ecu"
    CALIBRATION = "calibration"
    ACTUATOR_TEST = "actuator_test"


class SafetyLevel(Enum):
    """Safety levels for commands"""
    SAFE = "safe"           # No risk
    CAUTION = "caution"     # Minor risk, warning needed
    DANGEROUS = "dangerous" # High risk, expert mode required
    CRITICAL = "critical"   # Extreme risk, multiple confirmations


@dataclass
class SafetyRule:
    """Safety rule definition"""
    command_pattern: str
    safety_level: SafetyLevel
    description: str
    warnings: List[str]
    requirements: List[str]
    cooldown_seconds: int = 0


class SafetyManager:
    """
    Manages OBD command safety and prevents dangerous operations
    """
    
    def __init__(self):
        self.expert_mode_enabled = False
        self.expert_mode_expires = None
        self.expert_mode_duration = timedelta(minutes=30)
        
        # Command execution history
        self.command_history: List[Dict[str, Any]] = []
        self.max_history_size = 1000
        
        # Cooldown tracking
        self.command_cooldowns: Dict[str, datetime] = {}
        
        # Safety rules
        self.safety_rules = self._initialize_safety_rules()
        
        # Blocked commands in normal mode
        self.blocked_commands = {
            "04",  # Clear DTCs
            "10",  # ECU Reset
            "11",  # ECU Reset
            "14",  # Clear Diagnostic Information
            "2E",  # Write Data By Identifier
            "31",  # Routine Control
            "34",  # Request Download
            "35",  # Request Upload
            "36",  # Transfer Data
        }
        
        # Commands requiring confirmation
        self.confirmation_required = {
            "04": "Clear all diagnostic trouble codes",
            "10": "Reset ECU to default session",
            "11": "Perform ECU hard reset",
            "14": "Clear diagnostic information",
            "2E": "Write data to ECU",
            "31": "Execute ECU routine/calibration"
        }
    
    def _initialize_safety_rules(self) -> Dict[str, SafetyRule]:
        """Initialize safety rules for OBD commands"""
        rules = {}
        
        # Mode 04 - Clear DTCs (DANGEROUS)
        rules["04"] = SafetyRule(
            command_pattern="04",
            safety_level=SafetyLevel.DANGEROUS,
            description="Clear Diagnostic Trouble Codes",
            warnings=[
                "This will clear all stored diagnostic codes",
                "Vehicle may need to complete drive cycles to reset readiness monitors",
                "Some ECUs may reset to default parameters",
                "Emission test readiness will be lost"
            ],
            requirements=[
                "Expert mode must be enabled",
                "Vehicle must be in park/neutral",
                "Engine should be running and warmed up"
            ],
            cooldown_seconds=300  # 5 minute cooldown
        )
        
        # ECU Reset commands (CRITICAL)
        rules["10"] = SafetyRule(
            command_pattern="10",
            safety_level=SafetyLevel.CRITICAL,
            description="ECU Reset/Session Control",
            warnings=[
                "May reset ECU to factory defaults",
                "Could cause engine stalling",
                "May require reprogramming",
                "Vehicle may become inoperable"
            ],
            requirements=[
                "Expert mode required",
                "Vehicle must be stationary",
                "Engine off recommended",
                "Professional supervision advised"
            ],
            cooldown_seconds=600  # 10 minute cooldown
        )
        
        # Write commands (DANGEROUS)
        rules["2E"] = SafetyRule(
            command_pattern="2E",
            safety_level=SafetyLevel.DANGEROUS,
            description="Write Data to ECU",
            warnings=[
                "Modifies ECU parameters permanently",
                "Incorrect values can damage engine",
                "May void vehicle warranty",
                "Could cause safety system failures"
            ],
            requirements=[
                "Expert mode required",
                "Exact parameter knowledge required",
                "Backup of original values recommended"
            ],
            cooldown_seconds=180  # 3 minute cooldown
        )
        
        # Routine Control (DANGEROUS)
        rules["31"] = SafetyRule(
            command_pattern="31",
            safety_level=SafetyLevel.DANGEROUS,
            description="ECU Routine Control",
            warnings=[
                "Executes ECU calibration routines",
                "May cause unexpected vehicle behavior",
                "Could trigger safety systems",
                "May require special tools to recover"
            ],
            requirements=[
                "Expert mode required",
                "Vehicle in safe environment",
                "Professional knowledge required"
            ],
            cooldown_seconds=300
        )
        
        # Read-only commands (SAFE)
        for mode in ["01", "02", "03", "05", "06", "07", "09", "0A"]:
            rules[mode] = SafetyRule(
                command_pattern=mode,
                safety_level=SafetyLevel.SAFE,
                description=f"Read Mode {mode} Data",
                warnings=[],
                requirements=[],
                cooldown_seconds=0
            )
        
        return rules
    
    def check_command_safety(self, command: str, user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Check if command is safe to execute
        """
        # Normalize command
        cmd = command.upper().strip()
        mode = cmd[:2] if len(cmd) >= 2 else cmd
        
        # Get safety rule
        rule = self.safety_rules.get(mode)
        if not rule:
            # Unknown command - treat as dangerous
            rule = SafetyRule(
                command_pattern=cmd,
                safety_level=SafetyLevel.DANGEROUS,
                description="Unknown OBD command",
                warnings=["Unknown command - potential risk"],
                requirements=["Expert mode required for unknown commands"]
            )
        
        # Check safety level
        safety_check = {
            'command': command,
            'mode': mode,
            'safety_level': rule.safety_level.value,
            'allowed': False,
            'warnings': rule.warnings.copy(),
            'requirements': rule.requirements.copy(),
            'confirmation_required': False,
            'expert_mode_required': False,
            'cooldown_remaining': 0,
            'description': rule.description
        }
        
        # Check cooldown
        if mode in self.command_cooldowns:
            cooldown_end = self.command_cooldowns[mode] + timedelta(seconds=rule.cooldown_seconds)
            if datetime.now() < cooldown_end:
                remaining = (cooldown_end - datetime.now()).total_seconds()
                safety_check['cooldown_remaining'] = int(remaining)
                safety_check['warnings'].append(f"Command in cooldown for {int(remaining)} seconds")
                return safety_check
        
        # Check based on safety level
        if rule.safety_level == SafetyLevel.SAFE:
            safety_check['allowed'] = True
            
        elif rule.safety_level == SafetyLevel.CAUTION:
            safety_check['allowed'] = True
            safety_check['confirmation_required'] = True
            
        elif rule.safety_level == SafetyLevel.DANGEROUS:
            if self.is_expert_mode_active():
                safety_check['allowed'] = True
                safety_check['confirmation_required'] = True
            else:
                safety_check['expert_mode_required'] = True
                safety_check['warnings'].append("Expert mode required for this command")
                
        elif rule.safety_level == SafetyLevel.CRITICAL:
            if self.is_expert_mode_active():
                safety_check['allowed'] = True
                safety_check['confirmation_required'] = True
                safety_check['warnings'].append("CRITICAL OPERATION - Multiple confirmations required")
            else:
                safety_check['expert_mode_required'] = True
                safety_check['warnings'].append("Expert mode required for critical operations")
        
        # Additional context-based checks
        if user_context:
            additional_checks = self._perform_context_checks(command, user_context, rule)
            safety_check.update(additional_checks)
        
        return safety_check
    
    def _perform_context_checks(self, command: str, context: Dict[str, Any], rule: SafetyRule) -> Dict[str, Any]:
        """Perform additional safety checks based on context"""
        additional_warnings = []
        requirements_met = True
        
        # Check vehicle state
        vehicle_speed = context.get('vehicle_speed', 0)
        engine_running = context.get('engine_running', False)
        
        # Speed-based restrictions
        if rule.safety_level in [SafetyLevel.DANGEROUS, SafetyLevel.CRITICAL]:
            if vehicle_speed > 5:  # km/h
                additional_warnings.append("Vehicle must be stationary for this operation")
                requirements_met = False
        
        # Engine state requirements
        if command.startswith('04'):  # Clear DTCs
            if not engine_running:
                additional_warnings.append("Engine should be running and warmed up")
        
        if command.startswith('10'):  # ECU Reset
            if engine_running:
                additional_warnings.append("Engine should be turned off for ECU reset")
        
        return {
            'context_warnings': additional_warnings,
            'context_requirements_met': requirements_met
        }
    
    def enable_expert_mode(self, confirmation_code: str) -> bool:
        """
        Enable expert mode with confirmation
        """
        # Simple confirmation code check (in production, use proper authentication)
        expected_code = "EXPERT_MODE_2024"
        
        if confirmation_code == expected_code:
            self.expert_mode_enabled = True
            self.expert_mode_expires = datetime.now() + self.expert_mode_duration
            
            logger.warning("Expert mode enabled - dangerous commands now available")
            self._log_command_execution("EXPERT_MODE_ENABLED", {"confirmation_code": "***"})
            
            return True
        else:
            logger.warning(f"Failed expert mode activation attempt")
            return False
    
    def disable_expert_mode(self):
        """Disable expert mode"""
        self.expert_mode_enabled = False
        self.expert_mode_expires = None
        logger.info("Expert mode disabled")
        self._log_command_execution("EXPERT_MODE_DISABLED", {})
    
    def is_expert_mode_active(self) -> bool:
        """Check if expert mode is currently active"""
        if not self.expert_mode_enabled:
            return False
        
        if self.expert_mode_expires and datetime.now() > self.expert_mode_expires:
            self.disable_expert_mode()
            return False
        
        return True
    
    def get_expert_mode_status(self) -> Dict[str, Any]:
        """Get expert mode status"""
        status = {
            'enabled': self.expert_mode_enabled,
            'active': self.is_expert_mode_active(),
            'expires_at': self.expert_mode_expires.isoformat() if self.expert_mode_expires else None,
            'time_remaining': None
        }
        
        if self.expert_mode_expires:
            remaining = self.expert_mode_expires - datetime.now()
            if remaining.total_seconds() > 0:
                status['time_remaining'] = int(remaining.total_seconds())
        
        return status
    
    def log_command_execution(self, command: str, result: Dict[str, Any], user_id: Optional[str] = None):
        """Log command execution for audit trail"""
        self._log_command_execution(command, result, user_id)
        
        # Update cooldown
        mode = command[:2] if len(command) >= 2 else command
        if mode in self.safety_rules:
            rule = self.safety_rules[mode]
            if rule.cooldown_seconds > 0:
                self.command_cooldowns[mode] = datetime.now()
    
    def _log_command_execution(self, command: str, result: Dict[str, Any], user_id: Optional[str] = None):
        """Internal command logging"""
        log_entry = {
            'timestamp': datetime.now(),
            'command': command,
            'result': result,
            'user_id': user_id,
            'expert_mode_active': self.is_expert_mode_active(),
            'safety_level': self.safety_rules.get(command[:2], SafetyRule("", SafetyLevel.SAFE, "", [], [])).safety_level.value
        }
        
        self.command_history.append(log_entry)
        
        # Limit history size
        if len(self.command_history) > self.max_history_size:
            self.command_history = self.command_history[-self.max_history_size:]
        
        # Log to file for audit
        logger.info(f"OBD Command executed: {command} by {user_id or 'unknown'}")
    
    def get_command_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent command execution history"""
        return self.command_history[-limit:]
    
    def get_safety_summary(self) -> Dict[str, Any]:
        """Get safety system summary"""
        return {
            'expert_mode': self.get_expert_mode_status(),
            'total_commands_executed': len(self.command_history),
            'dangerous_commands_executed': len([
                cmd for cmd in self.command_history 
                if cmd['safety_level'] in ['dangerous', 'critical']
            ]),
            'active_cooldowns': {
                mode: int((self.command_cooldowns[mode] + timedelta(seconds=rule.cooldown_seconds) - datetime.now()).total_seconds())
                for mode, rule in self.safety_rules.items()
                if mode in self.command_cooldowns and 
                   datetime.now() < self.command_cooldowns[mode] + timedelta(seconds=rule.cooldown_seconds)
            },
            'safety_rules_count': len(self.safety_rules)
        }
    
    def create_confirmation_prompt(self, command: str) -> Dict[str, Any]:
        """Create confirmation prompt for dangerous commands"""
        mode = command[:2] if len(command) >= 2 else command
        rule = self.safety_rules.get(mode)
        
        if not rule:
            return {
                'title': 'Unknown Command Confirmation',
                'message': f'Are you sure you want to execute unknown command: {command}?',
                'warnings': ['Unknown command - potential risk'],
                'confirmation_text': 'I understand the risks'
            }
        
        confirmation_text = "I understand the risks and consequences"
        if rule.safety_level == SafetyLevel.CRITICAL:
            confirmation_text = "I UNDERSTAND THIS IS A CRITICAL OPERATION"
        
        return {
            'title': f'{rule.description} Confirmation',
            'message': f'Are you sure you want to execute: {command}?',
            'warnings': rule.warnings,
            'requirements': rule.requirements,
            'confirmation_text': confirmation_text,
            'safety_level': rule.safety_level.value
        }


# Global safety manager instance
safety_manager = SafetyManager()
