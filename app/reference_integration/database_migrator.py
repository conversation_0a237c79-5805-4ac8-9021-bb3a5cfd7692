"""
Database Migrator for Reference Knowledge
Imports extracted knowledge into main SQLite database with proper normalization
"""
import logging
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy import text
from sqlalchemy.exc import IntegrityError

from ..database.database import DatabaseManager
from ..database.models import <PERSON>TCCode, BrandProfile
from .knowledge_extractor import ExtractedKnowledge, knowledge_extractor

logger = logging.getLogger(__name__)


class DatabaseMigrator:
    """
    Migrates extracted knowledge to main database with proper normalization
    """
    
    def __init__(self):
        self.db = DatabaseManager()
        self.stats = {
            'dtc_codes_imported': 0,
            'pid_definitions_imported': 0,
            'can_messages_imported': 0,
            'duplicates_skipped': 0,
            'errors': 0
        }
    
    async def migrate_all_references(self) -> Dict[str, Any]:
        """Migrate all knowledge from references folder"""
        logger.info("Starting migration of all reference knowledge")
        
        # Scan for diagnostic files
        diagnostic_files = await knowledge_extractor.scan_references_folder()
        
        # Process each file
        for file_path in diagnostic_files:
            try:
                knowledge = await knowledge_extractor.extract_knowledge_from_file(file_path)
                if knowledge:
                    await self._migrate_knowledge(knowledge)
            except Exception as e:
                logger.error(f"Error migrating {file_path}: {e}")
                self.stats['errors'] += 1
        
        logger.info(f"Migration completed. Stats: {self.stats}")
        return self.stats
    
    async def _migrate_knowledge(self, knowledge: ExtractedKnowledge):
        """Migrate extracted knowledge to database"""
        try:
            # Migrate DTC codes
            await self._migrate_dtc_codes(knowledge)
            
            # Migrate PID definitions
            await self._migrate_pid_definitions(knowledge)
            
            # Migrate CAN messages
            await self._migrate_can_messages(knowledge)
            
            # Update brand profiles
            await self._update_brand_profiles(knowledge)
            
        except Exception as e:
            logger.error(f"Error migrating knowledge from {knowledge.source_project}: {e}")
            self.stats['errors'] += 1
    
    async def _migrate_dtc_codes(self, knowledge: ExtractedKnowledge):
        """Migrate DTC codes with proper normalization"""
        for dtc_data in knowledge.dtc_codes:
            try:
                # Normalize DTC code
                code = self._normalize_dtc_code(dtc_data.get('code', ''))
                if not code:
                    continue
                
                # Check if already exists
                with self.db.get_session() as session:
                    existing = session.query(DTCCode).filter(DTCCode.code == code).first()
                    if existing:
                        # Update with additional information if available
                        await self._update_existing_dtc(existing, dtc_data, knowledge)
                        self.stats['duplicates_skipped'] += 1
                        continue
                
                # Create new DTC code
                dtc_code = DTCCode(
                    code=code,
                    description=self._extract_description(dtc_data),
                    category=self._determine_category(code),
                    severity=self._determine_severity(dtc_data),
                    system=self._determine_system(dtc_data),
                    possible_causes=self._extract_causes(dtc_data),
                    repair_hints=self._extract_repair_hints(dtc_data),
                    related_pids=self._extract_related_pids(dtc_data),
                    freeze_frame_required=self._determine_freeze_frame_required(code),
                    obd2_standard=self._is_obd2_standard(code),
                    brand_specific_info={
                        'brand': knowledge.brand_specific_info.get('brand', 'unknown'),
                        'source_project': knowledge.source_project,
                        'source_file': dtc_data.get('source_file', ''),
                        'extraction_timestamp': knowledge.extraction_timestamp.isoformat()
                    }
                )
                
                with self.db.get_session() as session:
                    session.add(dtc_code)
                    session.commit()
                    self.stats['dtc_codes_imported'] += 1
                
            except Exception as e:
                logger.error(f"Error migrating DTC code {dtc_data.get('code', 'unknown')}: {e}")
                self.stats['errors'] += 1
    
    async def _migrate_pid_definitions(self, knowledge: ExtractedKnowledge):
        """Migrate PID definitions to database"""
        for pid_data in knowledge.pid_definitions:
            try:
                # Create or update PID definition table
                await self._ensure_pid_table_exists()
                
                with self.db.get_session() as session:
                    # Insert PID definition
                    insert_query = text("""
                        INSERT OR REPLACE INTO pid_definitions 
                        (pid, name, description, unit, factor, offset, source_project, source_file, brand)
                        VALUES (:pid, :name, :description, :unit, :factor, :offset, :source_project, :source_file, :brand)
                    """)
                    
                    session.execute(insert_query, {
                        'pid': pid_data.get('pid', ''),
                        'name': pid_data.get('name', ''),
                        'description': pid_data.get('description', ''),
                        'unit': pid_data.get('unit', ''),
                        'factor': pid_data.get('factor', 1.0),
                        'offset': pid_data.get('offset', 0.0),
                        'source_project': knowledge.source_project,
                        'source_file': pid_data.get('source_file', ''),
                        'brand': knowledge.brand_specific_info.get('brand', 'unknown')
                    })
                    session.commit()
                    self.stats['pid_definitions_imported'] += 1
                
            except Exception as e:
                logger.error(f"Error migrating PID definition: {e}")
                self.stats['errors'] += 1
    
    async def _migrate_can_messages(self, knowledge: ExtractedKnowledge):
        """Migrate CAN message definitions to database"""
        for can_data in knowledge.can_messages:
            try:
                # Create or update CAN messages table
                await self._ensure_can_table_exists()
                
                with self.db.get_session() as session:
                    insert_query = text("""
                        INSERT OR REPLACE INTO can_messages 
                        (can_id, name, dlc, sender, source_project, source_file, brand)
                        VALUES (:can_id, :name, :dlc, :sender, :source_project, :source_file, :brand)
                    """)
                    
                    session.execute(insert_query, {
                        'can_id': can_data.get('can_id', 0),
                        'name': can_data.get('name', ''),
                        'dlc': can_data.get('dlc', 8),
                        'sender': can_data.get('sender', ''),
                        'source_project': knowledge.source_project,
                        'source_file': can_data.get('source_file', ''),
                        'brand': knowledge.brand_specific_info.get('brand', 'unknown')
                    })
                    session.commit()
                    self.stats['can_messages_imported'] += 1
                
            except Exception as e:
                logger.error(f"Error migrating CAN message: {e}")
                self.stats['errors'] += 1
    
    async def _ensure_pid_table_exists(self):
        """Ensure PID definitions table exists"""
        with self.db.get_session() as session:
            create_table_query = text("""
                CREATE TABLE IF NOT EXISTS pid_definitions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pid VARCHAR(10) NOT NULL,
                    name VARCHAR(100),
                    description TEXT,
                    unit VARCHAR(20),
                    factor REAL DEFAULT 1.0,
                    offset REAL DEFAULT 0.0,
                    source_project VARCHAR(100),
                    source_file TEXT,
                    brand VARCHAR(50),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(pid, brand, source_project)
                )
            """)
            session.execute(create_table_query)
            session.commit()
    
    async def _ensure_can_table_exists(self):
        """Ensure CAN messages table exists"""
        with self.db.get_session() as session:
            create_table_query = text("""
                CREATE TABLE IF NOT EXISTS can_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    can_id INTEGER NOT NULL,
                    name VARCHAR(100),
                    dlc INTEGER DEFAULT 8,
                    sender VARCHAR(50),
                    source_project VARCHAR(100),
                    source_file TEXT,
                    brand VARCHAR(50),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(can_id, brand, source_project)
                )
            """)
            session.execute(create_table_query)
            session.commit()
    
    def _normalize_dtc_code(self, code: str) -> str:
        """Normalize DTC code format"""
        if not code:
            return ""
        
        # Remove whitespace and convert to uppercase
        code = code.strip().upper()
        
        # Check if it's a valid DTC format
        if len(code) == 5 and code[0] in 'PBUCD' and code[1:].isalnum():
            return code
        
        return ""
    
    def _extract_description(self, dtc_data: Dict[str, Any]) -> str:
        """Extract description from DTC data"""
        for key in ['description', 'desc', 'details', 'meaning']:
            if key in dtc_data:
                value = dtc_data[key]
                if isinstance(value, str):
                    return value
                elif isinstance(value, dict) and 'description' in value:
                    return value['description']
        return "No description available"
    
    def _determine_category(self, code: str) -> str:
        """Determine DTC category from code"""
        if not code:
            return "Unknown"
        
        category_map = {
            'P': 'Powertrain',
            'B': 'Body',
            'C': 'Chassis',
            'U': 'Network',
            'D': 'Diagnostic'
        }
        return category_map.get(code[0], 'Unknown')
    
    def _determine_severity(self, dtc_data: Dict[str, Any]) -> str:
        """Determine severity from DTC data"""
        severity = dtc_data.get('severity', '').lower()
        if severity in ['critical', 'high', 'medium', 'low', 'info']:
            return severity
        return 'medium'  # Default
    
    def _determine_system(self, dtc_data: Dict[str, Any]) -> str:
        """Determine system from DTC data"""
        return dtc_data.get('system', 'Unknown')
    
    def _extract_causes(self, dtc_data: Dict[str, Any]) -> List[str]:
        """Extract possible causes from DTC data"""
        causes = dtc_data.get('possible_causes', [])
        if isinstance(causes, str):
            return [causes]
        elif isinstance(causes, list):
            return causes
        return []
    
    def _extract_repair_hints(self, dtc_data: Dict[str, Any]) -> List[str]:
        """Extract repair hints from DTC data"""
        hints = dtc_data.get('repair_hints', [])
        if isinstance(hints, str):
            return [hints]
        elif isinstance(hints, list):
            return hints
        return []
    
    def _extract_related_pids(self, dtc_data: Dict[str, Any]) -> List[str]:
        """Extract related PIDs from DTC data"""
        pids = dtc_data.get('related_pids', [])
        if isinstance(pids, str):
            return [pids]
        elif isinstance(pids, list):
            return pids
        return []
    
    def _determine_freeze_frame_required(self, code: str) -> bool:
        """Determine if freeze frame is required"""
        # Powertrain codes typically require freeze frame
        return code.startswith('P') if code else False
    
    def _is_obd2_standard(self, code: str) -> bool:
        """Determine if code is OBD2 standard"""
        if not code or len(code) != 5:
            return False
        
        # Standard OBD2 codes have specific patterns
        if code[0] == 'P' and code[1] == '0':
            return True
        
        return False
    
    async def _update_existing_dtc(self, existing_dtc: DTCCode, new_data: Dict[str, Any], knowledge: ExtractedKnowledge):
        """Update existing DTC with additional information"""
        try:
            # Merge brand-specific information
            if existing_dtc.brand_specific_info:
                brand_info = existing_dtc.brand_specific_info.copy()
            else:
                brand_info = {}
            
            # Add new source information
            sources = brand_info.get('sources', [])
            sources.append({
                'project': knowledge.source_project,
                'file': new_data.get('source_file', ''),
                'brand': knowledge.brand_specific_info.get('brand', 'unknown'),
                'timestamp': knowledge.extraction_timestamp.isoformat()
            })
            brand_info['sources'] = sources
            
            existing_dtc.brand_specific_info = brand_info
            existing_dtc.updated_at = datetime.now()
            
            with self.db.get_session() as session:
                session.merge(existing_dtc)
                session.commit()
                
        except Exception as e:
            logger.error(f"Error updating existing DTC {existing_dtc.code}: {e}")
    
    async def _update_brand_profiles(self, knowledge: ExtractedKnowledge):
        """Update brand profiles with extracted information"""
        brand = knowledge.brand_specific_info.get('brand', 'unknown')
        if brand == 'unknown':
            return
        
        try:
            with self.db.get_session() as session:
                # Check if brand profile exists
                brand_profile = session.query(BrandProfile).filter(
                    BrandProfile.brand_name == brand
                ).first()
                
                if not brand_profile:
                    # Create new brand profile
                    brand_profile = BrandProfile(
                        brand_name=brand,
                        supported_protocols=['OBD2'],
                        special_procedures={},
                        common_issues=[],
                        recalls_tsbs=[],
                        required_tools=[],
                        diagnostic_software=[],
                        notes=f"Auto-generated from {knowledge.source_project}"
                    )
                    session.add(brand_profile)
                else:
                    # Update existing profile
                    if brand_profile.notes:
                        brand_profile.notes += f"\nUpdated from {knowledge.source_project}"
                    else:
                        brand_profile.notes = f"Updated from {knowledge.source_project}"
                
                session.commit()
                
        except Exception as e:
            logger.error(f"Error updating brand profile for {brand}: {e}")


# Global instance
database_migrator = DatabaseMigrator()
