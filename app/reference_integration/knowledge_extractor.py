"""
Knowledge Extractor for Reference Projects
Extracts diagnostic knowledge from open-source automotive projects
"""
import os
import json
import logging
import sqlite3
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import re
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ExtractedKnowledge:
    """Container for extracted diagnostic knowledge"""
    dtc_codes: List[Dict[str, Any]]
    pid_definitions: List[Dict[str, Any]]
    can_messages: List[Dict[str, Any]]
    brand_specific_info: Dict[str, Any]
    source_project: str
    extraction_timestamp: datetime


class KnowledgeExtractor:
    """
    Extracts diagnostic knowledge from reference projects
    """
    
    def __init__(self, references_path: str = "references"):
        self.references_path = Path(references_path)
        self.extractors = {
            '.dbc': self._extract_from_dbc,
            '.db': self._extract_from_sqlite,
            '.json': self._extract_from_json,
            '.py': self._extract_from_python,
            '.txt': self._extract_from_text
        }
    
    async def scan_references_folder(self) -> List[Path]:
        """Scan references folder for diagnostic files"""
        diagnostic_files = []
        
        for root, dirs, files in os.walk(self.references_path):
            for file in files:
                file_path = Path(root) / file
                if file_path.suffix.lower() in self.extractors:
                    diagnostic_files.append(file_path)
        
        logger.info(f"Found {len(diagnostic_files)} diagnostic files in references")
        return diagnostic_files
    
    async def extract_knowledge_from_file(self, file_path: Path) -> Optional[ExtractedKnowledge]:
        """Extract knowledge from a single file"""
        try:
            extractor = self.extractors.get(file_path.suffix.lower())
            if not extractor:
                return None
            
            return await extractor(file_path)
            
        except Exception as e:
            logger.error(f"Error extracting from {file_path}: {e}")
            return None
    
    async def _extract_from_dbc(self, file_path: Path) -> ExtractedKnowledge:
        """Extract knowledge from DBC (CAN database) files"""
        dtc_codes = []
        pid_definitions = []
        can_messages = []
        brand_specific_info = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Extract CAN messages
            message_pattern = r'BO_\s+(\d+)\s+(\w+):\s*(\d+)\s+(\w+)'
            for match in re.finditer(message_pattern, content):
                can_id, name, dlc, sender = match.groups()
                can_messages.append({
                    'can_id': int(can_id),
                    'name': name,
                    'dlc': int(dlc),
                    'sender': sender,
                    'source_file': str(file_path)
                })
            
            # Extract signal definitions
            signal_pattern = r'SG_\s+(\w+)\s*:\s*(\d+)\|(\d+)@([01])([+-])\s*\(([^,]+),([^)]+)\)\s*\[([^\]]*)\]\s*"([^"]*)"\s*(\w+)'
            for match in re.finditer(signal_pattern, content):
                signal_name, start_bit, length, byte_order, sign, factor, offset, range_info, unit, receiver = match.groups()
                
                # Convert to PID-like definition
                pid_definitions.append({
                    'name': signal_name,
                    'start_bit': int(start_bit),
                    'length': int(length),
                    'factor': float(factor),
                    'offset': float(offset),
                    'unit': unit,
                    'range': range_info,
                    'source_file': str(file_path)
                })
            
            # Determine brand from file path
            brand = self._determine_brand_from_path(file_path)
            brand_specific_info['brand'] = brand
            brand_specific_info['file_type'] = 'dbc'
            
        except Exception as e:
            logger.error(f"Error parsing DBC file {file_path}: {e}")
        
        return ExtractedKnowledge(
            dtc_codes=dtc_codes,
            pid_definitions=pid_definitions,
            can_messages=can_messages,
            brand_specific_info=brand_specific_info,
            source_project=str(file_path.parent.name),
            extraction_timestamp=datetime.now()
        )
    
    async def _extract_from_sqlite(self, file_path: Path) -> ExtractedKnowledge:
        """Extract knowledge from SQLite database files"""
        dtc_codes = []
        pid_definitions = []
        can_messages = []
        brand_specific_info = {}
        
        try:
            conn = sqlite3.connect(file_path)
            cursor = conn.cursor()
            
            # Get table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
            
            # Look for DTC-related tables
            for table in tables:
                if any(keyword in table.lower() for keyword in ['dtc', 'fault', 'error', 'code']):
                    cursor.execute(f"SELECT * FROM {table} LIMIT 100")
                    columns = [description[0] for description in cursor.description]
                    rows = cursor.fetchall()
                    
                    for row in rows:
                        row_dict = dict(zip(columns, row))
                        dtc_codes.append({
                            'source_table': table,
                            'source_file': str(file_path),
                            **row_dict
                        })
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Error reading SQLite file {file_path}: {e}")
        
        brand = self._determine_brand_from_path(file_path)
        brand_specific_info['brand'] = brand
        brand_specific_info['file_type'] = 'sqlite'
        
        return ExtractedKnowledge(
            dtc_codes=dtc_codes,
            pid_definitions=pid_definitions,
            can_messages=can_messages,
            brand_specific_info=brand_specific_info,
            source_project=str(file_path.parent.name),
            extraction_timestamp=datetime.now()
        )
    
    async def _extract_from_json(self, file_path: Path) -> ExtractedKnowledge:
        """Extract knowledge from JSON files"""
        dtc_codes = []
        pid_definitions = []
        can_messages = []
        brand_specific_info = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Look for DTC codes in various JSON structures
            if isinstance(data, dict):
                for key, value in data.items():
                    if 'dtc' in key.lower() or 'fault' in key.lower():
                        if isinstance(value, dict):
                            for code, details in value.items():
                                dtc_codes.append({
                                    'code': code,
                                    'details': details,
                                    'source_file': str(file_path)
                                })
                        elif isinstance(value, list):
                            for item in value:
                                if isinstance(item, dict):
                                    dtc_codes.append({
                                        'source_file': str(file_path),
                                        **item
                                    })
            
        except Exception as e:
            logger.error(f"Error parsing JSON file {file_path}: {e}")
        
        brand = self._determine_brand_from_path(file_path)
        brand_specific_info['brand'] = brand
        brand_specific_info['file_type'] = 'json'
        
        return ExtractedKnowledge(
            dtc_codes=dtc_codes,
            pid_definitions=pid_definitions,
            can_messages=can_messages,
            brand_specific_info=brand_specific_info,
            source_project=str(file_path.parent.name),
            extraction_timestamp=datetime.now()
        )
    
    async def _extract_from_python(self, file_path: Path) -> ExtractedKnowledge:
        """Extract knowledge from Python files"""
        dtc_codes = []
        pid_definitions = []
        can_messages = []
        brand_specific_info = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Look for DTC code patterns in Python code
            dtc_pattern = r'["\']([PBUCD][0-9A-F]{4})["\']'
            for match in re.finditer(dtc_pattern, content):
                dtc_code = match.group(1)
                dtc_codes.append({
                    'code': dtc_code,
                    'source_file': str(file_path),
                    'extraction_method': 'regex_pattern'
                })
            
            # Look for PID patterns
            pid_pattern = r'["\']?(0x[0-9A-F]+|[0-9A-F]{2})["\']?\s*:\s*["\']([^"\']+)["\']'
            for match in re.finditer(pid_pattern, content):
                pid, description = match.groups()
                pid_definitions.append({
                    'pid': pid,
                    'description': description,
                    'source_file': str(file_path)
                })
            
        except Exception as e:
            logger.error(f"Error parsing Python file {file_path}: {e}")
        
        brand = self._determine_brand_from_path(file_path)
        brand_specific_info['brand'] = brand
        brand_specific_info['file_type'] = 'python'
        
        return ExtractedKnowledge(
            dtc_codes=dtc_codes,
            pid_definitions=pid_definitions,
            can_messages=can_messages,
            brand_specific_info=brand_specific_info,
            source_project=str(file_path.parent.name),
            extraction_timestamp=datetime.now()
        )
    
    async def _extract_from_text(self, file_path: Path) -> ExtractedKnowledge:
        """Extract knowledge from text files"""
        dtc_codes = []
        pid_definitions = []
        can_messages = []
        brand_specific_info = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Look for DTC codes in text
            dtc_pattern = r'([PBUCD][0-9A-F]{4})\s*[-:]\s*([^\n\r]+)'
            for match in re.finditer(dtc_pattern, content):
                code, description = match.groups()
                dtc_codes.append({
                    'code': code,
                    'description': description.strip(),
                    'source_file': str(file_path)
                })
            
        except Exception as e:
            logger.error(f"Error parsing text file {file_path}: {e}")
        
        brand = self._determine_brand_from_path(file_path)
        brand_specific_info['brand'] = brand
        brand_specific_info['file_type'] = 'text'
        
        return ExtractedKnowledge(
            dtc_codes=dtc_codes,
            pid_definitions=pid_definitions,
            can_messages=can_messages,
            brand_specific_info=brand_specific_info,
            source_project=str(file_path.parent.name),
            extraction_timestamp=datetime.now()
        )
    
    def _determine_brand_from_path(self, file_path: Path) -> str:
        """Determine vehicle brand from file path"""
        path_str = str(file_path).lower()
        
        brand_keywords = {
            'toyota': ['toyota', 'lexus', 'prius'],
            'bmw': ['bmw', 'ediabas', 'bimmer'],
            'vag': ['vag', 'volkswagen', 'audi', 'skoda', 'seat'],
            'renault': ['renault', 'dacia', 'pyren'],
            'nissan': ['nissan', 'infiniti'],
            'ford': ['ford', 'lincoln'],
            'gm': ['gm', 'chevrolet', 'cadillac', 'buick'],
            'hyundai': ['hyundai', 'kia', 'genesis'],
            'honda': ['honda', 'acura'],
            'tesla': ['tesla', 'model3', 'models'],
            'volvo': ['volvo'],
            'mercedes': ['mercedes', 'mb'],
            'opel': ['opel', 'vauxhall']
        }
        
        for brand, keywords in brand_keywords.items():
            if any(keyword in path_str for keyword in keywords):
                return brand
        
        return 'unknown'


# Global instance
knowledge_extractor = KnowledgeExtractor()
