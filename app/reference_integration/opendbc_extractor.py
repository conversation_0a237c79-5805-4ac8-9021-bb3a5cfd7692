"""
OpenDBC Knowledge Extractor

Extracts diagnostic knowledge from OpenDBC project DBC files.

Source Attribution:
- OpenDBC project (https://github.com/commaai/opendbc)
- DBC file format specifications
- Automotive CAN message standards
"""

import os
import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class DBCMessage:
    """Represents a CAN message from DBC file"""
    message_id: int
    name: str
    size: int
    sender: str
    signals: List['DBCSignal']
    comment: str = ""


@dataclass
class DBCSignal:
    """Represents a signal within a CAN message"""
    name: str
    start_bit: int
    size: int
    byte_order: str  # 'big_endian' or 'little_endian'
    value_type: str  # 'signed' or 'unsigned'
    factor: float
    offset: float
    minimum: float
    maximum: float
    unit: str
    receivers: List[str]
    comment: str = ""


@dataclass
class ExtractedDiagnosticData:
    """Container for extracted diagnostic data"""
    brand: str
    model: str
    year: Optional[str]
    messages: List[DBCMessage]
    diagnostic_pids: Dict[str, Any]
    dtc_mappings: Dict[str, str]
    ecu_addresses: Dict[str, str]


class OpenDBCExtractor:
    """
    Extracts diagnostic knowledge from OpenDBC DBC files
    
    Supports extraction of:
    - CAN message definitions
    - Signal mappings for diagnostic parameters
    - Brand-specific diagnostic protocols
    - ECU addressing schemes
    """
    
    def __init__(self, opendbc_path: str):
        self.opendbc_path = Path(opendbc_path)
        self.dbc_path = self.opendbc_path / "opendbc" / "dbc"
        
        # Brand mappings from DBC filenames
        self.brand_mappings = {
            'toyota': ['toyota_', 'prius_'],
            'bmw': ['bmw_'],
            'volkswagen': ['vw_'],
            'audi': ['vw_'],  # VAG group
            'ford': ['ford_'],
            'gm': ['gm_', 'cadillac_'],
            'honda': ['acura_', 'honda_'],
            'hyundai': ['hyundai_'],
            'nissan': ['nissan_'],
            'mazda': ['mazda_'],
            'tesla': ['tesla_'],
            'mercedes': ['mercedes_'],
            'volvo': ['volvo_']
        }
        
        # Diagnostic-relevant message patterns
        self.diagnostic_patterns = {
            'engine': ['engine', 'powertrain', 'ecu', 'dme'],
            'transmission': ['transmission', 'gear', 'egs'],
            'abs_esp': ['abs', 'esp', 'dsc', 'brake'],
            'airbag': ['airbag', 'srs', 'acsm'],
            'body': ['body', 'bcm', 'comfort'],
            'climate': ['climate', 'hvac', 'ac'],
            'instrument': ['instrument', 'cluster', 'kombi'],
            'steering': ['steering', 'eps', 'sas']
        }
    
    def extract_all_brands(self) -> Dict[str, ExtractedDiagnosticData]:
        """Extract diagnostic data for all supported brands"""
        extracted_data = {}
        
        if not self.dbc_path.exists():
            logger.error(f"OpenDBC path not found: {self.dbc_path}")
            return extracted_data
        
        # Find all DBC files
        dbc_files = list(self.dbc_path.glob("*.dbc"))
        logger.info(f"Found {len(dbc_files)} DBC files")
        
        for dbc_file in dbc_files:
            try:
                brand = self._identify_brand(dbc_file.name)
                if brand:
                    data = self.extract_from_dbc(str(dbc_file), brand)
                    if data:
                        if brand not in extracted_data:
                            extracted_data[brand] = data
                        else:
                            # Merge data if brand already exists
                            self._merge_diagnostic_data(extracted_data[brand], data)
                        
                        logger.info(f"Extracted data from {dbc_file.name} for {brand}")
            
            except Exception as e:
                logger.warning(f"Error processing {dbc_file.name}: {e}")
                continue
        
        return extracted_data
    
    def extract_from_dbc(self, dbc_file_path: str, brand: str) -> Optional[ExtractedDiagnosticData]:
        """Extract diagnostic data from a single DBC file"""
        try:
            with open(dbc_file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()
            
            messages = self._parse_dbc_messages(content)
            diagnostic_pids = self._extract_diagnostic_pids(messages)
            dtc_mappings = self._extract_dtc_mappings(content)
            ecu_addresses = self._extract_ecu_addresses(content)
            
            # Extract model and year from filename
            filename = Path(dbc_file_path).stem
            model, year = self._parse_model_year(filename)
            
            return ExtractedDiagnosticData(
                brand=brand,
                model=model,
                year=year,
                messages=messages,
                diagnostic_pids=diagnostic_pids,
                dtc_mappings=dtc_mappings,
                ecu_addresses=ecu_addresses
            )
        
        except Exception as e:
            logger.error(f"Error extracting from {dbc_file_path}: {e}")
            return None
    
    def _identify_brand(self, filename: str) -> Optional[str]:
        """Identify vehicle brand from DBC filename"""
        filename_lower = filename.lower()
        
        for brand, patterns in self.brand_mappings.items():
            for pattern in patterns:
                if pattern in filename_lower:
                    return brand
        
        return None
    
    def _parse_dbc_messages(self, content: str) -> List[DBCMessage]:
        """Parse CAN messages from DBC content"""
        messages = []
        
        # Regex pattern for message definitions
        message_pattern = r'BO_\s+(\d+)\s+(\w+):\s+(\d+)\s+(\w+)'
        signal_pattern = r'SG_\s+(\w+)\s*:\s*(\d+)\|(\d+)@([01])([+-])\s*\(([^,]+),([^)]+)\)\s*\[([^|]*)\|([^\]]*)\]\s*"([^"]*)"\s*(.*)'
        
        # Find all messages
        for message_match in re.finditer(message_pattern, content):
            msg_id = int(message_match.group(1))
            msg_name = message_match.group(2)
            msg_size = int(message_match.group(3))
            msg_sender = message_match.group(4)
            
            # Find signals for this message
            signals = []
            message_end = content.find('\nBO_', message_match.end())
            if message_end == -1:
                message_end = len(content)
            
            message_section = content[message_match.end():message_end]
            
            for signal_match in re.finditer(signal_pattern, message_section):
                signal = self._parse_signal(signal_match)
                if signal:
                    signals.append(signal)
            
            # Check if this message is diagnostic-relevant
            if self._is_diagnostic_relevant(msg_name, msg_id):
                messages.append(DBCMessage(
                    message_id=msg_id,
                    name=msg_name,
                    size=msg_size,
                    sender=msg_sender,
                    signals=signals
                ))
        
        return messages
    
    def _parse_signal(self, match) -> Optional[DBCSignal]:
        """Parse a signal definition from regex match"""
        try:
            name = match.group(1)
            start_bit = int(match.group(2))
            size = int(match.group(3))
            byte_order = 'big_endian' if match.group(4) == '0' else 'little_endian'
            value_type = 'signed' if match.group(5) == '-' else 'unsigned'
            factor = float(match.group(6))
            offset = float(match.group(7))
            minimum = float(match.group(8)) if match.group(8) else 0.0
            maximum = float(match.group(9)) if match.group(9) else 0.0
            unit = match.group(10)
            receivers = match.group(11).split(',') if match.group(11) else []
            
            return DBCSignal(
                name=name,
                start_bit=start_bit,
                size=size,
                byte_order=byte_order,
                value_type=value_type,
                factor=factor,
                offset=offset,
                minimum=minimum,
                maximum=maximum,
                unit=unit,
                receivers=[r.strip() for r in receivers if r.strip()]
            )
        
        except (ValueError, IndexError) as e:
            logger.debug(f"Error parsing signal: {e}")
            return None
    
    def _is_diagnostic_relevant(self, msg_name: str, msg_id: int) -> bool:
        """Check if message is relevant for diagnostics"""
        msg_name_lower = msg_name.lower()
        
        # Check for diagnostic patterns in name
        for category, patterns in self.diagnostic_patterns.items():
            for pattern in patterns:
                if pattern in msg_name_lower:
                    return True
        
        # Check for OBD2 message IDs
        obd2_ids = [0x7DF, 0x7E0, 0x7E1, 0x7E2, 0x7E3, 0x7E4, 0x7E5, 0x7E6, 0x7E7,
                   0x7E8, 0x7E9, 0x7EA, 0x7EB, 0x7EC, 0x7ED, 0x7EE, 0x7EF]
        
        if msg_id in obd2_ids:
            return True
        
        # Check for common diagnostic message patterns
        diagnostic_keywords = ['dtc', 'fault', 'error', 'status', 'diag', 'obd']
        for keyword in diagnostic_keywords:
            if keyword in msg_name_lower:
                return True
        
        return False
    
    def _extract_diagnostic_pids(self, messages: List[DBCMessage]) -> Dict[str, Any]:
        """Extract diagnostic PIDs from messages"""
        pids = {}
        
        for message in messages:
            for signal in message.signals:
                # Create PID-like entry for each diagnostic signal
                pid_key = f"{message.message_id:03X}_{signal.name}"
                
                pids[pid_key] = {
                    'name': signal.name,
                    'description': f"{signal.name} from {message.name}",
                    'unit': signal.unit,
                    'formula': f"value * {signal.factor} + {signal.offset}",
                    'min_value': signal.minimum,
                    'max_value': signal.maximum,
                    'message_id': message.message_id,
                    'start_bit': signal.start_bit,
                    'size': signal.size,
                    'byte_order': signal.byte_order,
                    'value_type': signal.value_type
                }
        
        return pids
    
    def _extract_dtc_mappings(self, content: str) -> Dict[str, str]:
        """Extract DTC mappings from DBC comments"""
        dtc_mappings = {}
        
        # Look for DTC-related comments
        comment_pattern = r'CM_\s+SG_\s+\d+\s+\w+\s+"([^"]*)"'
        
        for match in re.finditer(comment_pattern, content):
            comment = match.group(1).lower()
            if any(keyword in comment for keyword in ['dtc', 'fault', 'error', 'trouble']):
                # Extract potential DTC codes from comment
                dtc_pattern = r'[PBCU]\d{4}'
                dtc_codes = re.findall(dtc_pattern, comment.upper())
                
                for dtc_code in dtc_codes:
                    dtc_mappings[dtc_code] = comment
        
        return dtc_mappings
    
    def _extract_ecu_addresses(self, content: str) -> Dict[str, str]:
        """Extract ECU addresses from DBC file"""
        ecu_addresses = {}
        
        # Look for ECU definitions
        ecu_pattern = r'BU_:\s*(.*)'
        match = re.search(ecu_pattern, content)
        
        if match:
            ecus = match.group(1).split()
            for i, ecu in enumerate(ecus):
                if ecu != 'XXX':  # Skip placeholder ECUs
                    # Assign sequential addresses (this is simplified)
                    ecu_addresses[f"0x{(i+1)*16:02X}"] = ecu
        
        return ecu_addresses
    
    def _parse_model_year(self, filename: str) -> Tuple[str, Optional[str]]:
        """Parse model and year from filename"""
        # Extract year if present
        year_match = re.search(r'(\d{4})', filename)
        year = year_match.group(1) if year_match else None
        
        # Extract model name
        model = filename.replace('.dbc', '')
        
        # Clean up model name
        for brand_patterns in self.brand_mappings.values():
            for pattern in brand_patterns:
                if pattern.rstrip('_') in model:
                    model = model.replace(pattern.rstrip('_'), '').strip('_')
                    break
        
        if year:
            model = model.replace(year, '').strip('_')
        
        return model.replace('_', ' ').title(), year
    
    def _merge_diagnostic_data(self, existing: ExtractedDiagnosticData, new: ExtractedDiagnosticData):
        """Merge new diagnostic data into existing data"""
        existing.messages.extend(new.messages)
        existing.diagnostic_pids.update(new.diagnostic_pids)
        existing.dtc_mappings.update(new.dtc_mappings)
        existing.ecu_addresses.update(new.ecu_addresses)
    
    def get_extraction_summary(self, data: Dict[str, ExtractedDiagnosticData]) -> Dict[str, Any]:
        """Get summary of extracted data"""
        summary = {
            'brands_extracted': len(data),
            'total_messages': sum(len(d.messages) for d in data.values()),
            'total_pids': sum(len(d.diagnostic_pids) for d in data.values()),
            'total_dtc_mappings': sum(len(d.dtc_mappings) for d in data.values()),
            'brands': {}
        }
        
        for brand, brand_data in data.items():
            summary['brands'][brand] = {
                'models': [brand_data.model] if brand_data.model else [],
                'messages': len(brand_data.messages),
                'pids': len(brand_data.diagnostic_pids),
                'dtc_mappings': len(brand_data.dtc_mappings),
                'ecu_addresses': len(brand_data.ecu_addresses)
            }
        
        return summary
