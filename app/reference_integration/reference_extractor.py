"""
Reference Project Knowledge Extractor

Extracts diagnostic knowledge from various open-source automotive projects.

Source Attribution:
- pyren: Renault diagnostic procedures and DTC codes
- ddt4all: Multi-brand diagnostic database
- ediabaslib: BMW diagnostic protocols
- androbd: Android OBD2 application knowledge
- Various awesome-* repositories for automotive diagnostics
"""

import os
import json
import re
import logging
from typing import Dict, List, Optional, Any, Set
from pathlib import Path
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class ExtractedDTC:
    """Extracted DTC information"""
    code: str
    description: str
    system: str
    severity: str
    brand: str
    possible_causes: List[str]
    repair_hints: List[str]
    source_project: str
    ecu_address: Optional[str] = None
    freeze_frame_required: bool = False


@dataclass
class ExtractedPID:
    """Extracted PID information"""
    pid: str
    name: str
    description: str
    unit: str
    formula: str
    min_value: Optional[float]
    max_value: Optional[float]
    brand: str
    source_project: str
    category: str = "general"


@dataclass
class ExtractedProcedure:
    """Extracted diagnostic procedure"""
    name: str
    description: str
    steps: List[str]
    brand: str
    system: str
    source_project: str
    required_tools: List[str] = None


class ReferenceExtractor:
    """
    Extracts diagnostic knowledge from reference projects
    
    Supports extraction from:
    - pyren (Renault diagnostics)
    - ddt4all (Multi-brand diagnostics)
    - ediabaslib (BMW diagnostics)
    - androbd (Android OBD2)
    - awesome-automotive projects
    """
    
    def __init__(self, references_path: str):
        self.references_path = Path(references_path)
        self.extracted_dtcs: List[ExtractedDTC] = []
        self.extracted_pids: List[ExtractedPID] = []
        self.extracted_procedures: List[ExtractedProcedure] = []
        
        # Known project structures
        self.project_extractors = {
            'pyren': self._extract_from_pyren,
            'pyren-pyren3': self._extract_from_pyren,
            'ddt4all': self._extract_from_ddt4all,
            'ddt4all-master': self._extract_from_ddt4all,
            'ediabaslib-master': self._extract_from_ediabaslib,
            'androbd': self._extract_from_androbd,
            'awesome-automotive-can-id-main': self._extract_from_awesome_automotive,
            'awesome-canbus-main': self._extract_from_awesome_canbus,
            'toyota-sample-obd-data': self._extract_from_toyota_sample,
            'OBD-PIDs-for-HKMC-EVs-master': self._extract_from_ev_pids,
            'bmw-ediabas': self._extract_from_bmw_ediabas,
            'vag-uds': self._extract_from_vag_uds
        }
    
    def extract_all_references(self) -> Dict[str, Any]:
        """Extract knowledge from all reference projects"""
        results = {
            'dtcs': [],
            'pids': [],
            'procedures': [],
            'summary': {}
        }
        
        if not self.references_path.exists():
            logger.error(f"References path not found: {self.references_path}")
            return results
        
        # Process each reference project
        for project_dir in self.references_path.iterdir():
            if project_dir.is_dir() and project_dir.name in self.project_extractors:
                try:
                    logger.info(f"Extracting from {project_dir.name}")
                    extractor = self.project_extractors[project_dir.name]
                    extractor(project_dir)
                except Exception as e:
                    logger.warning(f"Error extracting from {project_dir.name}: {e}")
                    continue
        
        # Compile results
        results['dtcs'] = [asdict(dtc) for dtc in self.extracted_dtcs]
        results['pids'] = [asdict(pid) for pid in self.extracted_pids]
        results['procedures'] = [asdict(proc) for proc in self.extracted_procedures]
        results['summary'] = self._generate_summary()
        
        logger.info(f"Extraction complete: {len(self.extracted_dtcs)} DTCs, "
                   f"{len(self.extracted_pids)} PIDs, {len(self.extracted_procedures)} procedures")
        
        return results
    
    def _extract_from_pyren(self, project_path: Path):
        """Extract knowledge from pyren project"""
        # Look for Python files with DTC definitions
        for py_file in project_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract DTC patterns
                self._extract_dtc_patterns_from_code(content, 'renault', 'pyren')
                
                # Extract Renault-specific procedures
                self._extract_renault_procedures(content)
                
            except Exception as e:
                logger.debug(f"Error reading {py_file}: {e}")
                continue
    
    def _extract_from_ddt4all(self, project_path: Path):
        """Extract knowledge from ddt4all project"""
        # Look for JSON/XML files with diagnostic data
        for data_file in project_path.rglob("*.json"):
            try:
                with open(data_file, 'r', encoding='utf-8', errors='ignore') as f:
                    data = json.load(f)
                
                # Extract DTCs from JSON structure
                self._extract_dtcs_from_json(data, 'multi-brand', 'ddt4all')
                
            except Exception as e:
                logger.debug(f"Error reading {data_file}: {e}")
                continue
        
        # Look for XML files
        for xml_file in project_path.rglob("*.xml"):
            try:
                self._extract_from_xml_file(xml_file, 'multi-brand', 'ddt4all')
            except Exception as e:
                logger.debug(f"Error reading {xml_file}: {e}")
                continue
    
    def _extract_from_ediabaslib(self, project_path: Path):
        """Extract knowledge from ediabaslib (BMW diagnostics)"""
        # Look for configuration files and scripts
        for config_file in project_path.rglob("*.cfg"):
            try:
                with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract BMW-specific DTCs and procedures
                self._extract_bmw_diagnostics(content)
                
            except Exception as e:
                logger.debug(f"Error reading {config_file}: {e}")
                continue
    
    def _extract_from_androbd(self, project_path: Path):
        """Extract knowledge from androbd project"""
        # Look for Java/Kotlin files with OBD definitions
        for java_file in project_path.rglob("*.java"):
            try:
                with open(java_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract PID definitions
                self._extract_pid_definitions(content, 'generic', 'androbd')
                
            except Exception as e:
                logger.debug(f"Error reading {java_file}: {e}")
                continue
    
    def _extract_from_awesome_automotive(self, project_path: Path):
        """Extract knowledge from awesome-automotive-can-id project"""
        # Look for markdown files with CAN ID definitions
        for md_file in project_path.rglob("*.md"):
            try:
                with open(md_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract CAN ID mappings
                self._extract_can_id_mappings(content)
                
            except Exception as e:
                logger.debug(f"Error reading {md_file}: {e}")
                continue
    
    def _extract_from_awesome_canbus(self, project_path: Path):
        """Extract knowledge from awesome-canbus project"""
        # Similar to awesome-automotive but focused on CAN bus
        for readme_file in project_path.rglob("README.md"):
            try:
                with open(readme_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract tool references and protocols
                self._extract_protocol_info(content)
                
            except Exception as e:
                logger.debug(f"Error reading {readme_file}: {e}")
                continue
    
    def _extract_from_toyota_sample(self, project_path: Path):
        """Extract knowledge from Toyota sample OBD data"""
        # Look for data files with Toyota-specific information
        for data_file in project_path.rglob("*.txt"):
            try:
                with open(data_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract Toyota-specific PIDs and DTCs
                self._extract_toyota_specific_data(content)
                
            except Exception as e:
                logger.debug(f"Error reading {data_file}: {e}")
                continue
    
    def _extract_from_ev_pids(self, project_path: Path):
        """Extract knowledge from EV-specific PID project"""
        # Look for EV-specific diagnostic data
        for data_file in project_path.rglob("*.csv"):
            try:
                with open(data_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract EV-specific PIDs
                self._extract_ev_pids(content)
                
            except Exception as e:
                logger.debug(f"Error reading {data_file}: {e}")
                continue
    
    def _extract_from_bmw_ediabas(self, project_path: Path):
        """Extract knowledge from BMW EDIABAS project"""
        # Look for EDIABAS script files
        for script_file in project_path.rglob("*.prg"):
            try:
                with open(script_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract BMW diagnostic procedures
                self._extract_ediabas_procedures(content)
                
            except Exception as e:
                logger.debug(f"Error reading {script_file}: {e}")
                continue
    
    def _extract_from_vag_uds(self, project_path: Path):
        """Extract knowledge from VAG UDS project"""
        # Look for UDS protocol definitions
        for py_file in project_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Extract VAG-specific UDS commands and DTCs
                self._extract_vag_uds_data(content)
                
            except Exception as e:
                logger.debug(f"Error reading {py_file}: {e}")
                continue
    
    def _extract_dtc_patterns_from_code(self, content: str, brand: str, source: str):
        """Extract DTC patterns from source code"""
        # Look for DTC code patterns
        dtc_patterns = [
            r'["\']([PBCU]\d{4})["\']',  # Standard DTC format
            r'["\']([DF]\d{3})["\']',    # Renault DF codes
            r'["\']([B]\d{4})["\']',     # Body codes
            r'["\']([C]\d{4})["\']',     # Chassis codes
            r'["\']([U]\d{4})["\']'      # Network codes
        ]
        
        for pattern in dtc_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for dtc_code in matches:
                # Try to find description nearby
                description = self._find_nearby_description(content, dtc_code)
                
                self.extracted_dtcs.append(ExtractedDTC(
                    code=dtc_code.upper(),
                    description=description or f"DTC {dtc_code} from {source}",
                    system="Unknown",
                    severity="medium",
                    brand=brand,
                    possible_causes=[],
                    repair_hints=[],
                    source_project=source
                ))
    
    def _extract_pid_definitions(self, content: str, brand: str, source: str):
        """Extract PID definitions from source code"""
        # Look for PID patterns
        pid_pattern = r'(?:PID|pid)[_\s]*[=:]\s*["\']?0x([0-9A-Fa-f]{2})["\']?'
        matches = re.findall(pid_pattern, content)
        
        for pid_hex in matches:
            pid_name = self._find_pid_name(content, pid_hex)
            
            self.extracted_pids.append(ExtractedPID(
                pid=f"0x{pid_hex.upper()}",
                name=pid_name or f"PID_{pid_hex}",
                description=f"PID 0x{pid_hex} from {source}",
                unit="unknown",
                formula="raw_value",
                min_value=None,
                max_value=None,
                brand=brand,
                source_project=source
            ))
    
    def _find_nearby_description(self, content: str, code: str) -> Optional[str]:
        """Find description text near a DTC code"""
        # Look for text within 200 characters of the code
        code_pos = content.find(code)
        if code_pos == -1:
            return None
        
        start = max(0, code_pos - 100)
        end = min(len(content), code_pos + 100)
        context = content[start:end]
        
        # Look for quoted strings that might be descriptions
        desc_pattern = r'["\']([^"\']{10,80})["\']'
        matches = re.findall(desc_pattern, context)
        
        for match in matches:
            if code not in match and len(match.split()) > 2:
                return match
        
        return None
    
    def _find_pid_name(self, content: str, pid_hex: str) -> Optional[str]:
        """Find name associated with a PID"""
        # Look for variable names or comments near PID definition
        pid_pos = content.find(pid_hex)
        if pid_pos == -1:
            return None
        
        start = max(0, pid_pos - 50)
        end = min(len(content), pid_pos + 50)
        context = content[start:end]
        
        # Look for identifier patterns
        name_pattern = r'(\w+)(?:_PID|_pid|\s*=)'
        matches = re.findall(name_pattern, context)
        
        if matches:
            return matches[0].replace('_', ' ').title()
        
        return None
    
    def _extract_dtcs_from_json(self, data: Any, brand: str, source: str):
        """Extract DTCs from JSON data structure"""
        if isinstance(data, dict):
            for key, value in data.items():
                if key.lower() in ['dtc', 'dtcs', 'fault_codes', 'errors']:
                    if isinstance(value, dict):
                        for dtc_code, dtc_info in value.items():
                            if isinstance(dtc_info, dict):
                                self.extracted_dtcs.append(ExtractedDTC(
                                    code=dtc_code,
                                    description=dtc_info.get('description', ''),
                                    system=dtc_info.get('system', 'Unknown'),
                                    severity=dtc_info.get('severity', 'medium'),
                                    brand=brand,
                                    possible_causes=dtc_info.get('causes', []),
                                    repair_hints=dtc_info.get('repairs', []),
                                    source_project=source
                                ))
                else:
                    self._extract_dtcs_from_json(value, brand, source)
        elif isinstance(data, list):
            for item in data:
                self._extract_dtcs_from_json(item, brand, source)
    
    def _extract_from_xml_file(self, xml_file: Path, brand: str, source: str):
        """Extract data from XML file (simplified)"""
        try:
            with open(xml_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Simple XML parsing for DTC codes
            dtc_pattern = r'<(?:dtc|fault|error)[^>]*code=["\']([^"\']+)["\'][^>]*>([^<]*)</(?:dtc|fault|error)>'
            matches = re.findall(dtc_pattern, content, re.IGNORECASE)
            
            for dtc_code, description in matches:
                self.extracted_dtcs.append(ExtractedDTC(
                    code=dtc_code,
                    description=description.strip(),
                    system="Unknown",
                    severity="medium",
                    brand=brand,
                    possible_causes=[],
                    repair_hints=[],
                    source_project=source
                ))
        
        except Exception as e:
            logger.debug(f"Error parsing XML {xml_file}: {e}")
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate summary of extracted data"""
        brands = set(dtc.brand for dtc in self.extracted_dtcs)
        brands.update(pid.brand for pid in self.extracted_pids)
        
        return {
            'total_dtcs': len(self.extracted_dtcs),
            'total_pids': len(self.extracted_pids),
            'total_procedures': len(self.extracted_procedures),
            'brands_covered': list(brands),
            'source_projects': list(set(dtc.source_project for dtc in self.extracted_dtcs))
        }
    
    # Placeholder methods for specific extractors (to be implemented)
    def _extract_renault_procedures(self, content: str): pass
    def _extract_bmw_diagnostics(self, content: str): pass
    def _extract_can_id_mappings(self, content: str): pass
    def _extract_protocol_info(self, content: str): pass
    def _extract_toyota_specific_data(self, content: str): pass
    def _extract_ev_pids(self, content: str): pass
    def _extract_ediabas_procedures(self, content: str): pass
    def _extract_vag_uds_data(self, content: str): pass
