body {
   -webkit-print-color-adjust:exact;
}
@page {
   size: 21cm 29.7cm;   /*A4*/
   margin: 1px; /*webkit says no*/
}
html{
   margin: 0;
   padding: 0 ;
   position: relative;
   width: 100%;
}
body {
   margin: 0;
   padding: 0;
   width: 100%;
}
@media all {
   .page-break { display: none; }
}
@media print {
   .page-break { display: none; }
   margin: 5mm;
   div.page {
      margin: 0mm;
   }
}
table{
   font-size :14px;
}
th{
   padding-bottom :8px;
   font-weight: bold;
   border-bottom : 1px solid ;
}
table.dtc_table{
   width:90%;
   margin :auto;
   padding:10px;
   border : 1px solid;
}
td.dtc_idx {
   vertical-align: top;
   width:5%;
   text-align: center;
}
td.dtc_code {
   vertical-align: top;
   width:15%;
   text-align: center;
}
td.dtc_desc {
   width:80%;
   text-align :start;
}
td.o2s_pid{
   width:8%;
   text-align: center;
}
th.o2s_pid{
   width:8%;
}
tr.o2s_desc{
   width:40%;
   text-align: start;
}
th.o2s_desc{
   width:40%;
   text-align: start;
}
td.o2s_value{
   width:10%;
   text-align: center;
}
th.o2s_value{
   width:10%;
}
td.o2s_min {
   width:10%;
   text-align: center;
}
th.o2s_min {
   width:10%;
}
td.o2s_max {
   width:10%;
   text-align: center;
}
th.o2s_max {
   width:10%;
}
td.o2s_unit{
   width:10%;
   text-align: center;
}
th.o2s_unit {
   width:10%;
}
td.o2s_result {
   width:12%;
   text-align: center;
}
th.o2s_result {
   width:12%;
}
td.obdmid_pid {
   width:8%;
   text-align: center;
}
th.obdmid_pid {
   width:8%;
}
td.obdmid_tid {
   width:8%;
   text-align: center;
}
th.obdmid_tid {
   width:8%;
}
td.obdmid_desc {
   width:32%;
   text-align: start;
}
th.obdmid_desc {
   width:32%;
   text-align: start;
}
td.obdmid_value {
   width:10%;
   text-align: center;
}
th.obdmid_value {
   width:10%;
}
td.obdmid_min {
   width:10%;
   text-align: center;
}
th.obdmid_min {
   width:10%;
}
td.obdmid_max {
   width:10%;
   text-align: center;
}
th.obdmid_max {
   width:10%;
}
td.obdmid_unit {
   width:10%;
   text-align: center;
}
th.obdmid_unit {
   width:10%;
}
td.obdmid_result {
   text-align: center;
}
th.obdmid_result {
   width:12%;
}
#contenu{
   width : auto;
   margin  : auto;
   font-size : 14px;
}
#corps{
   width : auto;
   margin  : auto;
}
#corps h1 {
   font-size : 25px;
   font-weight : bold;
   border : 1px solid;
   text-align : center;
}
#corps h2 {
   font-size : 20px;
   font-weight : bold;
   text-align : start;
   padding-left : 5px;
   line-height : 25px;
}
#corps h3 {
   font-size : 17px;
   font-weight : bold;
   text-decoration : underline;
   padding-left : 10px;
   margin-top : 25px;
}
#corps h4 {
   font-size : 15px;
   font-weight : bold;
   padding-left : 15px;
}
table.entete {
   width:960px;
   margin:auto;
   font-size : 17px;
}
td.columnCompanyName  {
   align: start;
   line-height: 20px;
   padding-left: 10px;
   padding-right: 10px;
   width: 50%;
}
td.columnCompanyLogo {
   align: end;
   padding-left: 10px;
   padding-right: 10px;
   width: 50%;
}
table.status_table{
   width:90%;
   margin :auto;
   padding:10px;
}
td.columnStatus1 {
   width:150px;
}
td.columnStatus2 {
   align: start;
   vertical-align: top;
}
div.columnStatusHeader {
   padding-left : 10px;
   font-size : 15px;
   font-weight : bold;
   text-decoration : underline;
}
div.columnStatusDesc {
   padding-left : 25px;
   font-size : 15px;
}
span.NomSociete {
   font-size : 22px;
   line-height : 30px;
}
table.VehiculeMoteur{
   width:960px;
   margin :auto;
}
.VehiculeMoteur td{
   width : 480px;
}