  \    �            l                 +   0   >   K   Z   n   }   �   �   �   �   �   �     /  9  B  		ellipsize layout_gravity orientation id paddingLeft 

paddingTop paddingRight fitsSystemWindows layout_width 

layout_height 

singleLine 

layout_weight 

textAlignment TextView android 00androidx.appcompat.widget.FitWindowsLinearLayout **http://schemas.android.com/apk/res/android include layout style   � <   � � � � � � � � � � ]��        ����       t      ��������                  ����           ����  ����      ����  ����   	   ����  ����    (   ��������
                 ����           ����   �       ����  �	      ����  c      ����  %       ����  c      ����  ����   	   ����  ����   
   ����  ����      ����     ����   ����  [     (   ��������
    t   .   ��������                  ����  ����   	   ����  ����      ����    �?����   ����       .   ��������          ��������          ����      