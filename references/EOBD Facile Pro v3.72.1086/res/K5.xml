  T    �            t           
         +   8   G   W   d   s   �   �   �   �   �   �   �   �   �     3  `  gravity orientation id paddingLeft 

paddingTop paddingRight 

paddingBottom 

visibility layout_width 

layout_height fillViewport 

layout_weight layoutDirection paddingStart 

paddingEnd Button 

ScrollView android android.widget.Space ))androidx.appcompat.widget.ButtonBarLayout **http://schemas.android.com/apk/res/android style � D   � � � � � � � � � � z����        ����       �      ��������                ����  � 	      ����  ����   	   ����  ����   
   ����  ��������   ����  �     %   ��������                   ����  P         ����            ����          ����          ����          ����          ����  ����   	   ����  ����      ����        
   ����          ����     t   +   ��������                ����         ����  ����   	   ����  ��������   ����  �     +   ��������    �   2   ��������                 ����  �	      ����  �      ����        	   ����           ����    �?    2   ��������    t   8   ��������                ����         ����  ����   	   ����  ��������   ����  �     8   ��������    t   >   ��������                ����         ����  ����   	   ����  ��������   ����  �     >   ��������       %   ��������          ��������          ����      