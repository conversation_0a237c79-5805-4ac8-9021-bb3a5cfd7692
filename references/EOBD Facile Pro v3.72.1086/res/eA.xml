  L    �            x              "   0   5   B   O   f   u   �   �   �   �   �   �   �   �   �         J  w  textAppearance layout_gravity orientation id 

background 

visibility duplicateParentState layout_width 

layout_height layout_marginTop layout_marginBottom 		scaleType minWidth 

singleLine 

layout_weight 		ImageView LinearLayout Space TextView android --androidx.appcompat.view.menu.ListMenuItemView **http://schemas.android.com/apk/res/android style  � D   4 � � � � � � � � � � ?]�        ����       t      ��������                  ����           ����  ����      ����  ����      ����  �   �   !   ��������                 ����  l	      ����  L       ����  ����      ����       	   ����       
   ����        !   ��������    �   (   ��������                ����  � 	      ����  ����      ����  ����      ����  �����   ����  R �   4   ��������                 ����        ����           ����  �	      ����  ����      ����  ����      ����  ����   
   ����  ��������   ����  W    4   ��������    `   9   ��������                  ����           ����          ����    �?    9   ��������    �   C   ��������                 ����  (      ����           ����  w	      ����  ����      ����  ����      ����  ����   
   ����  ��������   ����  T    C   ��������    �   L   ��������                ����           ����  �	      ����           ����  ����      ����  ����      ����     ����   ����  U    L   ��������       (   ��������          ��������          ����      