0x021200,Energy-saving mode active
0x02FF12,Fault memory entry: only for Test
0x100001,"Throttle valve, function: jammed briefly"
0x100101,"Throttle valve, function: jammed permanently"
0x100201,"Throttle valve, function: stiff, too slow"
0x100210,"Throttle Actuator, Position Monitoring: The Positional Deviation"
0x100A02,"Throttle valve, throttle valve potentiometer 1 and 2: double error"
0x100A10,"Throttle valve, throttle valve potentiometer 1 and 2: group error"
0x100C08,"Throttle valve, throttle potentiometer 1: Signal flap implausible to air mass"
0x100E08,"Throttle valve, throttle valve potentiometer 2: Signal implausible to mass air"
0x101001,"Throttle valve, throttle valve potentiometer 1, electrical: short circuit to Plus"
0x101002,"Throttle valve, throttle valve potentiometer 1, electrical: short circuit to ground"
0x101201,"Throttle valve, throttle potentiometer 2, electrical: short circuit to Plus"
0x101202,"Throttle valve, throttle potentiometer 2, electrical: short circuit to ground"
0x101401,"Throttle valve, adaptation: marginal conditions not met"
0x101402,"Throttle valve, adaptation: Notluftposition not adapted"
0x101408,"Throttle valve, adaptation: initial adaptation, lower stop not learned"
0x101410,"Throttle valve, adaptation: marginal conditions not met, the battery voltage is too low"
0x101C08,"Throttle valve, throttle valve potentiometer, plausibility: synchronism error between the Potentiometer 1 and 2"
0x101F01,"Throttle angle - absolute pressure, intake manifold, comparison: pressure too high"
0x101F02,"Throttle angle - absolute pressure, intake manifold, comparison: pressure too low"
0x102001,"Air mass, plausibility: air mass too high"
0x102002,"Air mass, plausibility: air mass too low"
0x102010,"Mass air flow sensor, plausibility: air mass compared with model too high"
0x102011,"Mass air flow sensor, plausibility: air mass compared with model too low"
0x102610,"Air-mass sensor, Signal: Implausible period duration, loose contact with low frequency"
0x102611,"Air-mass sensor, Signal: Implausible period duration, loose contact with high frequency"
0x102612,"Air mass meter, Signal: short circuit or line interruption"
0x102801,"Mass air flow sensor, working range: length of period is too low, the air mass too low"
0x102A01,"Air-mass sensor, Signal: electrical fault"
0x102A02,"Mass air flow sensor, working range: length of period too large, air mass too high"
0x103001,"Accelerator pedal module, pedal-travel sensor 1, electrical: short circuit to Plus"
0x103002,"Accelerator pedal module, pedal-travel sensor 1, electrical: short circuit to ground"
0x103101,"Accelerator pedal module, pedal-travel sensor 2, electrical: short circuit to Plus"
0x103102,"Accelerator pedal module, pedal-travel sensor 2, electrical: short circuit to ground"
0x103308,"Accelerator pedal module, pedal-travel sensor, plausibility: synchronism error between the pedal value sensor 1 pedal value sensor 2"
0x10351C,"Accelerator Pedal Module, Pedal-Travel Sensor: Collective Error"
0x104301,"Absolute pressure sensor, intake manifold, plausibility, overrun: pressure too high"
0x104302,"Absolute pressure sensor, intake manifold, plausibility, overrun: pressure too low"
0x104401,"Absolute pressure sensor, intake pipe, electrical: short circuit to Plus"
0x104402,"Absolute pressure sensor, intake pipe, electrical: short circuit to ground"
0x104610,"Absolute pressure sensor, intake manifold, plausibility: intake-manifold pressure too high"
0x104611,"Absolute pressure sensor, intake manifold, plausibility: intake-manifold pressure too low"
0x104A40,"Absolute pressure sensor, intake pipe, electrical: short circuit to Plus"
0x104B01,"Absolute Pressure Sensor, Suction Pipe: Collective Error"
0x105001,DME: internal error [ambient pressure sensor: short circuit to Plus]
0x105002,DME: internal error [ambient pressure sensor: short circuit to ground]
0x105101,"Ambient pressure, operating range: pressure too high"
0x105102,"Ambient pressure, operating range: pressure too low"
0x105201,"DME: internal error [ambient pressure sensor, plausibility: pressure too high in the Wake]"
0x105202,"DME: internal error [ambient pressure sensor, plausibility: pressure too low in the Wake]"
0x105A30,DME: internal error [ambient pressure sensor: collective error]
0x105A40,"DME: internal error [ambient pressure sensor, operating range: pressure too high]"
0x105A41,"DME: internal error [ambient pressure sensor, operating range: pressure too low]"
0x105A42,"DME: internal error [ambient pressure sensor, plausibility: pressure implausible]"
0x105A43,"DME: internal error [ambient pressure sensor, plausibility: pressure implausible]"
0x107801,Tuning protection: air mass too high
0x107A22,"Throttle valve, throttle potentiometer 1: Signal flap implausible to replacement value of filling"
0x107A30,"Throttle valve, throttle potentiometer 2, electrical: short circuit to Plus"
0x107A31,"Throttle valve, throttle potentiometer 2, electrical: short circuit to ground"
0x107A32,"Throttle valve, throttle potentiometer 2: Signal flap implausible to replacement value of filling"
0x107A40,"Throttle potentiometers: throttle valve potentiometer 1 or 2, function"
0x107A50,Throttle valve: emergency active
0x107A70,"Throttle Valve, Activation: Short Circuit"
0x107A71,"Throttle, control: over-temperature or current is too high"
0x107A72,"DME, internal fault, activation of throttle valve: internal communication error"
0x107A73,"Throttle Valve, Activation: Line Break"
0x107A80,"Throttle valve, closing spring test: crash test, spring closes "
0x107A81,"Throttle valve, closing spring test: malfunction"
0x107A90,"Throttle valve opening spring test: crash test, spring does not open "
0x107A91,Throttle valve opening spring test malfunction
0x107AE0,"Throttle valve, adaptation: re-learn, lower stop not learned"
0x107AF0,"Throttle Valve, Amplifier Balance: Malfunction"
0x107C10,"Load control, the plausibility of the mass flow is too high"
0x108001,"Intake air temperature sensor, electrical: short circuit to Plus"
0x108002,"Intake air temperature sensor, electrical: short circuit to ground"
0x108010,Intake air temperature sensor signal modification: to quickly
0x108920,Charge-Air Temperature Sensor: Collective Error
0x108930,Charge-Air Temperature Sensor: Collective Error
0x108932,Intake Air Temperature Sensor: Collective Error
0x108A01,"Charge-air temperature sensor, electrical: short circuit to Plus"
0x108A02,"Charge-air temperature sensor, electrical: short circuit to ground"
0x108A10,"Charge-air temperature sensor, voltage change: too fast"
0x108C01,"Charge-air temperature, plausibility: temperature too high"
0x108C08,"Charge-air temperature, Signal: fixed lying"
0x108F01,Intake air system: a suspected leak between turbocharger and intake valves
0x109001,"Coolant temperature sensor, electrical: short circuit to Plus"
0x109002,"Coolant temperature sensor, electrical: short circuit to ground"
0x109208,"Coolant temperature sensor, Signal: fixed lying on low"
0x109308,Coolant temperature sensor signal modification: to quickly
0x10AA20,"Coolant temperature sensor, plausibility, cold start: temperature too high"
0x10AA21,"Coolant temperature sensor, plausibility, cold start: temperature too low"
0x10AA30,Coolant Temperature Sensor: Collective Error
0x10AA40,FlexRay message (coolant temperature sensor): missing
0x10AA50,"Coolant temperature sensor, Signal: fixed lying on high"
0x10AA51,"Coolant temperature sensor, Signal: fixed lying on low"
0x10AA52,"Coolant temperature sensor, Signal: fixed lying"
0x10B101,Outdoor temperature sensor: short circuit to Plus
0x10B102,Outdoor temperature sensor: short circuit to ground
0x10B104,"Outside temperature sensor, Signal: CAN message errors"
0x10BA20,"Outside temperature sensor, Signal: Upper threshold exceeded"
0x10BA21,"Outside temperature sensor, Signal: Lower threshold value is undershot"
0x10BA22,"Outside temperature sensor, Signal: CAN message errors"
0x10BA30,Outdoor temperature sensor collective error: electric and plausibility
0x10BA40,"Outside temperature sensor, plausibility: ambient temperature is greater than the model temperature"
0x10BA41,"Outside temperature sensor, plausibility: ambient temperature less than model temperature"
0x10BA42,"Intake air temperature sensor, plausibility, cold start: temperature too high"
0x10BA43,"Intake air temperature sensor, plausibility, cold start: temperature too low"
0x10BA48,"Intake air temperature charge-air temperature, comparison: intake air temperature too high"
0x10BA49,"Intake air temperature charge-air temperature, comparison: intake air temperature too low"
0x10BA4A,"Charge-air temperature sensor, plausibility, cold start: temperature too high"
0x10BA4B,"Charge-air temperature sensor, plausibility, cold start: temperature too low"
0x10BA4F,"Charge-air temperature sensor, plausibility: charge air temperature too high"
0x10BA51,"Charge-Air Temperature Sensor, Cold Start: Bus Error"
0x10BA52,Charge-Air Temperature Sensor: Collective Error
0x10C001,"Charge-air temperature sensor, signal modification: to quickly"
0x10C004,"Charge-air temperature sensor, plausibility, cold start: temperature too high"
0x10C005,"Charge-air temperature sensor, signal modification: to quickly"
0x110001,Cylinder injection switch-off: Katschutz due to low pressure in the fuel System
0x110101,"Injector, cylinder 1, activation: high voltage side; short circuit to ground"
0x110102,"Injector, cylinder 1, activation: low voltage side; short circuit to Plus"
0x110104,"Injector, cylinder 1, activation: high voltage side; short circuit to Plus"
0x110108,"Injector, cylinder 1, activation: low voltage side; short circuit to ground"
0x110201,"Injector, cylinder 2, activation: high voltage side; short circuit to ground"
0x110202,"Injector, cylinder 2, activation: low voltage side; short circuit to Plus"
0x110204,"Injector, cylinder 2, activation: high voltage side; short circuit to Plus"
0x110208,"Injector, cylinder 2, activation: low voltage side; short circuit to ground"
0x110301,"Injector, cylinder 3, activation: high voltage side; short circuit to ground"
0x110302,"Injector, cylinder 3, activation: low voltage side; short circuit to Plus"
0x110304,"Injector, cylinder 3, activation: high voltage side; short circuit to Plus"
0x110308,"Injector, cylinder 3, activation: low voltage side; short circuit to ground"
0x110401,"Injector, cylinder 4, activation: high voltage side; short circuit to ground"
0x110402,"Injector, cylinder 4, activation: low voltage side; short circuit to Plus"
0x110404,"Injector, cylinder 4, activation: high voltage side; short circuit to Plus"
0x110408,"Injector, cylinder 4, activation: low voltage side; short circuit to ground"
0x110501,"Injector, cylinder 5, activation: high voltage side; short circuit to ground"
0x110502,"Injector, cylinder 5, activation: low voltage side; short circuit to Plus"
0x110504,"Injector, cylinder 5, activation: high voltage side; short circuit to Plus"
0x110508,"Injector, cylinder 5, activation: low voltage side; short circuit to ground"
0x110601,"Injector, cylinder 6, activation: high voltage side; short circuit to ground"
0x110602,"Injector, cylinder 6, activation: low voltage side; short circuit to Plus"
0x110604,"Injector, cylinder 6, activation: high voltage side; short circuit to Plus"
0x110608,"Injector, cylinder 6, activation: low voltage side; short circuit to ground"
0x111020,"Injector-Cylinder 1 High-Voltage Side, Activation: Interturn"
0x111021,"Injector Cylinder 2 And The High Voltage Side, Activation: Interturn"
0x111022,"Injector Cylinder 3 High-Voltage Side, Activation: Interturn"
0x111023,"Injector Cylinder 4 High-Voltage Side, Activation: Interturn"
0x111024,"Injector, Cylinder 5 High-Voltage Side, Activation: Interturn"
0x111025,"Injector Cylinder 6 High-Voltage Side, Activation: Interturn"
0x111030,"Injector cylinder 1, increase in Power: too slow"
0x111031,"Injector, cylinder 2, current rise: too slow"
0x111032,"Injector, cylinder 3, current rise: too slow"
0x111033,"Injector, cylinder 4, current rise: too slow"
0x111034,"Injector, cylinder 5, current rise: too slow"
0x111035,"Injector cylinder 6, the current rise too slow"
0x111040,"Injector-Cylinder 1 Low-Voltage Side, Activation: Line Break"
0x111041,"Injector Cylinder 2 And The Low-Voltage Side, Activation: Line Break"
0x111042,"Injector Cylinder 3 Low-Voltage Side, Activation: Line Break"
0x111043,"Injector, Cylinder 4 Low Voltage Side, Activation: Line Break"
0x111044,"Injector-Cylinder 5 Low-Voltage Side, Activation: Line Break"
0x111045,"Injector, Cylinder 6 Low Voltage Side, Activation: Line Break"
0x111050,"Injector cylinder 1, mechanical: valve is stuck open"
0x111051,"Injector cylinder 2, mechanical: valve is stuck open"
0x111052,"Injector cylinder 3, mechanical: valve is stuck open"
0x111053,"Injector cylinder 4, mechanical: valve is stuck open"
0x111054,"Injector cylinder 5, mechanical: valve is stuck open"
0x111055,"Injector cylinder 6, mechanical: valve is stuck open"
0x111060,"Injector, cylinder 1 and 2, electric: fuel injection valve 1 or 2 is stuck open"
0x111061,"Injector, cylinder 3 and 4, electrical: fuel injection valve 3 or 4 is stuck open"
0x111110,"DME, internal fault, HDEV-output stage module 1: SPI communication error"
0x111111,"DME, internal fault, HDEV output stages module 2: SPI communication error"
0x111112,"DME, internal fault, HDEV-output stage module 1: SPI communication implausible"
0x111113,"DME, internal fault, HDEV output stages module 2: SPI communication implausible"
0x111114,"DME, internal fault, HDEV-output stage module 1: SPI communication failed"
0x111115,"DME, internal fault, HDEV output stages module 2: SPI-disturbed communication "
0x111116,"Injector cylinder 5 and 6, electrical: fuel injection valve 5 or 6 is stuck open"
0x111117,"Injector cylinder 7 and 8, electrical: fuel injection valve 7 or 8 is stuck open"
0x113025,"Relay, ignition, and injectors, supply voltage, injection: short circuit to Plus"
0x113026,"Relay, ignition, and injectors, supply voltage, injection: short-circuit after mass"
0x113027,"Relay, ignition, and injectors, supply voltage, injection cable disruption"
0x118001,Mixture regulation: mixture too lean
0x118002,Mixture regulation: mixture too fat
0x118401,"Mixture regulation: mixture too lean, large deviation"
0x118402,"Mixture regulation: mixture too rich, large deviation"
0x118601,"Lambda probe front catalyst, mixture fine tuning: exhaust gas after the catalyst-to-fat"
0x118602,"Lambda probe front catalyst, mixture fine tuning: exhaust gas after the catalyst is too lean"
0x118C02,"Fuel trim, injector aging long term adaptation implausible"
0x118E01,"Mixture adaptation, idle: mixture too lean"
0x118E02,"Mixture adaptation, idle mixture too fat"
0x118F20,"Mixture adaptation, lower engine speed range: mixture at partial load too lean"
0x118F21,"Mixture adaptation, lower engine speed range: mixture at partial load too fat"
0x119001,"Rail pressure sensor, electrical: short circuit to Plus"
0x119002,"Rail pressure sensor, electrical: short circuit to ground"
0x119201,"Fuel low pressure sensor, electrical: short circuit to Plus"
0x119202,"Low-pressure fuel sensor, electric: short-circuit after mass"
0x119301,"Rail pressure sensor, operating range: pressure too high"
0x119302,"Rail pressure sensor, operating range: pressure too low"
0x119304,"Rail pressure sensor, plausibility: pressure too high prior to starting the engine"
0x119308,"Rail pressure sensor, plausibility: pressure too low prior to starting the engine"
0x119404,"Rail pressure sensor, Signal: fixed lying"
0x11A001,"Fuel high pressure, plausibility: pressure too high"
0x11A002,"Fuel high pressure, plausibility: pressure too low"
0x11A201,"Low-pressure fuel, operating range: pressure too high"
0x11A204,"Low-pressure fuel, operating range: pressure too low"
0x11A210,"Low-pressure fuel system, plausibility: performance electric fuel pump for the actual pressure to high"
0x11A211,Low-pressure fuel system: high performance electric fuel pump for the actual pressure to low
0x11A301,High fuel pressure after motor stop: pressure too high
0x11A401,High-pressure fuel in the case of release of the injection pressure is too low
0x11A701,"Rail pressure sensor, plausibility: pressure too low"
0x11A702,"Rail pressure sensor, plausibility: pressure too high"
0x11AA01,"Fuel high pressure, plausibility: pressure too high, emergency operation with low-pressure"
0x11AA02,"Fuel high pressure, plausibility: pressure too high, emergency operation with fuel injection shutdown"
0x11AA04,"Fuel high-pressure: pressure is momentarily too high, the speed and load limit"
0x11AB01,"High-pressure fuel system, plausibility: control deviation of the quantity control valve is too large"
0x11AB02,"High-pressure fuel system, plausibility: control deviation of the quantity control valve is too small"
0x11AC01,"Fuel high pressure, plausibility, cold start: pressure too high"
0x11AC02,"Fuel high pressure, plausibility, cold start: pressure too low"
0x11AD10,"Fuel pressure: minimum pressure undershot, the Injection shut off to Katschutz"
0x11AE01,"Fuel versorgungssytem, lambda control: upper limit exceeded"
0x11AE02,"Fuel versorgungssytem, lambda control: lower limit undershot"
0x11B209,"Low-pressure fuel system, plausibility: conveying capacity is too low"
0x11B210,"Low-pressure fuel system, plausibility: voltage electric fuel pump implausible"
0x11B211,"Low-pressure fuel system, control: actual pressure too low"
0x11B212,"Low-pressure fuel system, control: actual pressure too high"
0x11B401,High-pressure fuel at or after the release of the injection (2. Environmental condition set by time delay): pressure too low
0x11B501,High fuel pressure after release of injection pressure is too low
0x11B601,High-pressure fuel at or after the release of the injection (delayed environmental conditions): pressure too low
0x11C401,"Quantity control valve, activation: short circuit to Plus"
0x11C402,"Quantity control valve, activation: short circuit to ground"
0x11C404,"Quantity Control Valve, Activation: Line Break"
0x11CF30,Mixture Control: Collective Error
0x120208,"Charging pressure regulation, plausibility: pressure too high"
0x120308,"Charging pressure regulation, plausibility: pressure too low"
0x120408,Charge pressure control: shutdown as a result of reaction
0x121001,"The charging pressure sensor, electrical: short circuit to Plus"
0x121002,"The charging pressure sensor, electrical: short circuit to ground"
0x121201,"Boost pressure sensor, plausibility, overrun: pressure too high"
0x121202,"Boost pressure sensor, plausibility, overrun: pressure too low"
0x121521,Charge Pressure Sensor: Collective Error
0x121530,"Load pressure, plausibility: pressure too high"
0x121531,"Charging pressure, operating range: pressure too low"
0x121532,"Boost pressure - ambient pressure, comparison: boost pressure too high"
0x121533,"Boost pressure - ambient pressure, comparison: boost pressure too low"
0x121601,Charge pressure sensor: pressure too high
0x121602,Charge pressure sensor: pressure too low
0x122001,"Diverter valve, actuation: short circuit to Plus"
0x122002,"Diverter valve, actuation: short circuit to ground"
0x122004,"Diverter Valve, Actuation: Line Break"
0x122108,Diverter valve is stuck closed
0x122201,"Thrust circulating air valve, Mechanics: suspected open terminal end of the diverter valve"
0x123001,"Wastegate actuator, actuation: short circuit to Plus"
0x123002,"Wastegate actuator, actuation: short circuit to ground"
0x123004,Wastegate Control: Line Break
0x123201,Wastegate control: a suspected fault in the waste gate control
0x128101,"Lambda probe in front of catalytic Converter, system check: Signal fixed switching to Skimmed"
0x128301,"Lambda probe in front of catalytic Converter, system check: Signal fixed lying on fat"
0x128501,"Lambda probe in front of catalytic Converter, in the boost: Signal is outside the limit"
0x128901,"Lambda probe in front of catalytic Converter, dynamics: slow response"
0x128B01,Lambda probe in front of catalytic Converter: false air detected
0x128E01,"Lambda probe before catalytic Converter, electrical: interruption Nernst line"
0x128E08,"Lambda probe before catalytic Converter, electrical: interruption of the calibration line"
0x129001,"Lambda probe before catalytic Converter, signal lines: short circuit to Plus"
0x129002,"Lambda probe before catalytic Converter, signal lines: short circuit to ground"
0x129201,"DME, internal fault, oxygen sensor before catalyst: initialization error"
0x129202,"DME, internal fault, oxygen sensor before catalytic Converter: communication fault"
0x129801,"Lambda probe after catalytic Converter, dynamics: slow response"
0x129A20,"DME, internal fault, oxygen sensor before catalyst lambda sensor module, the signal circuit adaptation value too high"
0x129A21,"DME, internal fault, oxygen sensor before catalyst lambda sensor module, under-voltage"
0x12A101,"Lambda probe after catalytic Converter, system check: Signal fixed lying on fat"
0x12A102,"Lambda probe after catalytic Converter, system check: Signal fixed switching to Skimmed"
0x12A308,"Lambda probe behind catalyst, dynamics, from fat to Skinny: slow response"
0x12A701,"Lambda probe after catalytic Converter, electrical: short circuit to Plus"
0x12A902,"Lambda probe after catalytic Converter, electrical: short circuit to ground"
0x12AB04,"Lambda probe after catalytic Converter, electrical: line break"
0x12AF08,"Lambda probe after catalyst, in a drawer, from fat to Skinny: delayed reaction"
0x12B101,"Lambda probe heating before catalytic Converter, activation: short circuit to Plus"
0x12B102,"Lambda probe heating before catalytic Converter, activation: short circuit to ground"
0x12B104,"Lambda probe heating before catalytic Converter, activation: line break"
0x12B301,"Lambda probe heating after catalytic Converter, activation: short circuit to Plus"
0x12B302,"Lambda probe heating after catalytic Converter, activation: short circuit to ground"
0x12B304,"Lambda probe heating after catalytic Converter, activation: line break"
0x12B505,"Lambda probe heating in front of catalytic Converter, function: heater error"
0x12B701,"Lambda probe heating after catalytic Converter, function: internal resistance too high"
0x12BD20,"Lambda probe heating in front of catalytic Converter, function: operating temperature not reached"
0x12BD21,"Lambda probe heating in front of catalytic Converter, function: failure to signal readiness"
0x12BD22,"DME, internal fault, oxygen sensor before catalyst: calibration resistance value implausible"
0x12BD33,"Lambda probe after catalytic Converter, ageing: shear stress threshold has not been reached"
0x12BD40,"Lambda probe after catalytic Converter, electrical: short circuit to Plus"
0x12BD41,"Lambda probe after catalytic Converter, electrical: wires-circuit or oxygen sensor poisoning"
0x12BD43,"Lambda probe after catalytic Converter, electrical: line break"
0x12BD50,"Lambda probe before catalytic Converter, electrical: interruption of the pump current line"
0x12BD51,Lambda probe before catalyst pump current line: signal voltage in the thrust to be too small due to open pumping current line
0x12BD52,"Lambda probe before catalytic Converter, electrical: interruption of the pump current line"
0x12BD60,"Lambda probe before catalytic Converter, electrical: interruption of the virtual mass"
0x12BD70,"Lambda probe before catalytic Converter, electrical: Nernst cell resistance or ceramic temperature implausible, wiring or heater fault"
0x12BD80,Lambda probe before catalyst: collective error
0x12BD90,"Lambda probe front catalyst, plausibility: mixture to a catalyst to fat"
0x12BD91,"Lambda probe front catalyst, plausibility: mixture after the catalyst is too lean"
0x12BD92,"Lambda probe front catalyst, plausibility: fixed lying on skimmed"
0x12BD93,"Lambda probe front catalyst, plausibility: fixed lying on fat"
0x130001,"VANOS solenoid intake valve, activation: short circuit to Plus"
0x130002,"VANOS solenoid intake valve, activation: short circuit to ground"
0x130004,"VANOS solenoid valve inlet, control: line break"
0x130104,"VANOS, intake: control fault, camshaft stuck"
0x130108,"VANOS, intake: control fault, Position not reached"
0x130201,"VANOS solenoid valve, actuation: short circuit to Plus"
0x130202,"VANOS solenoid valve, actuation: short circuit to ground"
0x130204,"VANOS solenoid valve, actuation: line break"
0x130304,"VANOS, exhaust: control fault, camshaft stuck"
0x130308,"VANOS, exhaust: control fault, Position not reached"
0x130E11,"Intake camshaft sensor, plausibility: pattern invalid"
0x130E20,Intake camshaft: angular offset to crankshaft is outside tolerance
0x130F11,"The outlet shaft sensor, plausibility: pattern Cam is invalid"
0x130F20,Exhaust camshaft: angular offset to crankshaft is outside tolerance
0x131401,"VANOS, exhaust, cold start: not controllable"
0x131501,"VANOS, intake, cold start: not controllable"
0x132101,"VANOS, exhaust: collective error"
0x132201,"VANOS, intake: collective error"
0x132301,VANOS: bus error
0x132408,"VANOS, exhaust: camshaft at the Start not in the lock position"
0x132508,"VANOS, intake Cam shaft at the Start not in the lock position"
0x133101,"Valvetronic Relay, activation: short circuit to Plus"
0x133102,"Valvetronic Relay, activation: short circuit to ground"
0x133104,"Valvetronic Relay, Activation: Line Break"
0x133201,"Valvetronic servomotor, activation: short circuit to Plus"
0x133202,"Valvetronic servomotor, activation: short circuit to ground"
0x133206,"Valvetronic servomotor, activation: deactivation in driving mode"
0x133208,"Valvetronic Servomotor, Activation: Line Break"
0x133304,"Valvetronic: Components Protection, Shutdown System"
0x133710,"Valvetronic, eccentric shaft adaptation: lower stop is reached"
0x133B04,Valvetronic System: no adjustment is possible
0x133E10,Valvetronic System: disabled to more frequent adjustment error
0x134A02,Valvetronic Actuator Motor: Overload
0x134F01,"Valvetronic, adjustment range: Urlernen outside tolerances"
0x134F02,"Valvetronic, adjustment range: stop not learned"
0x134F04,"Valvetronic, Adjustment Range: Error Range Check"
0x134F08,"Valvetronic, adjustment range: range check, deviation to Urlernen"
0x134F10,"Valvetronic, adjustment range: stop not learned owing to environmental conditions"
0x135301,"DME, internal error Valvetronic: components protection, shutdown System"
0x135302,"Valvetronic-Servo Motor: Components For The Protection, Shutdown System"
0x135401,Valvetronic: power stage overload
0x135402,Valvetronic Actuator Motor: Overload
0x135501,Valvetronic: warning threshold overload protection exceeded
0x135502,Valvetronic-servo motor: warning threshold overload protection exceeded
0x135604,Valvetronic System: control deviation too large
0x135608,"Valvetronic System, no motion detected"
0x135704,Valvetronic System: alarm threshold deviation is exceeded
0x135705,Valvetronic System: disabled warning threshold deviation is exceeded too often 
0x135706,Valvetronic System: lower stop learned
0x135808,"Valvetronic actuator motor, position sensors, electrical: malfunction"
0x135908,"Valvetronic actuator motor, position sensors: supply voltage is missing"
0x135A08,"Valvetronic actuator motor, position sensors, the plausibility of the signals to each other is implausible"
0x135A10,"Valvetronic actuator motor, position sensors: absolute value of eccentric angle wrong"
0x135B10,"Valvetronic Actuator Motor, Control, U-Phase: Line Break"
0x135B11,"Valvetronic Actuator Motor, Control Phase V: Line Break"
0x135B12,"Valvetronic Actuator Motor, Control Phase W: Line Break"
0x135C10,"Valvetronic actuator motor, position sensors: error detected"
0x135C11,"Valvetronic actuator motor, position sensors: signal implausible"
0x138101,"Exhaust gas flap, activation: short circuit to Plus"
0x138102,"Exhaust gas flap, activation: short circuit to ground"
0x138104,"Exhaust Gas Flap, Activation: Line Break"
0x138201,"Radiator shutter, top, supply voltage, self-diagnosis: malfunctioning"
0x138301,"Cooler jalousie, above, self-diagnosis: Overtemperature detected"
0x138401,"Cooler jalousie, above, self-diagnosis: electrical fault"
0x138501,"Radiator shutter, top: lower stop not detected"
0x138601,"Radiator shutter, top: upper stop not detected"
0x138701,"Radiator shutter, top: top attack to be detected early"
0x138901,"Radiator blind, bottom, actuation: short circuit to Plus"
0x138902,"Radiator blind, bottom, actuation: short circuit to ground"
0x138904,"Radiator blind, bottom, control: line break"
0x140001,"Combustion misfiring, several cylinders: fuel injection is switched off"
0x140002,"Combustion misfiring, several cylinders: damaging exhaust gas after start-up process"
0x140004,"Combustion misfiring, several cylinders: exhaust harmful"
0x140101,"Combustion miss, cylinder 1: fuel injection is switched off"
0x140102,"Combustion miss, cylinder 1: exhaust gas harmful to the start-up process"
0x140104,"Combustion miss, cylinder 1: exhaust gas harmful to the"
0x140201,"Combustion miss, cylinder 2: fuel injection is switched off"
0x140202,"Combustion miss, cylinder 2: exhaust gas harmful to the start-up process"
0x140204,"Combustion miss, cylinder 2: exhaust gas harmful to the"
0x140301,"Combustion miss, cylinder 3: fuel injection is switched off"
0x140302,"Combustion miss, cylinder 3: exhaust gas harmful to the start-up process"
0x140304,"Combustion miss, cylinder 3: exhaust gas harmful to the"
0x140401,"Combustion miss, cylinder 4: fuel injection is switched off"
0x140402,"Combustion miss, cylinder 4: exhaust gas harmful to the start-up process"
0x140404,"Combustion miss, cylinder 4: exhaust gas harmful to the"
0x140501,"Combustion misfiring, cylinder 5: fuel injection is switched off"
0x140502,"Combustion misfiring, cylinder 5: exhaust gas harmful to the start-up process"
0x140504,"Combustion misfiring, cylinder 5: exhaust gas harmful to the"
0x140601,"Combustion misfiring, cylinder 6: fuel injection is switched off"
0x140602,"Combustion misfiring, cylinder 6: damaging exhaust gas after start-up process"
0x140604,"Combustion misfiring, cylinder 6: exhaust gas harmful to the"
0x143201,"Rough running, filling the individual cylinder torque contribution is too low"
0x150102,"Ignition, cylinder 1: spark duration too short"
0x150202,"Ignition, cylinder 2: spark duration too short"
0x150302,"Ignition, cylinder 3: spark duration too short"
0x150402,"Ignition, cylinder 4: spark duration too short"
0x150502,"Ignition, cylinder 5: burn time is too short"
0x150602,"Ignition, cylinder 6: the combustion duration is too short"
0x151001,"Ignition angle adjustment in the idle, cold start: Ignition angle too early"
0x151101,"Ignition angle adjustment in the partial load, cold start: Ignition angle too early"
0x152001,"Relay, ignition, and injectors, the supply voltage ignition switch: short circuit to Plus"
0x152007,"Relay, ignition, and injectors, the supply voltage ignition switch: short circuit to ground"
0x152009,"Ignition circuit, supply voltage: Bank failure or motor failure"
0x152108,Super Knock On Cylinder 1: Injection Deactivation
0x152208,Super Knocking Cylinder 2: Fuel Injection Deactivation
0x152308,Super Knocking Cylinder 3: Fuel Injection Deactivation
0x152408,Super Knock On Cylinder 4: Fuel Injection Deactivation
0x152508,Super Knocking Cylinder 5: Fuel Injection Deactivation
0x152608,Super Knocking Cylinder 6: Fuel Injection Deactivation
0x152D08,Super Knocking: Fuel Injection Deactivation
0x160001,"Crankshaft sensor, Signal: missing"
0x160020,"Crankshaft sensor, Signal: implausible"
0x160510,Crankshaft sensor [plausibility]: pulse-width implausible
0x164020,"Intake camshaft sensor, electrical: short circuit to Plus"
0x164021,"Intake camshaft sensor, electrical: short circuit to ground"
0x164030,"Exhaust camshaft sensor, electrical: short circuit to Plus"
0x164031,"Exhaust camshaft sensor, electrical: short circuit to ground"
0x164040,Intake camshaft: installation error
0x164041,Exhaust camshaft: installation error
0x168A20,Knock Control: The System Error
0x168A30,"Knock sensor 1, electrical: Signal, input A, short circuit to Plus"
0x168A31,"Knock sensor 1, electrical: Signal, input A, short circuit to ground"
0x168A40,"Knock sensor 1, electrical: Signal, input B, short circuit to Plus"
0x168A41,"Knock sensor 1, electrical: Signal, input B, short circuit to ground"
0x168A50,"Knock sensor 2, electrical: Signal, input A, short circuit to Plus"
0x168A51,"Knock sensor 2, electrical: Signal, input A, short circuit to ground"
0x168A60,"Knock sensor 2, electrical: Signal, input B, short circuit to Plus"
0x168A61,"Knock sensor 2, electrical: Signal, input B, short circuit to ground"
0x168A70,"Knock sensor 1, Signal: engine noise limit"
0x168A71,"Knock sensor 1, Signal: engine noise under limit or line interruption"
0x168A80,"Knock sensor 2, Signal: engine noise limit"
0x168A81,"Knock sensor 2, Signal: engine noise under limit or line interruption"
0x180001,Catalyst: efficiency below threshold
0x190001,"DMTL solenoid valve, activation: short circuit to Plus"
0x190002,"DMTL solenoid valve, activation: short circuit to ground"
0x190004,"DMTL solenoid valve, activation: line break"
0x190201,"Tank vent and purge air system, fine leak: leakage greater than 1.0 mm"
0x190302,"Tank vent and purge air system, fine leak: leakage of greater than 0.5 mm"
0x190401,"DMTL, system fault: pump current too high during reference measurement"
0x190402,"DMTL, system fault: pump current too low during reference measurement"
0x190404,"DMTL, system fault: abort due to current fluctuations during reference measurement"
0x190408,"DMTL, system fault: pump current when the Valve has reached the limit"
0x190501,"DMTL, heater, activation: short circuit to Plus"
0x190502,"DMTL, heater, activation: short circuit to ground"
0x190504,"DMTL, heater, activation: line break"
0x190601,"DMTL leak diagnosis pump, activation: short circuit to Plus"
0x190702,"DMTL leak diagnosis pump, activation: short circuit to ground"
0x190704,"DMTL leak diagnosis pump, activation: line break"
0x191001,"Tank vent valve, activation: short circuit to Plus"
0x191002,"Tank vent valve, activation: short circuit to ground"
0x191004,"Tank Vent Valve, Activation: Line Break"
0x191A20,Fuel tank vent valve: stuck closed 
0x191A21,Fuel tank vent valve: jammed open
0x191B01,"Tank ventilation system shut-off valve, activation: short circuit to Plus"
0x191B02,"Tank ventilation system shut-off valve, activation: short circuit to ground"
0x191B04,"Tank Ventilation System Shut-Off Valve, Activation: Line Break"
0x191C01,Tank ventilation system shut-off valve: jammed open
0x191C02,"The tank venting system, 2. Introductory Unit: Malfunction"
0x191C03,"The tank venting system, 2. Introductory Point, Caster: Malfunction"
0x191D01,Tank Vent System: Malfunction
0x192001,Tank lid not closed properly
0x192002,Tank lid: open in the Wake of
0x193002,"Fuel level sensor, left, electrical: short circuit to ground"
0x193008,"Fuel level sensor, left, plausibility: CAN value implausible"
0x193011,"Fuel level sensor, right, Signal: short circuit to Plus"
0x193102,"Fuel level sensor, right, electrical: short circuit to ground"
0x193108,"Fuel level sensor, right, plausibility: CAN value implausible"
0x193111,"Fuel level sensor, left, Signal: short circuit to Plus"
0x193120,"Fuel-level sensor, on the left, the plausibility of the tank level signal to high"
0x193218,Tank level sensor: Signal level is implausible because of the fixed hanging fuel Tank sender
0x193220,"Tank filling level, plausibility tank fill level is greater than the tank volume"
0x193221,Tank level sensor: deviation between consumption and level change
0x193A20,"Fuel level in the tank, collective error Signal and electrically"
0x1A2001,Electric fan control wiring: short circuit to Plus
0x1A2002,Electric fan control wiring: short circuit to ground
0x1A2004,"Electric Fan, Drive Line: Line Interrupt"
0x1A2108,"Electric fan, self diagnosis stage 1: light fan error"
0x1A2308,"Electric fan, self diagnosis step 2: Fan failure with potential risk for the fan"
0x1A2408,"Electric fan, self-diagnosis step 3: fan error the motor function restriction"
0x1A2508,"Electric fan, self diagnosis level 4: serious fan error"
0x1A2601,Fuse relay electric fan control wiring: short circuit to Plus
0x1A2602,Fuse relay electric fan control wiring: short circuit to ground
0x1A2604,"Fuse Relay Electric Fan, Drive Line: Line Interrupt"
0x1A2804,"Electric fan, stand-by: restricted"
0x1A2904,"Electric fan, stand-by: not given"
0x1B0A20,Bad detection: wheel speed distance to high
0x1B0A21,Bad detection distance: No wheel speed signal receive
0x1B0A40,"Vehicle speed, plausibility: speed too high"
0x1B0A50,Vehicle Speed: Collection Error
0x1B0A60,"Vehicle speed, plausibility: minimum speed under load, implausible"
0x1B0A61,"Vehicle speed, plausibility: minimum speed in the thrust implausible"
0x1B0A62,"Vehicle speed, plausibility: speed signal implausible"
0x1B0A64,"Vehicle speed, wheel speed sensor rear/left signal change: implausible"
0x1B0A65,"Vehicle speed, wheel speed sensor, front/left, signal change: implausible"
0x1B0A66,"Vehicle speed, wheel speed sensor rear/right, signal change: implausible"
0x1B0A67,"Vehicle speed, wheel speed sensor, front/right, signal change: implausible"
0x1B2002,EWS manipulation protection: programmed no start value 
0x1B2008,EWS manipulation protection: response implausible
0x1B2101,Interface EWS-DME: hardware error
0x1B2102,Interface EWS-DME: frame error
0x1B2104,Interface EWS-DME: timeout
0x1B2109,Interface EWS-DME: reception error CAS interface
0x1B2201,"DME, internal error, EWS data: no available storage possibility"
0x1B2202,"DME, internal error, EWS data: error code storage"
0x1B2208,"DME, internal error, EWS data: checksum error"
0x1B2209,"DME, internal error, EWS data: write error-Secret Key"
0x1B2302,"FA-CAN, message (EMS service DME1/DDE1, 0x5C0): frame error"
0x1B2304,"FA-CAN, message (EMS service DME1/DDE1, 0x5C0): in the absence of"
0x1B5101,"Terminal 15_3, line from CAS, electrical: short circuit to Plus"
0x1B5102,"Terminal 15_3, line from CAS, electrical: short circuit to ground"
0x1B5202,Terminal 15N_1: no voltage
0x1B5302,Terminal 15N_2: no voltage
0x1B5402,Terminal 15N_3: no voltage
0x1B6008,"Brake light switch, plausibility: Signal implausible"
0x1B9004,Engine stop time: time-out or invalid value
0x1B9008,Engine stop time: Signal implausible
0x1B9508,"Motor shutoff time, plausibility: time too short in correlation to engine-coolant-cooling"
0x1B9608,"Motor shutoff time, plausibility: time to long in correlation to engine-coolant-cooling"
0x1B9701,"Engine stop time, the time counter multi - counter DME, comparison: value of the time counter combination to a high in the running of the engine"
0x1B9702,"Engine stop time, the time counter multi - counter DME, comparison: value of the time counter combination to a low in running of the engine"
0x1B9804,"Engine stop time, Signal: missing"
0x1B9A01,"Engine stop time, the time counter multi - counter DME, comparison: value of the time counter combination to a high in the Wake of"
0x1B9A02,"Engine stop time, the time counter multi - counter DME, comparison: value of the time counter is combined to low in the Wake of"
0x1BC004,"Zero-gear sensor, adaptation: not learned (MSA disabled)"
0x1C0001,"Engine Oil Pressure Control, Plausibility: Pressure Fluctuations"
0x1C0101,"Engine oil pressure control, plausibility, static: pressure too high"
0x1C0102,"Engine oil pressure control, plausibility, static: pressure too low"
0x1C0201,"Engine oil pressure control valve, activation: short circuit to Plus"
0x1C0202,"Engine oil pressure control valve, activation: short circuit to ground"
0x1C0204,"Engine Oil Pressure Control Valve, Activation: Line Break"
0x1C0301,Engine oil pressure regulating valve stuck in fully energized position (minimum Oil pressure)
0x1C0302,Engine oil pressure control valve: stuck in unbestromter position (maximum Oil pressure)
0x1C0401,Engine oil pressure control: unstable
0x1C2001,Engine oil pump: pressure too high
0x1C2002,Engine oil pump: pressure too low
0x1C3001,"Engine oil pressure sensor, electrical: short circuit to Plus"
0x1C3002,"Engine oil pressure sensor, electrical: short circuit to ground"
0x1C3101,"Engine oil pressure sensor, plausibility: pressure too high before Start"
0x1C3102,"Engine oil pressure sensor, plausibility: pressure too low before Start"
0x1C3108,Engine oil pressure sensor Signal: fixed lying
0x1C4002,Engine oil level: too low
0x1C4110,Oil Condition Sensor: Malfunction
0x1C4111,"Oil condition sensor, plausibility: level implausible"
0x1C4112,"Oil condition sensor, plausibility: temperature implausible"
0x1C4113,"Oil condition sensor, plausibility: level implausible"
0x1C4114,"Oil condition sensor, plausibility: Permittivity implausible"
0x1C4115,"Oil condition sensor, plausibility: temperature implausible"
0x1C4116,"Oil Condition, Status, Level: Malfunction"
0x1C4117,"Oil Condition, Status Permittivity: Malfunction"
0x1C4118,Oil Condition Sensor Status Temperature: Malfunction
0x1C4119,"Engine oil temperature sensor, electrical: malfunction"
0x1C4120,"Engine oil temperature sensor, plausibility: temperature implausible"
0x1C5A20,"BSD, communication (oil condition sensor): missing"
0x1D2008,Map-controlled thermostat: stuck open
0x1D2401,"Characteristic map thermostat, activation: short circuit to Plus"
0x1D2402,"Characteristic map thermostat, activation: short circuit to ground"
0x1D2404,"Characteristic Map Thermostat, Activation: Line Break"
0x1D3701,"Power management, battery: speed increase due to low state of charge"
0x1D3901,"EGS, signal monitoring (Drehzahl_Getriebestrang_Turbine): loss of Signal or invalid signal content"
0x1D3A01,"Communication: Signal (Drehzahl_Getriebestrang_Abtrieb) in A-CAN, message (data transmission continuous, 0x1AF): invalid"
0x1D3B01,"EGS, signal monitoring (Status_Gangwahl_Antrieb): loss of Signal or invalid signal content"
0x1D3C01,"Communication: Signal (Status_Schaltung_Aktiv_Getriebe) in A / FA-CAN, message (Status gear control, 0x39A): invalid"
0x1E0001,Idle speed control: engine speed too high
0x1E0002,Idle speed control: engine speed too low
0x1E0101,"Idle control, cold start: engine speed too high"
0x1E0102,"Idle control, cold start: engine speed too low"
0x1E5201,Speed limit with the vehicle stationary: idle speed too high for too long
0x1E5301,Tamper protection: the motor power is too high
0x1E5A20,Drive safety function: performance reduction due to the safety concept
0x1F0514,"Valvetronic, supply voltage: short-circuit after mass"
0x1F0515,"Valvetronic, Supply Voltage: Line Break"
0x1F0516,Drive safety function: AD-transformer open circuit test impulse test
0x1F0517,Drive safety function: AD-Converter test-voltage test
0x1F0518,"DME, internal fault, safety function: air volume adjustment"
0x1F0519,Drive safety function: the accelerator pedal module or pedal-travel sensor implausible
0x1F0520,Drive safety function: speed sensor implausible
0x1F0521,"DME, internal fault, safety function: plausibility check of the mixture correction factors"
0x1F0522,"DME, internal fault, safety function: injection quantity limitation level 1"
0x1F0523,Drive Safety Function: Safety Switch-Off Injection
0x1F0524,"DME, internal fault, safety function: Lambda target value"
0x1F0525,"DME, internal fault, safety function: plausibility check, relative fuel mass"
0x1F0526,"DME, internal fault, safety function: torque comparison"
0x1F0527,"DME, internal fault, safety function: drive train gear ratio implausible"
0x1F0528,Drive safety function: transmission variant implausible
0x1F0529,"DME, internal fault, safety function: ignition angle monitoring"
0x1F0530,Drive safety function: shut-off path Test is negative
0x1F0531,"DME, internal fault, safety function: plausibli-ization of the mass of fuel"
0x1F0532,"DME, internal fault, Monitoring MSC communication malfunction in module R2S2/1"
0x1F0533,"DME, internal fault, Monitoring MSC communication malfunction in module R2S2/2"
0x1F0904,"DME, internal error, control Valvetronic: malfunction"
0x1F1A40,"DME, internal error: Monitoring SPI communications"
0x1F1A50,"DME, internal fault: Delete EEPROM faulty"
0x1F1A51,"DME, internal error: EEPROM read error"
0x1F1A52,"DME, internal error: Write to EEPROM is faulty"
0x1F1A60,"DME, internal error: monitoring engine error"
0x1F1A70,"DME, internal fault, Monitoring 5V supply: Overvoltage detected"
0x1F1A71,"DME, internal fault, Monitoring 5V supply: under voltage detected"
0x1F1A72,"DME, internal fault, Monitoring 5V supply 2: power Surge detected"
0x1F1A73,"DME, internal fault, Monitoring 5V supply 2: low voltage detected"
0x1F1A80,"DME, internal fault, Watchdog output: malfunction"
0x1F1A81,"DME, internal fault, Watchdog output: faulty question/answer communication"
0x1F1A82,"DME, internal fault, Watchdog output: overvoltage detection"
0x1F1A90,Monitoring 5V Sensor supply: voltage outside valid range
0x1F1A91,Monitoring 5V Sensor power supply 2: voltage outside valid range
0x1F1A92,Monitoring 5V Sensor supply 3: voltage outside valid range
0x1F1AA0,"DME, internal error: Software Reset"
0x1F1AA1,"DME, internal error: Software Reset"
0x1F1AA2,"DME, internal error: Software Reset"
0x1F1B40,"Start unit pinion starter, actuation: short circuit to Plus"
0x1F1B41,"Start unit pinion starter, actuation: short circuit to ground"
0x1F1B42,"Start Unit Pinion Starter, Control: Line Break"
0x1F1B50,"The vehicle voltage, DME main relay, working range: voltage too high"
0x1F2102,"Feature activation, performance increase: not done"
0x1F2104,"DME, a false data set: FA-CAN, message (vehicle type, 0x388): in the absence of"
0x1F2108,"DME, incorrect data record: variant monitoring"
0x1F2601,"DME, coding: missing"
0x1F2604,"DME, encoding: chassis number wrong"
0x1F2701,"DME, coding: write error"
0x1F2702,"DME, encoding: a signature verification error"
0x1F2704,"DME, coding data implausible"
0x1F4A01,"Relay, ignition, and injectors, actuation: short circuit to Plus"
0x1F4A02,"Relay, ignition, and injectors, actuation: short circuit to ground"
0x1F4A10,"Relay, ignition, and injectors, control: line break"
0x1F5020,"DME, internal fault, inside temperature sensor: value too high"
0x1F5021,"DME, internal fault, inside temperature sensor: value too low"
0x1F5101,DME temperature: over-temperature
0x1FBB01,Check Control message (ID 567): motor-fan. Moderate to
0x200502,"DME, internal fault, Monitoring external torque request: request ICM implausible"
0x200503,"DME, internal fault, Monitoring external torque request: request ICM implausible"
0x200D04,Drive safety function: bus signal accelerator pedal value implausible
0x200F11,"DME, internal fault, Monitoring of the broadcast signals: Is the wheel torque implausible"
0x200F12,"DME, internal fault, Monitoring of the broadcast signals: coordinated wheel torque implausible"
0x200F13,"DME, internal fault, Monitoring of the broadcast signals: loss of torque implausible"
0x200F14,"DME, internal fault, Monitoring of the broadcast signals: amplification powertrain implausible"
0x200F15,"DME, internal fault, Monitoring of the broadcast signals: Status, interface and driver assistance system implausible"
0x200F16,"DME, internal fault, Monitoring of the broadcast signals: status word wheel torque interface implausible"
0x200F17,"DME, internal fault, Monitoring of the broadcast signals: drag torque implausible"
0x200F18,"DME, internal error, advanced Monitoring transmission signals: accelerator pedal implausible"
0x200F19,"DME, internal error, advanced Monitoring transmission signals: Is the wheel torque implausible"
0x200F20,"DME, internal error, advanced Monitoring transmission signals: coordinated wheel torque implausible"
0x200F21,"DME, internal error, advanced Monitoring transmission signals: loss of torque implausible"
0x200F22,"DME, internal error, advanced Monitoring transmission signals: amplification powertrain implausible"
0x200F23,"DME, internal error, advanced Monitoring transmission signals: Status, interface and driver assistance system implausible"
0x200F24,"DME, internal error, advanced Monitoring transmission signals: Qualifier wheel torque interface implausible"
0x200F25,"DME, internal error, advanced Monitoring transmission signals: drag torque implausible"
0x200F26,"DME, internal error, advanced Monitoring transmission signals: Status, engine run-implausible"
0x200F27,"DME, internal fault, Monitoring of the broadcast signals: engine speed, rear axle implausible"
0x200F28,"DME, internal fault, Monitoring of the broadcast signals: the direction of travel request implausible"
0x200F29,"DME, internal fault, Monitoring of the broadcast signals: motor status implausible"
0x200F2A,"DME, internal fault, Monitoring of the broadcast signals: Status, power, powertrain implausible"
0x200F2B,"DME, internal error, advanced Monitoring transmission signals: engine speed, rear axle implausible"
0x200F2C,"DME, internal error, advanced Monitoring transmission signals: the direction of travel request implausible"
0x200F2D,"DME, internal error, advanced Monitoring transmission signals: motor status implausible"
0x200F2E,"DME, internal error, advanced Monitoring transmission signals: Status, power, powertrain implausible"
0x201004,CBS Client: output of substitute value
0x201008,CBS-the Client: availability jump
0x201010,A / FA-CAN Hardware: defective
0x201020,FlexRay Hardware: defective
0x201030,Test Function Layer: Error-Group 1
0x201040,Test Function Layer: Error-Group 2
0x201101,"DME, protection against manipulation: the program or data tampering has been detected"
0x20A701,Engine cooling system: engine speed coolant pump out of tolerance
0x20A801,Engine cooling system: shutdown coolant pump due to Overtemperature
0x20A802,Engine cooling system: shutdown coolant pump due to power Surge
0x20A804,"Engine cooling system: shutdown coolant pump, due to blocking"
0x20A901,"Motor-cooling system, low-power operation: loss-of-coolant through a coolant pump detected"
0x20A902,"Motor-cooling system, low-power operation: supply voltage the coolant pump is too low"
0x20A904,"Motor-cooling system, low-power operation: coolant pump temperature threshold 1 exceeded"
0x20A908,"Motor-cooling system, low-power operation: coolant pump temperature threshold 2 exceeded"
0x20A909,"BSD, communication (engine coolant pump): missing"
0x20AA04,Engine cooling system: communication with the coolant pump is faulty
0x20AB08,Engine-cooling system: no emergency signal to the coolant pump
0x20AC08,"Coolant pump, hardware, input: Status of emergency-entrance implausible"
0x20AD08,Coolant pump: rotation speed implausible
0x20BA20,"Clutch switch, Signal: missing"
0x210201,"Generator, electrical: malfunction"
0x210301,"Alternator, plausibility, electrical: calculated"
0x210401,"Generator, Temperature: Excess Temperature"
0x210501,"Alternator, plausibility, temperature: excess temperature calculated"
0x210601,"Alternator, mechanical: malfunction"
0x210701,"Generator, Regulator: type wrong"
0x210801,Generator: type wrong
0x210901,"Generator, communication: no communication"
0x210C01,"Generator, Communication: Bus Error"
0x211A21,BSD-Bus: communication error
0x211F01,Generator/start generator: encoding is missing
0x212001,"Start Generator, Communication: Bus Error"
0x212101,"Start, alternator, plausibility, electrical: calculated"
0x212201,"Start generator, electrical: malfunction"
0x212301,Start Generator: Excess Temperature
0x212401,"Start, alternator, mechanical: malfunction"
0x212501,"Start generator, MSA hardware management: Signal implausible"
0x212601,Start generator: MSA permanently disabled 
0x212701,Start generator: MSA disabled temporarily 
0x212801,Start Generator: Sensor Error
0x212A01,Start generator: type wrong
0x213301,Power management: the Central Surge
0x213401,Power management: centralized under-voltage
0x213501,Power Management: Battery Deep Discharge
0x213604,"Power Management, Quiescent Current Monitoring: Quiescent Current Violation"
0x213701,Power Management: Battery-Free Operation
0x213801,"Power management: transport monitoring state of charge, battery deep discharge"
0x213901,Power management: consumer reduction active
0x213A01,Power management: transport monitoring state of charge battery discharging
0x213A20,"On-Board network voltage, working range: voltage too high"
0x213A21,"On-Board network voltage, working range: voltage too low"
0x213A22,On-Board network voltage: analogue-to-Digital Converter is defective
0x213B08,"Power Management, Battery Condition Detection: Defective Battery"
0x213C08,"Power Management, Battery Condition Detection: Deep Discharge"
0x213D01,"Power management, component identification: battery charging unit and Power Control Unit detected"
0x215001,"Advanced Communication, Intelligent Battery Sensor: Malfunction"
0x215101,"Intelligent battery sensor, plausibility: temperature implausible"
0x215104,"Intelligent battery sensor, plausibility: voltage implausible"
0x215108,"Intelligent battery sensor, plausibility: current implausible"
0x215801,"Intelligent battery sensor, Wake-up pipe, electrical: short circuit to Plus or ground"
0x215901,"Intelligent battery sensor, compatibility: Version not plausible"
0x215A01,"Intelligent battery sensor, Wake-up-wire, electrical: line break"
0x215B01,"Intelligent battery sensor, communication: no communication"
0x215C01,"Intelligent Battery Sensor, Self-Diagnosis: System Error"
0x218001,Battery Charging Unit: Internal Error
0x218101,"Battery Load Unit, In-Line Monitoring: Malfunction"
0x218201,"Battery charger, Secondary battery: defective"
0x218301,Battery charging unit: errors in the separating element/wiring harness
0x219001,"Active engine mount, electrical: short circuit to Plus"
0x219002,"Active engine mount, electrical: short circuit to ground"
0x219004,"Active engine mount, electrical: line break"
0x21A210,"Communication: Signal (Status_Welligkeit_FSSP) in FA-CAN, message (services 0x5E0, Electrical requirement consumer ID2: 68): void"
0x231501,Fault memory entry: send buffer full
0x231502,Fault memory entry: Send failed
0x231802,"CAN, message (Status transmission control unit, 0x39A) in the case of under-voltage: communication error to the A-CAN"
0x231902,"CAN, message (data transmission continuous, 0x1AF) in the case of under-voltage: communication error to the A-CAN"
0x231F04,"A / FA-CAN, message (gearbox): missing"
0x233004,"FA-CAN, message (OBD-Sensor diagnosis status, 0x5E0): missing station wagon"
0xCD840A,FA-CAN Control Module Bus OFF
0xCD8420,FlexRay Bus: Communication Error
0xCD8486,A-the CAN Control Module Bus OFF
0xCD8801,"FlexRay Controller, Startup: maximum startup time exceeded"
0xCD8BFF,Network error: only for Test
0xCD8C10,"LIN, message (state of charge Status IBS_LIN, 0x0A): missing"
0xCD8C11,"LIN, message (error Status IBS_LIN, 0x0B): missing"
0xCD8C12,"LIN, Message (values IBS_LIN, 0x09): missing"
0xCD8D10,"Radiator shutter, communication: disturbed"
0xCD8D15,"LIN, Message (battery charger, secondary battery): Electrical fault"
0xCD8D16,"LIN, Message (battery charging unit, H-bridge): No load operation is possible"
0xCD8D17,"LIN, Message (battery charging unit, the Status of the generation of energy on-Board network 2, 0x5): in the absence of"
0xCD8D18,"LIN, Message (battery charging unit, the separating element or line): Electrical fault"
0xCD8D19,"LIN, communications (battery charging unit, the second voltage level): malfunction"
0xCD8E10,LIN Bus: communication error
0xCD8E11,"LIN, communication (charging unit for auxiliary battery): missing"
0xCD8F01,"LIN, communication (intelligent battery sensor): missing"
0xCD9002,"Coolant Pump, Communication: Invalid Message"
0xCD9011,Engine cooling system: communication with the coolant pump is faulty
0xCD9201,"LIN, communication (radiator shutter): timeout"
0xCD9203,"LIN, communication (radiator shutter): missing"
0xCD9402,"FlexRay, Message (Request Torque Crankshaft, Drive Dynamics, 58.1.4): Alive Test"
0xCD9404,"FlexRay, message (request torque crankshaft, drive dynamics, 58.1.4): missing"
0xCD9408,"FlexRay, message (request torque crankshaft, drive dynamics, 58.1.4): checksum incorrect"
0xCD9432,"A-CAN, message (Status transmission control unit, 0x39A) in the case of under-voltage: in the absence of"
0xCD9435,"A-CAN, message (data transmission continuous, 0x1AF) in the case of under-voltage: in the absence of"
0xCD9437,"FlexRay message (request wheel torque drivetrain sum of stabilisation / Should error distribution of the longitudinal moment, front axle, rear axle, 43.1.4) in the case of under-voltage: communication"
0xCD9502,"FlexRay Message (Request Wheel Torque Drivetrain Sum Of Stabilisation / To Test The Distribution Of The Longitudinal Moment, Front Axle, Rear Axle, 43.1.4): Alive"
0xCD9504,"FlexRay message (request wheel torque drivetrain sum of stabilisation / Is missing the distribution of longitudinal moment, front axle, rear axle, 43.1.4): "
0xCD9508,"FlexRay message (request wheel torque drivetrain sum of stabilisation / To wrong distribution of longitudinal moment, front axle, rear axle, 43.1.4): checksum "
0xCD9602,"FlexRay Message (Request Wheel Torque Drivetrain, Total Recuperation, 47.0.2): Alive Test"
0xCD9604,"FlexRay message (request wheel torque drivetrain, total recuperation, 47.0.2): missing"
0xCD9608,"FlexRay message (request wheel torque drivetrain, total recuperation, 47.0.2): checksum incorrect"
0xCD9702,"FlexRay, Message (Configuration, Switch, Driving Dynamics, 272.4.8): Alive Test"
0xCD9704,"FlexRay, message (configuration, switch, driving dynamics, 272.4.8): missing"
0xCD9708,"FlexRay, message (configuration, switch, driving dynamics, 272.4.8): checksum incorrect"
0xCD9902,"FlexRay, The Message (Speed, Vehicle, 55.3.4): Alive Test"
0xCD9904,"FlexRay, the message (speed, vehicle, 55.3.4): missing"
0xCD9908,"FlexRay, the message (speed, vehicle, 55.3.4): checksum incorrect"
0xCD9932,"FlexRay Message (Yaw Speed Of The Vehicle, 56.0.2): Alive Test"
0xCD9933,"FlexRay message (yaw speed of the vehicle, 56.0.2): missing"
0xCD9934,"FlexRay message (yaw speed of the vehicle, 56.0.2): checksum incorrect"
0xCD9935,"FlexRay, the message (data driving dynamics sensor Extended 38.0.2): missing"
0xCD9A02,"FlexRay, The Message Is (Braking Torque Sum, 43.3.4): Alive Test"
0xCD9A04,"FlexRay, the message Is (braking torque sum, 43.3.4): missing"
0xCD9A08,"FlexRay, the message Is (braking torque sum, 43.3.4): checksum incorrect"
0xCD9B02,"FlexRay, Message, (Is The Speed Wheel, 46.1.2): Alive Test"
0xCD9B04,"FlexRay, message, (Is the speed wheel, 46.1.2): missing"
0xCD9B08,"FlexRay, message, (Is the speed wheel, 46.1.2): checksum incorrect"
0xCD9D02,"FlexRay Message (Longitudinal Acceleration, Center Of Gravity, 55.0.2): Alive Test"
0xCD9D04,"FlexRay message (longitudinal acceleration, center of gravity, 55.0.2): missing"
0xCD9D08,"FlexRay message (longitudinal acceleration, center of gravity, 55.0.2): checksum incorrect"
0xCD9E02,"FlexRay, Message (Lateral Acceleration, Center Of Gravity, 55.0.2): Alive Test"
0xCD9E04,"FlexRay, Message (lateral acceleration, center of gravity, 55.0.2): missing"
0xCD9E08,"FlexRay, Message (lateral acceleration, center of gravity, 55.0.2): checksum incorrect"
0xCD9F02,"FlexRay message (Status stabilization DSC, 47.1.2): alive test"
0xCD9F04,"FlexRay message (Status stabilization DSC, 47.1.2): missing"
0xCD9F08,"FlexRay message (Status stabilization DSC, 47.1.2): checksum incorrect"
0xCDA002,"FlexRay message (request wheel torque drivetrain, total FAS, 33.1.4): alive test"
0xCDA004,"FlexRay message (request wheel torque drivetrain, total FAS, 33.1.4): missing"
0xCDA008,"FlexRay message (request wheel torque drivetrain, total FAS, 33.1.4): checksum incorrect"
0xCDA102,"FlexRay Message (Inclination Of The Carriageway, 56.1.2): Alive Test"
0xCDA104,"FlexRay message (inclination of the carriageway, 56.1.2): missing"
0xCDA108,"FlexRay message (inclination of the carriageway, 56.1.2): checksum incorrect"
0xCDA204,"FlexRay message (requirement, performance, Electric, EPS, 234.0.2): missing"
0xCDA302,"FlexRay Message (Status Vehicle Is At A Standstill, 263.1.4): Alive Test"
0xCDA304,"FlexRay message (Status vehicle is at a standstill, 263.1.4): missing"
0xCDA308,"FlexRay message (Status vehicle is at a standstill, 263.1.4): checksum incorrect"
0xCDA402,"FlexRay, The Message Is (Steering Angle Front Axle, 57.1.2): Alive Test"
0xCDA404,"FlexRay, the message Is (steering angle front axle, 57.1.2): missing"
0xCDA408,"FlexRay, the message Is (steering angle front axle, 57.1.2): checksum incorrect"
0xCDA410,"FlexRay, the message (display LDM 1, 135.0.2): missing"
0xCDA421,"FlexRay Message (Status Door Sensors Are Secured, 256.3.4): Alive Test"
0xCDA422,"FlexRay message (Status door sensors are Secured, 256.3.4): missing"
0xCDA423,"FlexRay message (Status door sensors are Secured, 256.3.4): checksum incorrect"
0xCDA425,"FlexRay message (Status, Park assist, 231.1.2): missing"
0xCDA426,"FlexRay message (Status, distribution of longitudinal moment, front axle, rear axle, 19.3.4): missing"
0xCDA430,"FlexRay, The Message (Operation Mode Speed Torque Hybrid, 74.0.2): Alive Test"
0xCDA431,"FlexRay, the message (operation mode speed torque Hybrid, 74.0.2): checksum incorrect"
0xCDA432,"FlexRay, the message (operation mode speed torque Hybrid, 74.0.2): missing"
0xCDA435,"FlexRay, Message (mass/weight of the vehicle, 108.1.2): missing"
0xCDA440,"FlexRay Message (Control Coordination Torque Hybrid, 73.0.2): Alive Test"
0xCDA441,"FlexRay message (control coordination torque Hybrid, 73.0.2): checksum incorrect"
0xCDA442,"FlexRay message (control coordination torque Hybrid, 73.0.2): missing"
0xCDA451,"FlexRay, the message (The steering torque the driver's actuator, 49.0.2): missing"
0xCDA512,"FA-CAN, message (Status belt contact seat occupancy, 0x297): alive test"
0xCDA514,"FA-CAN, message (Status belt contact seat occupancy, 0x297): in the absence of"
0xCDA518,"FA-CAN, message (Status belt contact seat occupancy, 0x297): checksum incorrect"
0xCDA602,"FA-CAN, message (request torque crankshaft transmission 2, 0xA0): alive test"
0xCDA608,"FA-CAN, message (request torque crankshaft transmission 2, 0xA0): checksum incorrect"
0xCDA702,"FA-CAN, message (Status vehicle standstill Parking brake, 0x2DC): alive test"
0xCDA704,"FA-CAN, message (Status vehicle standstill Parking brake, 0x2DC): in the absence of"
0xCDA708,"FA-CAN, message (Status vehicle standstill Parking brake, 0x2DC): checksum incorrect"
0xCDA804,"FA-CAN, message (relative time, 0x328): in the absence of"
0xCDA904,"FA-CAN, message (Status of trailer, 0x2E4): in the absence of"
0xCDAB04,"FA-CAN, message (Status gear Reverse, 0x3B0): in the absence of"
0xCDAC04,"FA-CAN, message (Status transmission control unit, 0x39A): in the absence of"
0xCDAD04,"FA-CAN, message (control crash shutdown electric fuel pump, 0x135): in the absence of"
0xCDAE04,"FA-CAN, message (time/date, 0x2F8): missing"
0xCDAF04,"FA-CAN, message (ZV and flaps condition, 0x2FC): in the absence of"
0xCDB002,"FA-CAN, message (data transmission continuous, 0x1AF): alive test"
0xCDB008,"FA-CAN, message (data transmission continuous, 0x1AF): checksum incorrect"
0xCDB102,"FA-CAN, message (request torque crankshaft gear, 0xB0): alive test"
0xCDB108,"FA-CAN, message (request torque crankshaft gear, 0xB0): checksum incorrect"
0xCDB204,"FA-CAN, message (outside temperature, 0x2CA): in the absence of"
0xCDB304,"FA-CAN, message (data, display Gear train, 0x3FD): in the absence of"
0xCDB404,"FA-CAN, message (vehicle condition, 0x3A0): in the absence of"
0xCDB504,"FA-CAN, message (mileage/range, 0x330): missing"
0xCDB602,"FA-CAN, message (terminal, 0x12F): alive test"
0xCDB604,"FA-CAN, message (terminal, 0x12F): in the absence of"
0xCDB608,"FA-CAN, message (terminal, 0x12F): checksum incorrect"
0xCDB804,"FA-CAN, message (request, air conditioning, 0x2F9): in the absence of"
0xCDB904,"FA-CAN, message (OBD transmission, 0x396): in the absence of"
0xCDBA04,"FA-CAN, message (sleep, standby, Global FCM, 0x3A5): in the absence of"
0xCDBA10,"FA-CAN, message (torque transmission, Hybrid, 0x8D): alive test"
0xCDBA11,"FA-CAN, message (torque transmission, Hybrid, 0x8D): checksum incorrect"
0xCDBA12,"FA-CAN, message (torque transmission, Hybrid, 0x8D): in the absence of"
0xCDBA13,"FA-CAN, message (data transmission, E-Motor 1, 0x91): alive test"
0xCDBA14,"FA-CAN, message (data transmission, E-Motor 1, 0x91): checksum incorrect"
0xCDBA15,"FA-CAN, message (Should missing data transmission E-Motor 1, 0x91): "
0xCDBA17,"FA-CAN, message (release cooling the high-voltage storage, 0x37B): missing"
0xCDBA20,"FA-CAN, message (data E-Motor 1, 0x90): alive test"
0xCDBA21,"FA-CAN, message (data E-Motor 1, 0x90): checksum incorrect"
0xCDBA22,"FA-CAN, message (data E-Motor 1, 0x90): missing"
0xCDBA25,"FA-CAN, message (diagnostic OBD engine control Electrically, 0x3E8): in the absence of"
0xCDBA30,"FA-CAN, message (data E-Motor 1 a long time, 0x25B): alive test"
0xCDBA31,"FA-CAN, message (data E-Motor 1 a long time, 0x25B): checksum incorrect"
0xCDBA32,"FA-CAN, message (data E-Motor 1 a long time, 0x25B): in the absence of"
0xCDBB02,"A-CAN, message (request torque crankshaft transmission 2, 0xA0): alive test"
0xCDBB04,"A-CAN, message (request torque crankshaft transmission 2, 0xA0): missing"
0xCDBB08,"A-CAN, message (request torque crankshaft transmission 2, 0xA0): checksum incorrect"
0xCDBB10,"A-CAN, message (Status gear Hybrid, 0x409): alive test"
0xCDBB11,"A-CAN, message (Status gear Hybrid, 0x409): checksum incorrect"
0xCDBB12,"A-CAN, message (Status gear Hybrid, 0x409): missing"
0xCDBB20,"A-CAN, message (Status leak diagnosis pressure tank, 0x2E7): alive test"
0xCDBB21,"A-CAN, message (Status leak diagnosis pressure tank, 0x2E7): checksum incorrect"
0xCDBB22,"A-CAN, message (Status leak diagnosis pressure tank, 0x2E7): in the absence of"
0xCDBC04,"A-CAN, message (request-performance Electrically PCU, 0x33F): in the absence of"
0xCDBC10,"A-CAN, message (data drive Electrically, 0x32F): in the absence of"
0xCDBC20,"A-CAN, message (Status drive Hybrid, 0x3A4): alive test"
0xCDBC21,"A-CAN, message (Status drive Hybrid, 0x3A4): checksum incorrect"
0xCDBC22,"A-CAN, message (Status drive Hybrid, 0x3A4): in the absence of"
0xCDBC23,"A-CAN, message (diagnostic OBD engine control Electrically, 0x3E8): in the absence of"
0xCDBD04,"A-CAN, message (Status, energy production, BN2, 0x2AF): in the absence of"
0xCDBE02,"A-CAN, message (display speed, engine dynamics, 0xF8): alive test"
0xCDBE04,"A-CAN, message (display speed, engine dynamics, 0xF8): missing"
0xCDBE20,"A-CAN, message (possibility of motor start-motor stop. 0x3EC): alive test"
0xCDBE21,"A-CAN, message (possibility of motor start-motor stop. 0x3EC): checksum incorrect"
0xCDBE22,"A-CAN, message (possibility of motor start-motor stop. 0x3EC): in the absence of"
0xCDBF04,"A-CAN, message (Status transmission control unit, 0x39A): in the absence of"
0xCDBF20,"A-CAN, message (data is the internal combustion engine E-Motor 1, 0x407): alive test"
0xCDBF21,"A-CAN, message (data is the internal combustion engine E-Motor 1, 0x407): checksum incorrect"
0xCDBF22,"A-CAN, message (data is the internal combustion engine E-Motor 1, 0x407): in the absence of"
0xCDC004,"A-CAN, message (OBD transmission, 0x396): in the absence of"
0xCDC102,"A-CAN, message (data transmission continuous, 0x1AF): alive test"
0xCDC104,"A-CAN, message (data transmission continuous, 0x1AF): in the absence of"
0xCDC108,"A-CAN, message (data transmission continuous, 0x1AF): checksum incorrect"
0xCDC202,"A-CAN, message (request torque crankshaft gear, 0xB0): alive test"
0xCDC204,"A-CAN, message (request torque crankshaft gear, 0xB0): in the absence of"
0xCDC208,"A-CAN, message (request torque crankshaft gear, 0xB0): checksum incorrect"
0xCDC304,"A-CAN, message (Status Electrical fuel pump, 0x335): in the absence of"
0xFFFFFF,unknown error location
