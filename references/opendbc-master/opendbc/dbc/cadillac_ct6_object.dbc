VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: RRSRR_FO LRSRR_FO CIPM_FO _DOFIMU2_FO _DOFIMU1_FO DMS_FO AMM_FO EOCM2B_IMX6_FO EOCM2B_K2_FO EOCM2B_K1_FO EOCM2A_IMX6_FO EOCM2A_K2_FO EOCM2A_K1_FO NVS_FO Dummy_FO TestTool_FO LRR_FO RFSRR_FO LFSRR_FO RSRR_FO VIS_FO EOCM_F_FO VIS2_FO
VAL_TABLE_ vt_BooleanValues 1 "true" 0 "false" ;
VAL_TABLE_ FrntVsnInPthVehBrkNwSt 10 "Active" 5 "Inactive" ;
VAL_TABLE_ FrntVsnClostPedBrkNwSt 10 "Active" 5 "Inactive" ;
VAL_TABLE_ DrvrMonSysEngSt 7 "Unused and Reserved 1" 6 "Recovering" 5 "Tracking" 4 "Searching" 3 "Video test port only" 2 "Idle" 1 "Invalid state" 0 "Does not exist or DME" ;
VAL_TABLE_ DrvrMonEngUnrecvrFltCod 7 "Unused and Reserved 3" 6 "Unused and Reserved 2" 5 "Unused and Reserved 1" 4 "Vehicle power supply Errors" 3 "Problem with LED illuminators" 2 "Vehicle input signals Errors" 1 "Problem with imager" 0 "Ok" ;
VAL_TABLE_ DrvrMonEngRecvrFltCod 3 "Engine is unable to find a face" 2 "Input Images too dark" 1 "Input images too bright" 0 "Ok" ;
VAL_TABLE_ DrvrMntrSysVTP 1 "Video test port active" 0 "Video test port inactive" ;
VAL_TABLE_ DrvrAttnStatCnfdc 3 "High" 2 "Medium" 1 "Low" 0 "Lowest" ;
VAL_TABLE_ DrvrAttnStat 7 "Invalid" 6 "Driver is exhibiting sleep" 5 "Driver is exhibiting microsleep" 4 "Attention is Center Console" 3 "Attention is Drivers Lap" 2 "Attention is Off Road" 1 "Attention is On Road" 0 "Unknown" ;
VAL_TABLE_ PPSMd 7 "GNSS and RTX and DR and MM" 6 "DR ONLY" 5 "GNSS and RTX and DR" 4 "GNSS and SBAS and DR" 3 "GNSS and DR" 2 "GNSS and  RTX" 1 "GNSS and SBAS" 0 "GNSS Standalone" ;
VAL_TABLE_ AdvDrvAstMpPrfShrtAcur 3 "Accuracy Is Unknown" 2 "Lowest Accuracy" 1 "Medium Accuracy" 0 "Highest Accuracy" ;
VAL_TABLE_ AdvDrAstMpStbRtOfWay 3 "Not Applicable" 2 "Unknown" 1 "Sub Path Has Right Of Way Over Path" 0 "Path Has Right Of Way Over Sub Path" ;
VAL_TABLE_ AdvDrAstMpStbPrtCalRut 3 "Not Applicable" 2 "Unknown" 1 "Path From This Point On Is Part Of Calculated Route" 0 "Path From This Point On Is Not Part Of Calculated Route" ;
VAL_TABLE_ AdvDrAstMpStbMsgTyp 7 "Unused and Reserved 2" 6 "Metadata" 5 "Profile Long" 4 "Profile Short" 3 "Stub" 2 "Segment" 1 "Position" 0 "Unused and Reserved 1" ;
VAL_TABLE_ AdvDrAstMpStbFmOfWay 15 "Not Applicable" 14 "Pedestrian Zone" 13 "Entrance To Or Exit To Service" 12 "Entrance To Or Exit Of A Car Park" 11 "Service Road Or Frontage Road" 10 "Slip Road per Ramp" 9 "Slip Road per Ramp On A Freeway Or Controlled Access Road" 8 "Parallel Road" 7 "Unused and Reserved 2" 6 "Unused and Reserved 1" 5 "Traffic Square per Special Traffic Figure" 4 "Roundabout Circle" 3 "Single Carriageway" 2 "Multiple Carriageway Or Multiply Digitized Road" 1 "Freeway Or Controlled Access Road That Is Not A Slip Road Or Ramp" 0 "Unknown" ;
VAL_TABLE_ AdvDrAstMpStbCmplxInsct 3 "Not Applicable" 2 "Unknown" 1 "Stub Is Part Of Complex Intersection" 0 "Stub Is Not Part Of Complex Intersection" ;
VAL_TABLE_ AdvDrAstMpSegTunl 3 "Not Applicable" 2 "Unknown" 1 "Segment Is Part Of Tunnel" 0 "Segment Is Not A Part Of Tunnel" ;
VAL_TABLE_ AdvDrAstMpSegPrtCalRut 3 "Not Applicable" 2 "Unknown" 1 "Segment Is Part Of Calculated Route" 0 "Segment Is Not Part Of Calculated Route" ;
VAL_TABLE_ AdvDrAstMpSegMsgTyp 7 "Unused and Reserved 2" 6 "Metadata" 5 "Profile Long" 4 "Profile Short" 3 "Stub" 2 "Segment" 1 "Position" 0 "Unused and Reserved 1" ;
VAL_TABLE_ AdvDrAstMpSegFrmOfWay 15 "Not Applicable" 14 "Pedestrian Zone" 13 "Entrance To Or Exit To Service" 12 "Entrance To Or Exit Of A Car Park" 11 "Service Road Or Frontage Road" 10 "Slip Road per Ramp" 9 "Slip Road per Ramp On A Freeway Or Controlled Access Road" 8 "Parallel Road" 7 "Unused and Reserved 2" 6 "Unused and Reserved 1" 5 "Traffic Square per Special Traffic Figure" 4 "Roundabout Circle" 3 "Single Carriageway" 2 "Multiple Carriageway Or Multiply Digitized Road" 1 "Freeway Or Controlled Access Road That Is Not A Slip Road or Ramp" 0 "Unknown" ;
VAL_TABLE_ AdvDrAstMpSegEffSdLmtTp 7 "Not Applicable" 6 "Explicit Snow" 5 "Explicit Rain" 4 "Explicit Time Of Day" 3 "Explicit By Day" 2 "Explicit By Night" 1 "Explicit On Traffic Sign" 0 "Implicit" ;
VAL_TABLE_ AdvDrAstMpSegDivdRd 3 "Not Applicable" 2 "Unknown" 1 "Segment Is Part Of Divided Road" 0 "Segment Is Not Part Of Divided Road" ;
VAL_TABLE_ AdvDrAstMpSegCmplxInsct 3 "Not Applicable" 2 "Unknown" 1 "Segment Is Part Of Complex Intersection" 0 "Segment Is Not Part Of Complex Intersection" ;
VAL_TABLE_ AdvDrAstMpSegBrdg 3 "Not Applicable" 2 "Unknown" 1 "Segment Is Part Of Bridge" 0 "Segment Not Part Of Bridge" ;
VAL_TABLE_ AdvDrAstMpSegBldUpAra 3 "Not Applicable" 2 "Unknown" 1 "Segment Is Part Of Built Up Area" 0 "Segment Is Not Part Of Built Up Area" ;
VAL_TABLE_ AdvDrAstMpMtdtSpdUnt 1 "Miles Per Hour" 0 "Kilometers Per Hour" ;
VAL_TABLE_ AdvDrAstMpMtdtDrvSd 1 "Driving Side Right" 0 "Driving Side Left" ;
VAL_TABLE_ WSMR_WiFiAssnReq 3 "Unexpected Undefined Connection Behavior" 2 "Wi Fi association failed with available credentials" 1 "Failed to receive Wi Fi credentials after 255 attempts" 0 "SSID and Passphrase Request" ;
VAL_TABLE_ StrgColCommsFlt 2 "Disabled Communications DTC" 1 "No Communications Fault" 0 "Communications Fault" ;
VAL_TABLE_ CPMAPINFO4 1 "Hardware Or Software Error" 0 "No Hardware Or Software Error" ;
VAL_TABLE_ AdvDrvAstMpPrfShrt2Acur 3 "Accuracy Is Unknown" 2 "Lowest Accuracy" 1 "Medium Accuracy" 0 "Highest Accuracy" ;
VAL_TABLE_ InterLghtStat 1 "Interior Lights On" 0 "Interior Light Off" ;
VAL_TABLE_ VehLnStatConf 2 "High Confidence" 1 "Low Confidence" 0 "No Confidence" 3 "Very High Confidence" ;
VAL_TABLE_ VehLnStat 3 "Lane Change To Right" 2 "Lane Change To Left" 1 "Staying in Lane" 0 "Unknown" ;
VAL_TABLE_ HrznPrvdRstRq 1 "Reset Not Required" 0 "Reset Required" ;
VAL_TABLE_ ExptNxtTrnstnDirConf 3 "Very High Confidence" 2 "High Confidence" 1 "Low Confidence" 0 "No Confidence" ;
VAL_TABLE_ ExptNxtTrnstnDir 7 "Reserved4" 6 "Reserved3" 5 "Reserved2" 4 "Reserved1" 3 "Traversing Middle Transition" 2 "Traversing Next Transition Right" 1 "Traversing Next Transition Left" 0 "Unknown" ;
VAL_TABLE_ ADASISMsgTypRetrans 7 "GM System Specific" 6 "Meta Data" 5 "Profile Long" 4 "Profile Short" 3 "Stub" 2 "Segment" 1 "Position" 0 "Checksum" ;
VAL_TABLE_ NVSysStat 7 "Needs Headlights" 6 "Temporarily Unavailable" 5 "Not Dark" 4 "Needs Service" 3 "Active" 2 "Inactive" 1 "Initializing" 0 "Unknown" ;
VAL_TABLE_ NVSysPedWrnIndReq 3 "Unused & Reserved" 2 "Pedestrian Alert" 1 "Pedestrian Detected" 0 "None" ;
VAL_TABLE_ NVSysPedDetCstReq 2 "On" 1 "Off" 0 "No Value" ;
VAL_TABLE_ NVSysPedDetCsCrStVal 2 "On" 1 "Off" 0 "No Value" ;
VAL_TABLE_ FwVsnCinCoutPotT9Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT8Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT7Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT6Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT5Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT4Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT3Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT2Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT1Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT12Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT11Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT10Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ GFHBObjDirTrk8 1 "Ahead Traffic" 0 "Incoming Traffic" ;
VAL_TABLE_ GFHBObjDirTrk7 1 "Ahead Traffic" 0 "Incoming Traffic" ;
VAL_TABLE_ GFHBObjDirTrk6 1 "Ahead Traffic" 0 "Incoming Traffic" ;
VAL_TABLE_ GFHBObjDirTrk5 1 "Ahead Traffic" 0 "Incoming Traffic" ;
VAL_TABLE_ GFHBObjDirTrk4 1 "Ahead Traffic" 0 "Incoming Traffic" ;
VAL_TABLE_ GFHBObjDirTrk3 1 "Ahead Traffic" 0 "Incoming Traffic" ;
VAL_TABLE_ GFHBObjDirTrk2 1 "Ahead Traffic" 0 "Incoming Traffic" ;
VAL_TABLE_ GFHBObjDirTrk1 1 "Ahead Traffic" 0 "Incoming Traffic" ;
VAL_TABLE_ GFHBFwVsnRelLaneTrk8 3 "Left Lane" 2 "Right Lane" 1 "Host Lane" 0 "Unknown" ;
VAL_TABLE_ GFHBFwVsnRelLaneTrk7 3 "Left Lane" 2 "Right Lane" 1 "Host Lane" 0 "Unknown" ;
VAL_TABLE_ GFHBFwVsnRelLaneTrk6 3 "Left Lane" 2 "Right Lane" 1 "Host Lane" 0 "Unknown" ;
VAL_TABLE_ GFHBFwVsnRelLaneTrk5 3 "Left Lane" 2 "Right Lane" 1 "Host Lane" 0 "Unknown" ;
VAL_TABLE_ GFHBFwVsnRelLaneTrk4 3 "Left Lane" 2 "Right Lane" 1 "Host Lane" 0 "Unknown" ;
VAL_TABLE_ GFHBFwVsnRelLaneTrk3 3 "Left Lane" 2 "Right Lane" 1 "Host Lane" 0 "Unknown" ;
VAL_TABLE_ GFHBFwVsnRelLaneTrk2 3 "Left Lane" 2 "Right Lane" 1 "Host Lane" 0 "Unknown" ;
VAL_TABLE_ GFHBFwVsnRelLaneTrk1 3 "Left Lane" 2 "Right Lane" 1 "Host Lane" 0 "Unknown" ;
VAL_TABLE_ GFHBFwVsnMesStatTrk8 3 "Measured this cycle" 2 "Latent track not detected this cycle" 1 "New Object" 0 "No Object" ;
VAL_TABLE_ GFHBFwVsnMesStatTrk7 3 "Measured this cycle" 2 "Latent track not detected this cycle" 1 "New Object" 0 "No Object" ;
VAL_TABLE_ GFHBFwVsnMesStatTrk6 3 "Measured this cycle" 2 "Latent track not detected this cycle" 1 "New Object" 0 "No Object" ;
VAL_TABLE_ GFHBFwVsnMesStatTrk5 3 "Measured this cycle" 2 "Latent track not detected this cycle" 1 "New Object" 0 "No Object" ;
VAL_TABLE_ GFHBFwVsnMesStatTrk4 3 "Measured this cycle" 2 "Latent track not detected this cycle" 1 "New Object" 0 "No Object" ;
VAL_TABLE_ GFHBFwVsnMesStatTrk3 3 "Measured this cycle" 2 "Latent track not detected this cycle" 1 "New Object" 0 "No Object" ;
VAL_TABLE_ GFHBFwVsnMesStatTrk2 3 "Measured this cycle" 2 "Latent track not detected this cycle" 1 "New Object" 0 "No Object" ;
VAL_TABLE_ GFHBFwVsnMesStatTrk1 3 "Measured this cycle" 2 "Latent track not detected this cycle" 1 "New Object" 0 "No Object" ;
VAL_TABLE_ GFHBFwVsnCnfdncTrk8 3 "Confident" 2 "Speculative" 1 "Highly Speculative" 0 "Invalid" ;
VAL_TABLE_ GFHBFwVsnCnfdncTrk7 3 "Confident" 2 "Speculative" 1 "Highly Speculative" 0 "Invalid" ;
VAL_TABLE_ GFHBFwVsnCnfdncTrk6 3 "Confident" 2 "Speculative" 1 "Highly Speculative" 0 "Invalid" ;
VAL_TABLE_ GFHBFwVsnCnfdncTrk5 3 "Confident" 2 "Speculative" 1 "Highly Speculative" 0 "Invalid" ;
VAL_TABLE_ GFHBFwVsnCnfdncTrk4 3 "Confident" 2 "Speculative" 1 "Highly Speculative" 0 "Invalid" ;
VAL_TABLE_ GFHBFwVsnCnfdncTrk3 3 "Confident" 2 "Speculative" 1 "Highly Speculative" 0 "Invalid" ;
VAL_TABLE_ GFHBFwVsnCnfdncTrk2 3 "Confident" 2 "Speculative" 1 "Highly Speculative" 0 "Invalid" ;
VAL_TABLE_ GFHBFwVsnCnfdncTrk1 0 "Invalid" 3 "Confident" 2 "Speculative" 1 "Highly Speculative" ;
VAL_TABLE_ GFHBFVsnObjTypTrkRev8 12 "Cluster Object" 11 "Child Close to Danger Zone" 10 "Pedestrian Moving Out of Danger Zone" 9 "Pedestrian Moving Into Danger Zone" 8 "Pedestrian Standing Outside Danger Zone" 7 "No Object Present" 6 "Fixed Roadside Object" 5 "Fixed Overhead Object" 4 "Pedestrian" 3 "Motorcycle/ Bicycle" 2 "Large Vehicle (Semi)" 1 "4 Wheel Vehicle (Car)" 0 "Unknown" ;
VAL_TABLE_ GFHBFVsnObjTypTrkRev7 12 "Cluster Object" 11 "Child Close to Danger Zone" 10 "Pedestrian Moving Out of Danger Zone" 9 "Pedestrian Moving Into Danger Zone" 8 "Pedestrian Standing Outside Danger Zone" 7 "No Object Present" 6 "Fixed Roadside Object" 5 "Fixed Overhead Object" 4 "Pedestrian" 3 "Motorcycle/ Bicycle" 2 "Large Vehicle (Semi)" 1 "4 Wheel Vehicle (Car)" 0 "Unknown" ;
VAL_TABLE_ GFHBFVsnObjTypTrkRev6 12 "Cluster Object" 11 "Child Close to Danger Zone" 10 "Pedestrian Moving Out of Danger Zone" 9 "Pedestrian Moving Into Danger Zone" 8 "Pedestrian Standing Outside Danger Zone" 7 "No Object Present" 6 "Fixed Roadside Object" 5 "Fixed Overhead Object" 4 "Pedestrian" 3 "Motorcycle/ Bicycle" 2 "Large Vehicle (Semi)" 1 "4 Wheel Vehicle (Car)" 0 "Unknown" ;
VAL_TABLE_ GFHBFVsnObjTypTrkRev5 12 "Cluster Object" 11 "Child Close to Danger Zone" 10 "Pedestrian Moving Out of Danger Zone" 9 "Pedestrian Moving Into Danger Zone" 8 "Pedestrian Standing Outside Danger Zone" 7 "No Object Present" 6 "Fixed Roadside Object" 5 "Fixed Overhead Object" 4 "Pedestrian" 3 "Motorcycle/ Bicycle" 2 "Large Vehicle (Semi)" 1 "4 Wheel Vehicle (Car)" 0 "Unknown" ;
VAL_TABLE_ GFHBFVsnObjTypTrkRev4 12 "Cluster Object" 11 "Child Close to Danger Zone" 10 "Pedestrian Moving Out of Danger Zone" 9 "Pedestrian Moving Into Danger Zone" 8 "Pedestrian Standing Outside Danger Zone" 7 "No Object Present" 6 "Fixed Roadside Object" 5 "Fixed Overhead Object" 4 "Pedestrian" 3 "Motorcycle/ Bicycle" 2 "Large Vehicle (Semi)" 1 "4 Wheel Vehicle (Car)" 0 "Unknown" ;
VAL_TABLE_ GFHBFVsnObjTypTrkRev3 12 "Cluster Object" 11 "Child Close to Danger Zone" 10 "Pedestrian Moving Out of Danger Zone" 9 "Pedestrian Moving Into Danger Zone" 8 "Pedestrian Standing Outside Danger Zone" 7 "No Object Present" 6 "Fixed Roadside Object" 5 "Fixed Overhead Object" 4 "Pedestrian" 3 "Motorcycle/ Bicycle" 2 "Large Vehicle (Semi)" 1 "4 Wheel Vehicle (Car)" 0 "Unknown" ;
VAL_TABLE_ GFHBFVsnObjTypTrkRev2 12 "Cluster Object" 11 "Child Close to Danger Zone" 10 "Pedestrian Moving Out of Danger Zone" 9 "Pedestrian Moving Into Danger Zone" 8 "Pedestrian Standing Outside Danger Zone" 7 "No Object Present" 6 "Fixed Roadside Object" 5 "Fixed Overhead Object" 4 "Pedestrian" 3 "Motorcycle/ Bicycle" 2 "Large Vehicle (Semi)" 1 "4 Wheel Vehicle (Car)" 0 "Unknown" ;
VAL_TABLE_ GFHBFVsnObjTypTrkRev1 12 "Cluster Object" 11 "Child Close to Danger Zone" 10 "Pedestrian Moving Out of Danger Zone" 9 "Pedestrian Moving Into Danger Zone" 8 "Pedestrian Standing Outside Danger Zone" 7 "No Object Present" 6 "Fixed Roadside Object" 5 "Fixed Overhead Object" 4 "Pedestrian" 3 "Motorcycle/ Bicycle" 2 "Large Vehicle (Semi)" 1 "4 Wheel Vehicle (Car" 0 "Unknown" ;
VAL_TABLE_ CRL_Cnfdnc 7 "Reserved4" 6 "Reserved3" 5 "Reserved2" 4 "Reserved1" 3 "Best" 2 "Marking Present" 1 "Weak Marking" 0 "No Lane" ;
VAL_TABLE_ CLL_Cnfdnc 7 "Reserved4" 6 "Reserved3" 5 "Reserved2" 4 "Reserved1" 3 "Best" 2 "Marking Present" 1 "Weak Marking" 0 "No Lane" ;
VAL_TABLE_ VBBrkCtrlSt 4 "Release Control" 3 "Hold Vehicle" 2 "Apply Brake" 1 "Decrease Torque" 0 "No Action" ;
VAL_TABLE_ ObstacleType 6 "NO_OBJECT" 5 "OBJ_GUARDRAIL" 4 "OBJ_TUNNEL " 3 "OBJ_TRAFFIC_SIGN" 2 "OBJ_VEHICLE" 1 "OBJ_BRIDGE " 0 "OBJ_UNKNOWN" ;
VAL_TABLE_ VpathMode 2 "Mid Hi Speed" 1 "Low Speed" 0 "Disabled" ;
VAL_TABLE_ LaneChngStatus 2 "Lane Change Right" 1 "Lane Change Left" 0 "Idle" ;
VAL_TABLE_ TravelDirection 3 "Other (excessive side slip)" 2 "reverse / stopped in rvrs gear" 1 "Forward / stopped in frwd gear" 0 "Unknown" ;
VAL_TABLE_ TrueOrFalse 1 "true" 0 "false" ;
VAL_TABLE_ ModeCommand 5 "Sensing with Reduced Power" 4 "Undefined" 3 "Radio Silent" 2 "Sensing" 1 "Not Sensing" 0 "Undefined" ;
VAL_TABLE_ LaneSnsLLnPosValid 1 "Invalid" 0 "Valid" ;
VAL_TABLE_ LnSnsRLnPosValid 1 "Invalid" 0 "Valid" ;
VAL_TABLE_ Elevation 3 "Low " 2 "mid (reserved)" 1 "high " 0 "Unknown" ;
VAL_TABLE_ ModeCommandFdbk 5 "Sensing with reduced power" 4 "Undefined" 3 "Radio Silent" 2 "Sensing" 1 "Not Sensing" 0 "Undefined" ;
VAL_TABLE_ relativeLane 3 "Left Lane" 2 "Right Lane" 1 "Host Lane" 0 "Unknown" ;
VAL_TABLE_ RoadTypeInfo 5 "Constiction Zone Exit" 4 "Construction Zone Entry" 3 "Highway" 2 "Secondary Road" 1 "City" 0 "Unknown" ;
VAL_TABLE_ ObjectLossInfo 2 "elevation conditions" 1 "tight curve" 0 "not lost" ;
VAL_TABLE_ MeasurementStatus 3 "Measured this cycle" 2 "Latent track not detctd this cyc" 1 "New Object" 0 "No Object" ;
VAL_TABLE_ ObjectType 7 "no object present" 6 "fixed roadside object" 5 "fixed overhead object" 4 "pedestrian" 3 "motocycle  / bicycle" 2 "Large vehicle (semi)" 1 "4 Wheel Vehicle (car, small trk)" 0 "Unknown" ;
VAL_TABLE_ Confidence 3 "confident" 2 "speculative" 1 "highly speculative" 0 "invalid" ;
VAL_TABLE_ DynamicProp 4 "Moving in opposite direction" 3 "Moving in same direction as host" 2 "Has moved but currently stopped" 1 "Has never moved" 0 "Unknown" ;
VAL_TABLE_ DrvWndPosSt 6 "Fully Opened" 5 "Open More Than C" 4 "Position C" 3 "Position B" 2 "Position A" 1 "Open Less Than A" 0 "Fully Closed" ;
VAL_TABLE_ VehMovSta 4 "Invalid" 3 "Reverse" 2 "Forward" 1 "Neutral" 0 "Parked" ;
VAL_TABLE_ OtsdAmbtLtLvlStat 2 "Day" 1 "Night" 0 "Unknown" ;
VAL_TABLE_ RtTrnLmpAtv 2 "On with telltale" 1 "On without telltale" 0 "Off" ;
VAL_TABLE_ LftTrnLmpAtv 2 "On with telltale" 1 "On without telltale" 0 "Off" ;
VAL_TABLE_ HdlmpBmSelectStat 2 "High Beams" 1 "Low Beams" 0 "Unknown" ;
VAL_TABLE_ DTCI_DTCFaultType 3 "Type C" 2 "Type B" 1 "Type A" 0 "Not Supported" ;
VAL_TABLE_ TrnsShftLvrPos 13 "Forward Range J" 12 "Forward Range I" 15 "Lever Position Unknown" 11 "Forward Range H" 10 "Forward Range G" 9 "Forward Range F" 8 "Forward Range E" 7 "Forward Range D" 6 "Forward Range C" 5 "Forward Range B" 4 "Forward Range A" 3 "Neutral Range" 2 "Reverse Range" 1 "Park Range" 0 "Between Ranges" ;
VAL_TABLE_ SysPwrMd 3 "Crank Request" 2 "Run" 1 "Accessory" 0 "Off" ;
VAL_TABLE_ ValidityStates 1 "Invalid" 0 "Valid" ;


BO_ 1548 ADAS_Profile_Short2_FO: 8 AMM_FO
 SG_ AdvDrvAstMpPrfShrt2Val1 : 1|10@0+ (1,0) [0|1023] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2Val0 : 33|10@0+ (1,0) [0|1023] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2Updt : 39|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2RTrns : 3|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2Typ : 38|5@0+ (1,0) [0|31] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2PthIdx : 23|6@0+ (1,0) [0|63] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2Ofst : 52|13@0+ (1,0) [0|8191] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2MsgTp : 55|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2Dist1 : 17|10@0+ (1,0) [0|1023] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2CycCnt : 7|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2CtlPt : 2|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpPrfShrt2Acur : 5|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 1547 ADAS_Profile_Long2_FO: 8 AMM_FO
 SG_ AdvDrAstMpProfLng2Val : 39|32@0+ (1,0) [0|4294967295] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfLng2Updt : 7|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfLng2RTrns : 6|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfLng2PrfTyp : 4|5@0+ (1,0) [0|31] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfLng2PthIdx : 13|6@0+ (1,0) [0|63] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfLng2Ofst : 20|13@0+ (1,0) [0|8191] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfLng2MgTyp : 23|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfLng2CycCt : 15|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfLng2CtrlPt : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 1630 USDT_Resp_From_EOCM2B_K2_FO: 8 EOCM2B_K2_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1374 UUDT_Resp_From_EOCM2B_K2_FO: 8 EOCM2B_K2_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 606 USDT_Req_to_EOCM2B_K2_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  EOCM2B_K2_FO

BO_ 328 Vehicle_Info_FO: 5 EOCM2A_K1_FO
 SG_ StrWhAngGrd : 27|12@0- (1,0) [-2048|2047] "deg/sec"  DMS_FO
 SG_ TrnsShftLvrPos : 31|4@0+ (1,0) [0|15] ""  DMS_FO
 SG_ StrWhAng_148 : 15|16@0- (0.0625,0) [-2048|2047.9375] "deg"  DMS_FO
 SG_ TrnsShftLvrPosV : 3|1@0+ (1,0) [0|1] ""  DMS_FO
 SG_ StrWhAngV_148 : 4|1@0+ (1,0) [0|1] ""  DMS_FO
 SG_ StrWhAngMsk_148 : 5|1@0+ (1,0) [0|1] ""  DMS_FO
 SG_ StrWhAngGrdV : 6|1@0+ (1,0) [0|1] ""  DMS_FO
 SG_ StrWhAngGrdMsk : 7|1@0+ (1,0) [0|1] ""  DMS_FO

BO_ 1930 DTC_Triggered_78A_FO: 7 LRSRR_FO
 SG_ DTCI_CodeSupported_78A : 40|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_78A : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCFailType_78A : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_78A : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_78A : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCSource_78A : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_78A : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_78A : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_78A : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_78A : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_78A : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_78A : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_78A : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_78A : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_78A : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_78A : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_78A : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_78A : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_78A : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_78A : 47|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1624 USDT_Resp_From_LRSRR: 8 LRSRR_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1368 UUDT_Resp_From_LRSRR: 8 LRSRR_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 600 USDT_Req_to_LRSRR: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  LRSRR_FO

BO_ 613 PPS_QualMetrics_FO: 8 EOCM2A_K1_FO
 SG_ PPSPstnDilPrcs : 47|10@0+ (0.1,0) [0|102.3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSMd : 53|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPS3DAbsPosErrEstmt : 9|10@0+ (0.1,0) [0|102.3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSAbsHdngErrEstmt : 0|7@0+ (0.5,0) [0|63.5] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSAbsVelErrEstmt : 30|5@0+ (1,0) [0|31] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSPosQltyMtrcsChksm : 50|11@0+ (1,0) [0|2047] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPS2DAbsPosErrEstmt : 25|10@0+ (0.1,0) [0|102.3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSPosQltyMtcBrstID : 2|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPS2DAbsPosErrEstmtV : 5|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPS3DAbsPosErrEstmtV : 4|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSAbsHdngErrEstmtV : 3|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSAbsVelErrEstmtV : 31|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSMdV : 7|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSPstnDilPrcsV : 6|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO

BO_ 612 PPS_Time_FO: 8 EOCM2A_K1_FO
 SG_ PPSTmdayV : 2|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSCldrDayV : 0|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSCldrDay : 8|9@0+ (1,0) [0|511] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSTmBrstID : 52|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSTmday : 31|27@0+ (1,0) [0|134217727] "ms"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSTmChksm : 50|11@0+ (1,0) [0|2047] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSCldrYrV : 1|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSCldrYr : 15|7@0+ (1,2014) [2014|2141] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO

BO_ 611 PPS_SigAcqTime_FO: 6 EOCM2A_K1_FO
 SG_ PPSSigAqTmBrstID : 38|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSSigAcqTmV : 39|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSSigAcqTm : 7|32@0+ (1,0) [0|4294967295] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSSigAqTmChksm : 34|11@0+ (1,0) [0|2047] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO

BO_ 610 PPS_PosLong_FO: 6 EOCM2A_K1_FO
 SG_ PPSLongBrstID : 39|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSLong : 6|31@0- (1,0) [-1073741824|1073741823] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSLongV : 7|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSLongChksm : 34|11@0+ (1,0) [0|2047] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO

BO_ 609 PPS_PosLat_FO: 6 EOCM2A_K1_FO
 SG_ PPSLatV : 7|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSLatChksm : 34|11@0+ (1,0) [0|2047] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSLatBrstID : 36|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSLat : 5|30@0- (1,0) [-536870912|536870911] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO

BO_ 1160 PassPhrase_3_AMM_FO: 8 EOCM2A_K1_FO
 SG_ WiFiPssPhrsDgts17to24_Mp : 7|64@0+ (1,0) [0|0] ""  EOCM2B_K1_FO,AMM_FO

BO_ 1159 PassPhrase_2_AMM_FO: 8 EOCM2A_K1_FO
 SG_ WiFiPssPhrsDgts9to16_Mp : 7|64@0+ (1,0) [0|0] ""  EOCM2B_K1_FO,AMM_FO

BO_ 1158 PassPhrase_1_AMM_FO: 8 EOCM2A_K1_FO
 SG_ WiFiPssPhrsDgts1to8_Mp : 7|64@0+ (1,0) [0|0] ""  EOCM2B_K1_FO,AMM_FO

BO_ 1546 ADAS_Protection_FO: 4 AMM_FO
 SG_ AdvDrAstMpPrfLng2Avbl : 11|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPosAvbl : 15|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpHwFlt : 7|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtAvbl : 8|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpProtChksm : 23|16@0+ (1,0) [0|65535] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpProtRTrns : 0|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpProtCycCtMsg : 2|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbAvbl : 9|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpSegAvbl : 10|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfShrtAvbl : 14|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfShrt2Avbl : 13|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrAstMpPrfLngAvbl : 12|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ AdvDrvAstMpInpSigFld : 6|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO

BO_ 1545 ADAS_Metadata_FO: 8 AMM_FO
 SG_ AdvDrAstMpMtdtVerYrQtr : 60|2@0+ (1,1) [1|4] "Qtr"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtVerYr : 53|6@0+ (1,2000) [2000|2063] "Year"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtSpdUnt : 39|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtPrvdr : 7|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtMsgTyp : 58|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtMnrPrtVr : 23|4@0+ (1,0) [0|15] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtCycCnt : 18|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtRgnCd : 38|15@0+ (1,0) [0|32767] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtHwVer : 16|9@0+ (1,0) [0|511] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtMnrPrtSbVr : 4|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtMjrPrtVr : 55|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtDrvSd : 19|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpMtdtCntryCd : 1|10@0+ (1,0) [0|1023] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO

BO_ 1543 ADAS_Profile_Long_FO: 8 AMM_FO
 SG_ AdvDrAstMpPrfLngUpdt : 7|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPrfLngRTrns : 6|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPrfLngCtlPt : 5|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPrfLngTyp : 4|5@0+ (1,0) [0|31] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPrfLngCycCt : 15|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPrfLngPthIdx : 13|6@0+ (1,0) [0|63] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPrfLngMsgTp : 23|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPrfLngOfst : 20|13@0+ (1,0) [0|8191] "m"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPrfLngVal : 39|32@0+ (1,0) [0|4294967295] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO

BO_ 1544 ADAS_Profile_Short_FO: 8 AMM_FO
 SG_ AdvDrvAstMpPrfShrtVal1 : 1|10@0+ (1,0) [0|1023] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtVal0 : 33|10@0+ (1,0) [0|1023] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtDist1 : 17|10@0+ (1,0) [0|1023] "m"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtCycCnt : 7|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtAcur : 5|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtCtlPt : 2|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtRetr : 3|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtTyp : 38|5@0+ (1,0) [0|31] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtUpdt : 39|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtPthIdx : 23|6@0+ (1,0) [0|63] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtMsgTp : 55|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrvAstMpPrfShrtOfst : 52|13@0+ (1,0) [0|8191] "m"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO

BO_ 1542 ADAS_Stub_FO: 8 AMM_FO
 SG_ AdvDrAstMpStbSubPthIdx : 63|6@0+ (1,0) [0|63] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbUpdt : 55|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbPrtCalRut : 57|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbNmLnDrvDir : 54|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbCycCnt : 51|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbCmplxInsct : 49|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbTrnAngl : 47|8@0+ (1.417,0) [0|361.335] "deg"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbRtOfWay : 38|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbRelProb : 36|5@0+ (3.333,0) [0|103.323] "%"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbRetr : 39|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbNmLnOppDir : 31|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbPathIdx : 29|6@0+ (1,0) [0|63] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbLstStbOfst : 23|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbMsgTyp : 22|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbFmOfWay : 19|4@0+ (1,0) [0|15] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbFuncRdCls : 7|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpStbOfst : 4|13@0+ (1,0) [0|8191] "m"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO

BO_ 1541 ADAS_Segment_FO: 8 AMM_FO
 SG_ AdvDrAstMpSegRelProb : 12|5@0+ (3.333,0) [0|103.323] "%"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegUpdt : 0|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegTunl : 25|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegCycCnt : 39|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegCmplxInsct : 37|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegBldUpAra : 43|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegBrdg : 41|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegRTrns : 1|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegDivdRd : 31|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegPthIdx : 7|6@0+ (1,0) [0|63] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegPrtCalRut : 27|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegNmLnOppDir : 29|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegNmLnDrvDir : 55|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegMsgTyp : 46|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegFunRdCls : 23|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegFrmOfWay : 35|4@0+ (1,0) [0|15] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegEffSpdLmt : 20|5@0+ (1,0) [0|31] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegEffSdLmtTp : 15|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpSegOffset : 52|13@0+ (1,0) [0|8191] "m"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO

BO_ 608 PPS_ElevHdSpd_FO: 8 EOCM2A_K1_FO
 SG_ PPSVel : 31|8@0+ (1,0) [0|255] "km / h"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSVelV : 3|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSElvHedngSpdBrstID : 5|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSElvHdengSpdChksm : 50|11@0+ (1,0) [0|2047] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSHedngV : 6|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSHedng : 2|19@0+ (0.001,0) [0|524.287] "deg"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSElvtnV : 7|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO
 SG_ PPSElvtn : 39|21@0+ (1,-100000) [-100000|1997151] "cm"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,AMM_FO

BO_ 1168 WiFi_Station_AMM_FO: 5 AMM_FO
 SG_ WSMR_WiFiStnMpMACAddr : 15|32@0+ (1,0) [0|4294967295] ""  EOCM2B_K1_FO,EOCM2A_K1_FO
 SG_ WSMR_WiFiAssnReq : 1|2@0+ (1,0) [0|3] ""  EOCM2B_K1_FO,EOCM2A_K1_FO

BO_ 1161 WiFi_AP_Data_AMM_FO: 2 EOCM2A_K1_FO
 SG_ WAPDM_SecurityType : 15|4@0+ (1,0) [0|15] ""  EOCM2B_K1_FO,AMM_FO
 SG_ WAPDM_WiFiEnStat : 0|1@0+ (1,0) [0|1] ""  EOCM2B_K1_FO,AMM_FO
 SG_ WAPDM_EncrptnType : 11|4@0+ (1,0) [0|15] ""  EOCM2B_K1_FO,AMM_FO

BO_ 1157 SSID_AMM_3_FO: 8 EOCM2A_K1_FO
 SG_ WiFiSSIDDgts17to24_Mp : 7|64@0+ (1,0) [0|0] ""  EOCM2B_K1_FO,AMM_FO

BO_ 326 DMS_Eye_AOI_Info_FO: 6 DMS_FO
 SG_ DrvrMontSysInit : 1|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMontSysAvlbl : 3|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMontMdlDatFlshProgrs : 2|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonEngRecvrFltCodARC : 36|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrAttnStatChksm : 34|11@0+ (1,0) [0|2047] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrAttnStat : 39|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrAttnStatPrd : 22|15@0+ (0.025,0) [0|819.175] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonEngRecvrFltCodV : 23|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonEngUnrecvrFltCod : 10|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonEngUnrecvrFltCodV : 11|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrAttnStatCnfdc : 13|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonEngRecvrFltCod : 15|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrAttnStatV : 0|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO

BO_ 324 DMS_RawMeasurement_Info2_FO: 8 DMS_FO
 SG_ DrvrMonFrmNum : 47|8@0+ (1,0) [0|255] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMntrSysVTP : 29|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonSysEngStV : 2|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonSysEngSt : 28|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonFrmNumV : 3|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrHeadRotAngZV : 4|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrHeadRotAngYV : 5|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrHeadRotAngXV : 6|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrHeadRotAngARC : 52|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonLatV : 7|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrMonLat : 50|11@0+ (0.001,0) [0|2.047] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvHeadRotAngZ : 25|10@0- (0.00625,0) [-3.2|3.19375] "rad"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ DrvHeadRotAngY : 23|10@0- (0.00625,0) [-3.2|3.19375] "rad"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ DrvrHeadRotAngX : 1|10@0- (0.00625,0) [-3.2|3.19375] "rad"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 322 DMS_RawMeasurement_Info1_FO: 8 DMS_FO
 SG_ DrvrEyeClosrRt : 47|8@0+ (0.005,0) [0|1.275] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrEyeClosrLft : 39|8@0+ (0.005,0) [0|1.275] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvHeadPosZV : 3|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvHeadPosYV : 4|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvHeadPosXV : 5|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrEyeClosrRtV : 6|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrEyeClosrLftV : 7|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvrEyeClosrARC : 52|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ DrvHeadPosZ : 50|11@0- (0.005,0) [-5.12|5.115] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ DrvHeadPosY : 18|11@0- (0.005,0) [-5.12|5.115] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ DrvHeadPosX : 2|11@0- (0.005,0) [-5.12|5.115] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 320 Inertial2_Rates_FO: 8 _DOFIMU2_FO
 SG_ MstrTgrSyncInrtl2Rte : 55|5@0+ (1,0) [0|31] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2YawRteV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2YawRte : 36|13@0- (0.024,0) [-98.304|98.28] "deg/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2RollRteV : 6|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2RollRte : 20|13@0- (0.024,0) [-98.304|98.28] "deg/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2RteChksum : 50|11@0+ (1,0) [0|2047] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2PitchRteV : 7|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2PitchRte : 4|13@0- (0.024,0) [-98.304|98.28] "deg/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 308 Inertial1_Rates_FO: 8 _DOFIMU1_FO
 SG_ MstrTrgrSyncInrtl1Rte : 55|5@0+ (1,0) [0|31] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial1YawRteV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial1YawRte : 36|13@0- (0.024,0) [-98.304|98.28] "deg/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial1RollRteV : 6|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial1RollRte : 20|13@0- (0.024,0) [-98.304|98.28] "deg/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial1RteChksum : 50|11@0+ (1,0) [0|2047] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial1PitchRteV : 7|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial1PitchRte : 4|13@0- (0.024,0) [-98.304|98.28] "deg/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 312 Inertial2_Accel2_FO: 8 _DOFIMU2_FO
 SG_ MstrTrigSyncInrtl22 : 55|5@0+ (1,0) [0|31] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSns2AccFrm2Chksm : 50|11@0+ (1,0) [0|2047] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr2YawAccV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr2YawAcc : 36|13@0- (0.024,0) [-98.304|98.28] "deg/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr2RollAccV : 6|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr2RollAcc : 20|13@0- (0.024,0) [-98.304|98.28] "deg/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr2PtchAccV : 7|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr2PtchAcc : 4|13@0- (0.024,0) [-98.304|98.28] "deg/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 306 Inertial1_Accel2_FO: 8 _DOFIMU1_FO
 SG_ MstrTrigSyncInrtl12 : 55|5@0+ (1,0) [0|31] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1AccelFrm2Chksm : 50|11@0+ (1,0) [0|2047] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1YawAccV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1YawAcc : 36|13@0- (0.024,0) [-98.304|98.28] "deg/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1RollAccV : 6|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1RollAcc : 20|13@0- (0.024,0) [-98.304|98.28] "deg/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1PtchAccV : 7|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1PtchAcc : 4|13@0- (0.024,0) [-98.304|98.28] "deg/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 310 Inertial2_Accel1_FO: 8 _DOFIMU2_FO
 SG_ MstrTrigSyncInrtl21 : 7|5@0+ (1,0) [0|31] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2VertAccV : 34|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2VertAcc : 33|10@0- (0.0625,0) [-32|31.9375] "m/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2LonAccV : 2|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2LonAcc : 1|10@0- (0.0625,0) [-32|31.9375] "m/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2LatAccV : 18|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ Inertial2LatAcc : 17|10@0- (0.0625,0) [-32|31.9375] "m/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ IntlSnsr2AcelFm1Chksm : 50|11@0+ (1,0) [0|2047] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 304 Inertial1_Accel1_FO: 8 _DOFIMU1_FO
 SG_ MstrTrigSyncInrtl11 : 7|5@0+ (1,0) [0|31] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1VertAccV : 34|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1VertAcc : 33|10@0- (0.0625,0) [-32|31.9375] "m/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1LonAccV : 2|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1LonAcc : 1|10@0- (0.0625,0) [-32|31.9375] "m/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1LatAccV : 18|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InrtlSnsr1LatAcc : 17|10@0- (0.0625,0) [-32|31.9375] "m/s^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ IntlSnsr1AcelFm1Chksm : 50|11@0+ (1,0) [0|2047] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 356 Map_Pos_Correction_FO: 4 EOCM2A_K1_FO
 SG_ LongErrPstn : 7|8@0- (0.5,0) [-64|63.5] "m"  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ LatErrPstn : 15|8@0- (0.5,0) [-64|63.5] "m"  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ PstnErrChcksm : 18|11@0+ (1,0) [0|2047] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ PstnErrARC : 20|2@0+ (1,0) [0|3] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 354 Map_Retrans_Request_FO: 1 EOCM2A_K1_FO
 SG_ HrznPrvdRstRq : 2|1@0+ (1,0) [0|1] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ ADASISRwDtMplxdCycCnt : 7|2@0+ (1,0) [0|3] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ ADASISMsgTypRetrans : 5|3@0+ (1,0) [0|7] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 352 Map_Path_Correction_FO: 6 EOCM2A_K1_FO
 SG_ ExptNxtTrnstnDir : 37|3@0+ (1,0) [0|7] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ MstProbLnV : 15|1@0+ (1,0) [0|1] ""  AMM_FO
 SG_ MstProbLn : 14|5@0+ (1,0) [0|31] ""  AMM_FO
 SG_ PathCrtnChcksm : 34|11@0+ (1,0) [0|2047] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ PathCrtnARC : 4|2@0+ (1,0) [0|3] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ EgoLnCurvV : 2|1@0+ (1,0) [0|1] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ EgoLnCurv : 23|16@0- (9.53E-007,0) [-0.031227904|0.031226951] "1/m^2"  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ VehLnStatV : 1|1@0+ (1,0) [0|1] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ VehLnStatConf : 6|2@0+ (1,0) [0|3] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ VehLnStat : 9|2@0+ (1,0) [0|3] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ ExptNxtTransDirV : 0|1@0+ (1,0) [0|1] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ ExptNxtTrnstnDirConf : 39|2@0+ (1,0) [0|3] ""  AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 264 Inertial_Trigger_FO: 1 EOCM2A_K1_FO
 SG_ InrtlSnsrMstrTrgrSync : 7|5@0+ (1,0) [0|31] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,_DOFIMU2_FO,_DOFIMU1_FO

BO_ 1156 SSID_AMM_2_FO: 8 EOCM2A_K1_FO
 SG_ WiFiSSIDDgts9to16_Mp : 7|64@0+ (1,0) [0|0] ""  EOCM2B_K1_FO,AMM_FO

BO_ 1155 SSID_AMM_1_FO: 8 EOCM2A_K1_FO
 SG_ WiFiSSIDDgts1to8_Mp : 7|64@0+ (1,0) [0|0] ""  EOCM2B_K1_FO,AMM_FO

BO_ 1927 DTC_Triggered_787_FO: 7 DMS_FO
 SG_ DTCI_CodeSupported_787 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_787 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCFailType_787 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_787 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_787 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCSource_787 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_787 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_787 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_787 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_787 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_787 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_787 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_787 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_787 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_787 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_787 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_787 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_787 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_787 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_787 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1926 DTC_Triggered_786_FO: 7 AMM_FO
 SG_ DTCI_CodeSupported_786 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_786 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCFailType_786 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_786 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_786 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCSource_786 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_786 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_786 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_786 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_786 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_786 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_786 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_786 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_786 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_786 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_786 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_786 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_786 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_786 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_786 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1928 DTC_Triggered_788_FO: 7 _DOFIMU1_FO
 SG_ DTCI_CodeSupported_788 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_788 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCFailType_788 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_788 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_788 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCSource_788 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_788 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_788 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_788 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_788 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_788 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_788 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_788 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_788 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_788 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_788 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_788 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_788 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_788 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_788 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1929 DTC_Triggered_789_FO: 7 _DOFIMU2_FO
 SG_ DTCI_CodeSupported_789 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_789 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCFailType_789 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_789 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_789 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCSource_789 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_789 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_789 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_789 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_789 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_789 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_789 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_789 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_789 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_789 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_789 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_789 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_789 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_789 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_789 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1352 UUDT_Resp_From_6DOFIMU2_FO: 8 _DOFIMU2_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 588 USDT_Req_to_EOCM2B_K1_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  EOCM2B_K1_FO

BO_ 1356 UUDT_Resp_From_EOCM2B_K1_FO: 8 EOCM2B_K1_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1612 USDT_Resp_From_EOCM2B_K1_FO: 8 EOCM2B_K1_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1616 USDT_Resp_From_EOCM2A_IMX6_FO: 8 EOCM2A_IMX6_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1360 UUDT_Resp_From_EOCM2A_IMX6_FO: 8 EOCM2A_IMX6_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 592 USDT_Req_to_EOCM2A_IMX6_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  EOCM2A_IMX6_FO

BO_ 1610 USDT_Resp_From_EOCM2A_K1_FO: 8 EOCM2A_K1_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1354 UUDT_Resp_From_EOCM2A_K1_FO: 8 EOCM2A_K1_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 586 USDT_Req_to_EOCM2A_K1_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  EOCM2A_K1_FO

BO_ 1925 DTC_Triggered_785_FO: 7 NVS_FO
 SG_ DTCI_CodeSupported_785 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_785 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCFailType_785 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_785 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_785 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCSource_785 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_785 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_785 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_785 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_785 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_785 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_785 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_785 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_785 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_785 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_785 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_785 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_785 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_785 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_785 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1859 VIN_Digits_10_to_17_FO: 8 EOCM_F_FO
 SG_ VehIdNmDig10_17 : 7|64@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,NVS_FO

BO_ 1857 VIN_Digits_2_to_9_FO: 8 EOCM_F_FO
 SG_ VehIdNmDig2_9 : 7|64@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,NVS_FO

BO_ 771 Outside_Air_Temperature_FO: 2 EOCM_F_FO
 SG_ OtsAirTmpCrValV : 0|1@0+ (1,0) [0|1] ""  NVS_FO
 SG_ OtsAirTmpCrVal : 15|8@0+ (0.5,-40) [-40|87.5] "deg C"  NVS_FO

BO_ 777 Night_Vision_System_Ped_FO: 1 NVS_FO
 SG_ NVSysStat : 3|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ NVSysPedWrnIndReq : 7|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ NVSysPedDetCsCrStVal : 5|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ NVSysPedDetCstStAvl : 0|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 607 USDT_Req_to_NVS_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  NVS_FO

BO_ 1631 USDT_Resp_From_NVS_FO: 8 NVS_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1375 UUDT_Resp_From_NVS_FO: 8 NVS_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1540 ADAS_Position_FO: 8 AMM_FO
 SG_ AdvDrAstMpLatOffst : 40|9@0+ (1,-256) [-256|255] "m"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPstnIndx : 63|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPstnCnf : 61|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPsnIdxCrLn : 58|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPstnPthIndx : 46|6@0+ (1,0) [0|63] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPstnProb : 31|5@0+ (3.333,0) [0|103.323] "%"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPstnCycCnt : 26|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPstnAge : 24|9@0+ (5,0) [0|2555] "ms"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPstnRelHd : 23|8@0+ (1.417,0) [0|361.335] "deg"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPstnMsgTyp : 7|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO
 SG_ AdvDrAstMpPstnOfst : 4|13@0+ (1,0) [0|8191] "m"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO

BO_ 1608 USDT_Resp_From_6DOFIMU2_FO: 8 _DOFIMU2_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 871 LGT_ObjectDetect_Info_8_FO: 8 VIS2_FO
 SG_ GFHBFwVsnWidthTrk8 : 62|7@0+ (0.1,0) [0|12.7] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnVertPosTrk8 : 53|6@0+ (0.25,-3) [-3|12.75] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnRelLaneTrk8 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnAzmthRtTrk8 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ GFHBFwVsnCnfdncTrk8 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnRngTrkRev8 : 16|12@0+ (0.2,0) [0|819] "m"  EOCM_F_FO
 SG_ GFHBFwVsnAzmthTrkRev8 : 10|10@0- (0.05,0) [-25.6|25.55] "deg"  EOCM_F_FO
 SG_ GFHBFVsnObjTypTrkRev8 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ GFHBObjDirTrk8 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ GFHBFwVsnMesStatTrk8 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnObjIDTrk8 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 870 LGT_ObjectDetect_Info_7_FO: 8 VIS2_FO
 SG_ GFHBFwVsnWidthTrk7 : 62|7@0+ (0.1,0) [0|12.7] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnVertPosTrk7 : 53|6@0+ (0.25,-3) [-3|12.75] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnRelLaneTrk7 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnAzmthRtTrk7 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ GFHBFwVsnCnfdncTrk7 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnRngTrkRev7 : 16|12@0+ (0.2,0) [0|819] "m"  EOCM_F_FO
 SG_ GFHBFwVsnAzmthTrkRev7 : 10|10@0- (0.05,0) [-25.6|25.55] "deg"  EOCM_F_FO
 SG_ GFHBFVsnObjTypTrkRev7 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ GFHBObjDirTrk7 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ GFHBFwVsnMesStatTrk7 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnObjIDTrk7 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 869 LGT_ObjectDetect_Info_6_FO: 8 VIS2_FO
 SG_ GFHBFwVsnWidthTrk6 : 62|7@0+ (0.1,0) [0|12.7] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnVertPosTrk6 : 53|6@0+ (0.25,-3) [-3|12.75] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnRelLaneTrk6 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnAzmthRtTrk6 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ GFHBFwVsnCnfdncTrk6 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnRngTrkRev6 : 16|12@0+ (0.2,0) [0|819] "m"  EOCM_F_FO
 SG_ GFHBFwVsnAzmthTrkRev6 : 10|10@0- (0.05,0) [-25.6|25.55] "deg"  EOCM_F_FO
 SG_ GFHBFVsnObjTypTrkRev6 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ GFHBObjDirTrk6 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ GFHBFwVsnMesStatTrk6 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnObjIDTrk6 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 868 LGT_ObjectDetect_Info_5_FO: 8 VIS2_FO
 SG_ GFHBFwVsnWidthTrk5 : 62|7@0+ (0.1,0) [0|12.7] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnVertPosTrk5 : 53|6@0+ (0.25,-3) [-3|12.75] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnRelLaneTrk5 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnAzmthRtTrk5 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ GFHBFwVsnCnfdncTrk5 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnRngTrkRev5 : 16|12@0+ (0.2,0) [0|819] "m"  EOCM_F_FO
 SG_ GFHBFwVsnAzmthTrkRev5 : 10|10@0- (0.05,0) [-25.6|25.55] "deg"  EOCM_F_FO
 SG_ GFHBFVsnObjTypTrkRev5 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ GFHBObjDirTrk5 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ GFHBFwVsnMesStatTrk5 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnObjIDTrk5 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 867 LGT_ObjectDetect_Info_4_FO: 8 VIS2_FO
 SG_ GFHBFwVsnWidthTrk4 : 62|7@0+ (0.1,0) [0|12.7] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnVertPosTrk4 : 53|6@0+ (0.25,-3) [-3|12.75] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnRelLaneTrk4 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnAzmthRtTrk4 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ GFHBFwVsnCnfdncTrk4 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnRngTrkRev4 : 16|12@0+ (0.2,0) [0|819] "m"  EOCM_F_FO
 SG_ GFHBFwVsnAzmthTrkRev4 : 10|10@0- (0.05,0) [-25.6|25.55] "deg"  EOCM_F_FO
 SG_ GFHBFVsnObjTypTrkRev4 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ GFHBObjDirTrk4 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ GFHBFwVsnMesStatTrk4 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnObjIDTrk4 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 866 LGT_ObjectDetect_Info_3_FO: 8 VIS2_FO
 SG_ GFHBFwVsnWidthTrk3 : 62|7@0+ (0.1,0) [0|12.7] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnVertPosTrk3 : 53|6@0+ (0.25,-3) [-3|12.75] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnRelLaneTrk3 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnAzmthRtTrk3 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ GFHBFwVsnCnfdncTrk3 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnRngTrkRev3 : 16|12@0+ (0.2,0) [0|819] "m"  EOCM_F_FO
 SG_ GFHBFwVsnAzmthTrkRev3 : 10|10@0- (0.05,0) [-25.6|25.55] "deg"  EOCM_F_FO
 SG_ GFHBFVsnObjTypTrkRev3 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ GFHBObjDirTrk3 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ GFHBFwVsnMesStatTrk3 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnObjIDTrk3 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 865 LGT_ObjectDetect_Info_2_FO: 8 VIS2_FO
 SG_ GFHBFwVsnWidthTrk2 : 62|7@0+ (0.1,0) [0|12.7] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnVertPosTrk2 : 53|6@0+ (0.25,-3) [-3|12.75] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnRelLaneTrk2 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnAzmthRtTrk2 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ GFHBFwVsnCnfdncTrk2 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnRngTrkRev2 : 16|12@0+ (0.2,0) [0|819] "m"  EOCM_F_FO
 SG_ GFHBFwVsnAzmthTrkRev2 : 10|10@0- (0.05,0) [-25.6|25.55] "deg"  EOCM_F_FO
 SG_ GFHBFVsnObjTypTrkRev2 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ GFHBFwVsnMesStatTrk2 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBObjDirTrk2 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ GFHBFwVsnObjIDTrk2 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 864 LGT_ObjectDetect_Info_1_FO: 8 VIS2_FO
 SG_ GFHBFwVsnWidthTrk1 : 62|7@0+ (0.1,0) [0|12.7] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnVertPosTrk1 : 53|6@0+ (0.25,-3) [-3|12.75] "deg"  EOCM_F_FO
 SG_ GFHBFwVsnRelLaneTrk1 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnAzmthRtTrk1 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ GFHBFwVsnCnfdncTrk1 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnRngTrkRev1 : 16|12@0+ (0.2,0) [0|819] "m"  EOCM_F_FO
 SG_ GFHBFwVsnAzmthTrkRev1 : 10|10@0- (0.05,0) [-25.6|25.55] "deg"  EOCM_F_FO
 SG_ GFHBFVsnObjTypTrkRev1 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ GFHBObjDirTrk1 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ GFHBFwVsnMesStatTrk1 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ GFHBFwVsnObjIDTrk1 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1915 DTC_Triggered_77B_FO: 7 VIS_FO
 SG_ DTCI_DTCFaultType_77B : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_CodeSupported_77B : 40|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_77B : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_77B : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_77B : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_77B : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_77B : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_77B : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_77B : 47|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCFailType_77B : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_77B : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCSource_77B : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_77B : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_77B : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_77B : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_77B : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_77B : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_77B : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_77B : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_77B : 7|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1923 DTC_Triggered_783_FO: 7 RSRR_FO
 SG_ DTCI_WrnIndRqdSt_783 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_783 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_783 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_783 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_783 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_783 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_783 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCSource_783 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_783 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_783 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFailType_783 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_783 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CodeSupported_783 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_783 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_783 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_783 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_783 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_783 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_783 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_783 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1924 DTC_Triggered_784_FO: 7 RRSRR_FO
 SG_ DTCI_HistStat_784 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_784 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_784 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_784 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_784 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_784 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_784 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_784 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_784 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_784 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_784 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_784 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_784 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CodeSupported_784 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_784 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCFailType_784 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_784 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_784 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCSource_784 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_784 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1922 DTC_Triggered_782_FO: 7 RFSRR_FO
 SG_ DTCI_WrnIndRqdSt_782 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_782 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_782 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_782 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_782 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_782 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_782 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_782 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_782 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_782 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_782 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_782 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_782 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_782 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCSource_782 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_782 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_782 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFailType_782 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_CurrentStatus_782 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_CodeSupported_782 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1920 DTC_Triggered_780_FO: 7 LRR_FO
 SG_ DTCI_CurrentStatus_780 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_780 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_780 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_780 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_780 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_780 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_780 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_780 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_780 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_780 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_780 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_780 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_780 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_780 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_780 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCSource_780 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_780 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_780 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFailType_780 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_CodeSupported_780 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1921 DTC_Triggered_781_FO: 7 LFSRR_FO
 SG_ DTCI_CurrentStatus_781 : 41|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_WrnIndRqdSt_781 : 47|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused7_781 : 1|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused6_781 : 2|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused5_781 : 3|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused4_781 : 4|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused3_781 : 5|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused2_781 : 6|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCIUnused1_781 : 7|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdPwrUpSt_781 : 45|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstNPsdCdClrdSt_781 : 42|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldPwrUpSt_781 : 46|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_TstFldCdClrdStat_781 : 43|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_HistStat_781 : 44|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCTriggered_781 : 0|1@0+ (1,0) [0|1] ""  TestTool_FO
 SG_ DTCI_DTCSource_781 : 15|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCNumber_781 : 23|16@0+ (1,0) [0|65535] ""  TestTool_FO
 SG_ DTCI_DTCFaultType_781 : 55|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_DTCFailType_781 : 39|8@0+ (1,0) [0|255] ""  TestTool_FO
 SG_ DTCI_CodeSupported_781 : 40|1@0+ (1,0) [0|1] ""  TestTool_FO

BO_ 1034 Curvature_Right_Line_FO: 7 VIS2_FO
 SG_ CRL_Cnfdnc : 50|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ CRL_ViewRng : 33|15@0+ (0.0039064,0) [0|128.0010088] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ CRL_CoefdA : 17|16@0- (3.6622E-009,0) [-0.0001200029696|0.0001199993074] "1/m^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ CRL_CoefA : 1|16@0- (6.1036E-007,0) [-0.02000027648|0.01999966612] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 1033 Curvature_Left_Line_FO: 7 VIS2_FO
 SG_ CLL_Cnfdnc : 50|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ CLL_ViewRng : 33|15@0+ (0.0039064,0) [0|128.0010088] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ CLL_CoefdA : 17|16@0- (3.6622E-009,0) [-0.0001200029696|0.0001199993074] "1/m^2"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ CLL_CoefA : 1|16@0- (6.1036E-007,0) [-0.02000027648|0.01999966612] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO

BO_ 1365 UUDT_Resp_From_VIS2_FO: 8 VIS2_FO
 SG_ DgnInf_OBJ555 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1621 USDT_Resp_From_VIS2_FO: 8 VIS2_FO
 SG_ DgnInf_OBJ655 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1094 F_Vision_Obj_Track_12: 8 VIS2_FO
 SG_ FwdVsnObjTypTr12Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnAzmthTrk12Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk12Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FVisionWidthTrk12 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FVisionMeasStatTrk12 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnVertPosTrk12 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FVisionRelLaneTrk12 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk12 : 34|11@0- (0.125,0) [-128|127.875] "deg/sec"  EOCM_F_FO
 SG_ FVisionConfTrk12 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ ObjDirTrk12 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk12 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk12 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1093 F_Vision_Obj_Track_11: 8 VIS2_FO
 SG_ FwdVsnObjTypTr11Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnAzmthTrk11Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk11Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FVisionWidthTrk11 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FVisionMeasStatTrk11 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnVertPosTrk11 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FVisionRelLaneTrk11 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk11 : 34|11@0- (0.125,0) [-128|127.875] "deg/sec"  EOCM_F_FO
 SG_ FVisionConfTrk11 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ ObjDirTrk11 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk11 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk11 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1100 F_Vision_Obj_Track_12_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT12Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk12 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk12 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk12 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk12 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr12 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk12 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo12 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1099 F_Vision_Obj_Track_11_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT11Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk11 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk11 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk11 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk11 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr11 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk11 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo11 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1098 F_Vision_Obj_Track_10_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT10Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk10 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk10 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk10 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk10 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr10 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk10 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo10 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1097 F_Vision_Obj_Track_9_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT9Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk9 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk9 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk9 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk9 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr9 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk9 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo9 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1096 F_Vision_Obj_Track_8_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT8Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk8 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk8 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk8 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk8 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr8 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk8 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo8 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1095 F_Vision_Obj_Track_7_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT7Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk7 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk7 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk7 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk7 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr7 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk7 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo7 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 863 F_Vision_GFHB_Data_FO: 8 VIS2_FO
 SG_ RgDtLgtSrcHrtAngl : 49|10@0- (0.04,0) [-20.48|20.44] "deg"  EOCM_F_FO
 SG_ RgDtLgtSrcHrtAngVcty : 55|6@0- (1,0) [-32|31] "deg/sec"  EOCM_F_FO
 SG_ LfDtLgtSrcHrtAngl : 33|10@0- (0.04,0) [-20.48|20.44] "deg"  EOCM_F_FO
 SG_ LfDtLgtSrcHrtAnVcty : 39|6@0- (1,0) [-32|31] "deg/sec"  EOCM_F_FO
 SG_ AdvWthrStat : 25|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ DtctdLghtSrcDstnc : 22|7@0+ (10,0) [0|1270] "m"  EOCM_F_FO
 SG_ DtctdLghtSrcVrtclAngl : 1|10@0- (0.04,0) [-20.48|20.44] "deg"  EOCM_F_FO
 SG_ IntLghtRngAct : 2|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ DtctdLghtSrcDstncV : 3|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ TwnDtctnSts : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ DtctdLghtSrcDrvngDrctn : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 862 LGT_ControlHighBeamGlare_FO: 2 VIS2_FO
 SG_ FwdCamSysOpStat : 10|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ EnvIllum : 2|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RdTyp : 5|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ AutoHgBmSts : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1068 F_Vision_Obj_Track_6_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT6Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk6 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk6 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk6 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk6 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr6 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk6 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo6 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1067 F_Vision_Obj_Track_5_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT5Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk5 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk5 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk5 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk5 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr5 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk5 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo5 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1066 F_Vision_Obj_Track_4_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT4Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk4 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk4 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk4 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk4 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr4 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk4 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo4 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1065 F_Vision_Obj_Track_3_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT3Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk3 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk3 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk3 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk3 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr3 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk3 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo3 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1064 F_Vision_Obj_Track_2_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT2Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk2 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk2 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk2 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk2 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr2 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk2 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo2 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1063 F_Vision_Obj_Track_1_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT1Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk1 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk1 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk1 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk1 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk1 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr1 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo1 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1151 Long_Range_Radar_add_Info_5_FO: 8 LRR_FO
 SG_ FrtRdrRdEdgLtLatRdEdgDst : 63|8@0- (0.1,0) [-12.8|12.7] "m/m"  EOCM_F_FO
 SG_ FrtRdrRdEdgLtCrvtPrvDst : 5|4@0+ (10,0) [0|150] ""  EOCM_F_FO
 SG_ FrtRdrRdEdgLtTanHdgAng : 15|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM_F_FO
 SG_ FrtRdrRdEdgLtCrvtV : 48|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtRdrRdEdgLtCrvtGradV : 0|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtRdrRdEdgLtCrvtGrad : 39|16@0- (5.96E-008,0) [-0.0019529728|0.0019529132] "1/(m*sec)"  EOCM_F_FO
 SG_ FrtRdrRdEdgLtCrvtConf : 55|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM_F_FO
 SG_ FrtRdrRdEdgLtCrvt : 23|16@0- (9.53E-007,0) [-0.031227904|0.031226951] "1/m"  EOCM_F_FO
 SG_ FrtRdrRdEdgLtTanHdgAngV : 1|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRAddInfo5BurstID : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1150 Long_Range_Radar_add_Info_4_FO: 8 LRR_FO
 SG_ FrtRdrRdEdgRtLatRdEdgDst : 63|8@0- (0.1,0) [-12.8|12.7] "m/m"  EOCM_F_FO
 SG_ FrtRdrRdEdgRtCrvtPrvDst : 5|4@0+ (10,0) [0|150] ""  EOCM_F_FO
 SG_ FrtRdrRdEdgRtTanHdgAngV : 1|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtRdrRdEdgRtTanHdgAng : 15|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM_F_FO
 SG_ FrtRdrRdEdgRtCrvtV : 48|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtRdrRdEdgRtCrvtGradV : 0|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtRdrRdEdgRtCrvtGrad : 39|16@0- (5.96E-008,0) [-0.0019529728|0.0019529132] "1/(m*sec)"  EOCM_F_FO
 SG_ FrtRdrRdEdgRtCrvtConf : 55|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM_F_FO
 SG_ FrtRdrRdEdgRtCrvt : 23|16@0- (9.53E-007,0) [-0.031227904|0.031226951] "1/m"  EOCM_F_FO
 SG_ FLRRAddInfo4BurstID : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1088 F_Vision_Obj_Header_2: 8 VIS2_FO
 SG_ FrntVsnInPthVehBrkNwSt : 35|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FrntVsnClostPedBrkNwSt : 39|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FrntVsnClostPedObjID : 29|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FrntVsnClostPedAlrtNwFlg : 30|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrntVsnClostPedNotftnFlg : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrntVsnInPthVehAlrtNwFlg : 2|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnVldTgtNum2 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FrtVsnTmStmp2V : 31|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnTmStmp2 : 10|11@0+ (1,0) [0|2047] ""  EOCM_F_FO
 SG_ FrtVsnRollCnt2 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FrtVsnBrstChksum2 : 55|16@0+ (1,0) [0|65535] ""  EOCM_F_FO

BO_ 854 F_Vision_Environment_7: 3 VIS2_FO
 SG_ FwdVsnCnstrctAreaDst : 13|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnCnstrctZnDet : 15|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnEgoVehLnPos : 17|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnRdTypDet : 9|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnTunnlDetd : 23|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnTunnlDst : 21|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBrstID5 : 1|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 853 F_Vision_Environment_6: 8 VIS2_FO
 SG_ LnMrkg4LnSnsLnHdngTngtV : 7|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnHdngTngt : 23|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnDstV : 56|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnDst : 15|8@0- (0.1,0) [-12.8|12.7] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnCrvtV : 6|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnCrvtGradV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnCrvtGrad : 47|16@0- (5.96E-008,0) [-0.0019529728|0.0019529132] "1/(m*sec)"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnCrvt : 31|16@0- (9.53E-007,0) [-0.031227904|0.031226951] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnQltyConfLvl : 63|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnMrkrTyp : 4|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBrstID4 : 1|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 852 F_Vision_Environment_5: 8 VIS2_FO
 SG_ LnMrkg3LnSnsLnHdngTngtV : 7|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnHdngTngt : 23|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnDstV : 56|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnDst : 15|8@0- (0.1,0) [-12.8|12.7] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnCrvtV : 6|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnCrvtGradV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnCrvtGrad : 47|16@0- (5.96E-008,0) [-0.0019529728|0.0019529132] "1/(m*sec)"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnCrvt : 31|16@0- (9.53E-007,0) [-0.031227904|0.031226951] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnQltyConfLvl : 63|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnMrkrTyp : 4|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBrstID3 : 1|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 602 USDT_Req_to_RRSRR: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  RRSRR_FO

BO_ 1626 USDT_Resp_From_RRSRR: 8 RRSRR_FO
 SG_ DgnInf_OBJ65A : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1370 UUDT_Resp_From_RRSRR: 8 RRSRR_FO
 SG_ DgnInf_OBJ55A : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1536 RR_SRR_Trace_data: 8 RRSRR_FO
 SG_ RRSRRYear : 7|8@0+ (1,0) [0|0] ""  EOCM_F_FO
 SG_ RRSRRSerialNmbr : 39|32@0+ (1,0) [0|0] ""  EOCM_F_FO
 SG_ RRSRRJulianDate : 15|24@0+ (1,0) [0|0] ""  EOCM_F_FO

BO_ 1210 RR_SRR_Object_Track10: 8 RRSRR_FO
 SG_ RRSrrTrkRawAzimuth10 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate10 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange10 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange10 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID10 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation10 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus10 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp10 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth10 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RRSrrBurstID10 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1209 RR_SRR_Object_Track9: 8 RRSRR_FO
 SG_ RRSrrTrkRawAzimuth9 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate9 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange9 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange9 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID9 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation9 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus9 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp9 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth9 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RRSrrBurstID9 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1208 RR_SRR_Object_Track8: 8 RRSRR_FO
 SG_ RRSrrTrkRawAzimuth8 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate8 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange8 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange8 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID8 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation8 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus8 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp8 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth8 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RRSrrBurstID8 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1207 RR_SRR_Object_Track7: 8 RRSRR_FO
 SG_ RRSrrTrkRawAzimuth7 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate7 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange7 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange7 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID7 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation7 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus7 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp7 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth7 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RRSrrBurstID7 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1206 RR_SRR_Object_Track6: 8 RRSRR_FO
 SG_ RRSrrTrkRawAzimuth6 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate6 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange6 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange6 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID6 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation6 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus6 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp6 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth6 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RRSrrBurstID6 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1205 RR_SRR_Object_Track5: 8 RRSRR_FO
 SG_ RRSrrTrkRawAzimuth5 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate5 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange5 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange5 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID5 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation5 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus5 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp5 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth5 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RRSrrBurstID5 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 262 LHT_AutoHighBeamAssistStatus_FO: 5 EOCM_F_FO
 SG_ NtVsnSysEnbld : 6|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,NVS_FO
 SG_ VehMovState : 5|3@0+ (1,0) [0|7] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,NVS_FO
 SG_ NVSysPedDetCstReq : 2|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,NVS_FO
 SG_ StrWhAngV : 8|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,NVS_FO,VIS_FO
 SG_ StrWhAngMsk : 9|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ StrWhAng : 23|16@0- (0.0625,0) [-2048|2047.9375] "deg"  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,NVS_FO,VIS_FO
 SG_ RtTrnLmpAtv : 13|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ MpDataAvlbl : 0|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ LftTrnLmpAtv : 11|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ HdlmpBmSelectStat : 33|2@0+ (1,0) [0|3] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ BldUpArDet : 14|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ FrFogLmpsAct : 15|1@0+ (1,0) [0|1] ""  EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,NVS_FO,VIS_FO

BO_ 309 LHT_CameraObjConfirmation_FO: 1 VIS_FO
 SG_ HiBmRecmnd : 1|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ CtLghtDet : 0|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 2034 CCP_Data_Transmission_Object_FO: 8 VIS2_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 2032 CCP_Command_Receive_Object_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  VIS2_FO

BO_ 1362 F_Vis_Obj_Conf_CPS_1B: 8 EOCM_F_FO
 SG_ FObjConfCPSTrkObjectIDB : 7|6@0+ (1,0) [0|63] ""  VIS_FO
 SG_ CPSVisConfLonPos1 : 20|12@0- (0.125,0) [-256|255.875] "m"  VIS_FO
 SG_ CPSVisConfLatPos1 : 15|10@0- (0.125,0) [-64|63.875] "m"  VIS_FO
 SG_ CPSVisConfChecksum : 50|11@0+ (1,0) [0|2047] ""  VIS_FO
 SG_ FObjConfCPSTrkRangeRate : 45|11@0- (0.125,0) [-128|127.875] "m/s"  VIS_FO
 SG_ CPSConfTimeStamp : 24|11@0+ (1,0) [0|2047] "ms"  VIS_FO
 SG_ CPSConfTimeStampV : 21|1@0+ (1,0) [0|1] ""  VIS_FO
 SG_ FObjConfCPSRollingTrkCnt : 1|2@0+ (1,0) [0|3] ""  VIS_FO

BO_ 1413 TOS_ACC_IDS: 5 EOCM_F_FO
 SG_ TOS_ACC_IDSRollCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ TOS_ACC_IDSFuncState : 5|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ TOS_ACC_ID1 : 3|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ TOS_ACC_ID2 : 13|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ TOS_ACC_ID3 : 23|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ TOS_ACC_ID4 : 17|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ TOS_ACC_ID5 : 27|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ TOS_ACC_ID6 : 37|6@0+ (1,0) [0|63] ""  Dummy_FO

BO_ 1412 F_ACC_Target: 8 EOCM_F_FO
 SG_ FACCTOSRollCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FACCTOSFuncState : 5|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FACCTOSLongPos : 3|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FACCTOSLatPos : 23|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FACCTOSMeasStat : 28|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FACCTOSLongVel : 26|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FACCTOSTrgtDecelFlg : 47|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FACCTOSDynProp : 46|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FACCTOSLatVel : 42|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FACCTOSRelLane : 63|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FACCTOSHiThrtID : 61|6@0+ (1,0) [0|63] ""  Dummy_FO

BO_ 1409 F_CPS_TOS_B: 8 EOCM_F_FO
 SG_ CPSTOSObjType : 7|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ CPSTOSHiThrtPriNo : 60|5@0+ (1,0) [0|31] ""  Dummy_FO
 SG_ CPSTOSRelLongAcc : 53|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ CPSTOSConfAsmt : 21|12@0+ (1,0) [0|4095] ""  Dummy_FO
 SG_ CPSTOSNumCycTrkd : 4|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ CPSTOSTimeToColl : 1|12@0+ (0.025,0) [0|102.375] "s"  Dummy_FO
 SG_ CPSTOSClosestInPthVehRng : 47|10@0+ (0.25,0) [0|255.75] "m"  Dummy_FO
 SG_ CPSTOSClosestInPthObID : 37|6@0+ (1,0) [0|63] "m"  Dummy_FO
 SG_ CPSTOSMeasStat : 39|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ CPSTOSFuncState : 25|2@0+ (1,0) [0|3] ""  Dummy_FO

BO_ 1408 F_CPS_TOS_A: 8 EOCM_F_FO
 SG_ CPSTOSLongPos : 7|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ CPSTOSLatPos : 11|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ CPSTOSLongVel : 16|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ CPSTOSDynProp : 37|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ CPSTOSLatVel : 34|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ CPSTOSARelLane : 63|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ CPSTOSHiThrtID : 61|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ CPSTOSHighestThreatObAsmt : 55|8@0+ (1,0) [0|255] ""  Dummy_FO

BO_ 1344 FLPEstimate: 8 EOCM_F_FO
 SG_ FLPRollCount : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ NewLaneIndex : 5|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ VehPathInOK : 3|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LaneSnsInOK : 2|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ MapInOK : 1|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FWDObjFusInOK : 0|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LngthFrstSeg : 15|4@0+ (10,0) [0|150] "m"  Dummy_FO
 SG_ LngthScndSeg : 11|4@0+ (10,0) [0|150] "m"  Dummy_FO
 SG_ CurvFrstSeg : 23|13@0- (5E-005,0) [-0.2048|0.20475] "1/m"  Dummy_FO
 SG_ CurvScndSeg : 26|3@0- (0.001,0) [-0.004|0.003] "1/m"  Dummy_FO
 SG_ OffstLaneCntr : 39|8@0- (0.05,0) [-6.4|6.35] "m"  Dummy_FO
 SG_ TngntLaneHead : 47|8@0- (0.002,0) [-0.256|0.254] "m/m"  Dummy_FO
 SG_ LaneWidth : 55|8@0+ (0.05,0) [0|12.75] "m"  Dummy_FO
 SG_ FLPDataTimeStampV : 63|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FLPDataTimeStamp : 62|7@0+ (16,0) [0|2032] "ms"  Dummy_FO

BO_ 770 F_Fwd_Collision_Alert: 8 EOCM_F_FO
 SG_ Vpath_Accel : 51|11@0- (0.125,0) [-128|127.875] "m/s^2"  NVS_FO,Dummy_FO
 SG_ FCA_Ra : 7|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FCA_Range : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FCA_AlertLevel : 44|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FCA_Alert_Sup : 31|16@0+ (1,0) [0|65535] ""  Dummy_FO
 SG_ FCAStatus : 46|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FCA_VehAhead : 47|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FCA_CPS_Alert : 42|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FCAChime : 41|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FCADiagOK : 40|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ NBSMS_Alert : 55|1@0+ (1,0) [0|1] ""  Dummy_FO

BO_ 1601 USDT_Resp_From_VIS: 8 VIS_FO
 SG_ DgnInf_OBJ641 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1606 USDT_Resp_From_RFSRR: 8 RFSRR_FO
 SG_ DgnInf_OBJ646 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1603 USDT_Resp_From_LFSRR: 8 LFSRR_FO
 SG_ DgnInf_OBJ643 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1602 USDT_Resp_From_LRR: 8 LRR_FO
 SG_ DgnInf_OBJ642 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1625 USDT_Resp_From_RSRR: 8 RSRR_FO
 SG_ DgnInf_OBJ644 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1611 USDT_Resp_From_FEOCM_FO: 8 EOCM_F_FO
 SG_ DgnInf_OBJ64B : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 768 F_Smgr_Vehicle_Motion: 8 EOCM_F_FO
 SG_ SmgrMotRollAngle : 44|10@0- (0.1,0) [-51.2|51.1] "deg"  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ SmgrMotRollAngleV : 0|1@0+ (1,0) [0|1] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ SmgrMotChecksum : 50|11@0+ (1,0) [0|2047] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ SmgrMotPitchAngle : 39|11@0- (0.1,0) [-102.4|102.3] "deg"  CIPM_FO,NVS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ SmgrMotPitchAngleV : 1|1@0+ (1,0) [0|1] ""  CIPM_FO,NVS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ SmgrMotLongSpeedV : 2|1@0+ (1,0) [0|1] " "  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ SmgrMotLongSpeed : 18|11@0- (0.1,0) [-102.4|102.3] "m/s"  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ SmgrMotYawRate : 14|12@0- (0.05,0) [-102.4|102.35] "deg/s"  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ SmgrMotYawRateV : 15|1@0+ (1,0) [0|1] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO
 SG_ SmgrMotRollingCnt : 4|2@0+ (1,0) [0|3] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS_FO

BO_ 1350 UUDT_Resp_From_RFSRR: 8 RFSRR_FO
 SG_ DgnInf_OBJ546 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1369 UUDT_Resp_From_RSRR: 8 RSRR_FO
 SG_ DgnInf_OBJ544 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1347 UUDT_Resp_From_LFSRR: 8 LFSRR_FO
 SG_ DgnInf_OBJ543 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1346 UUDT_Resp_From_LRR: 8 LRR_FO
 SG_ DgnInf_OBJ542 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1345 UUDT_Resp_From_VIS: 8 VIS_FO
 SG_ DgnInf_OBJ541 : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 579 USDT_Req_to_LFSRR: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  LFSRR_FO

BO_ 577 USDT_Req_to_VIS: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  VIS_FO

BO_ 578 USDT_Req_to_LRR: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  LRR_FO

BO_ 582 USDT_Req_to_RFSRR: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  RFSRR_FO

BO_ 601 USDT_Req_to_RSRR: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  RSRR_FO

BO_ 1355 UUDT_Resp_From_FEOCM_FO: 8 EOCM_F_FO
 SG_ DgnInf_OBJ54B : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 587 USDT_Req_to_FEOCM_obj: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  EOCM2A_K2_FO,EOCM_F_FO

BO_ 784 Body_Info_FOB: 6 EOCM_F_FO
 SG_ StrgColUpDwnPos : 39|8@0+ (1,0) [0|255] ""  DMS_FO
 SG_ CPMAPINFO4 : 47|1@0+ (1,0) [0|1] ""  DMS_FO
 SG_ StrgColInOutPos : 31|8@0+ (1,0) [0|255] ""  DMS_FO
 SG_ StrgColCommsFlt : 19|2@0+ (1,0) [0|3] ""  DMS_FO
 SG_ DrDoorOpenSwActV : 16|1@0+ (1,0) [0|1] ""  DMS_FO
 SG_ DrDoorOpenSwAct : 17|1@0+ (1,0) [0|1] ""  DMS_FO
 SG_ DrvWndPosStat : 22|3@0+ (1,0) [0|7] ""  CIPM_FO,DMS_FO,VIS2_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ InterLghtStat : 23|1@0+ (1,0) [0|1] ""  CIPM_FO,DMS_FO,VIS2_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ DrvrHndsOnWhlZn3 : 13|1@0+ (1,0) [0|1] ""  CIPM_FO,AMM_FO,DMS_FO,VIS2_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ DrvrHndsOnWhlZn2 : 14|1@0+ (1,0) [0|1] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,VIS2_FO
 SG_ DrvrHndsOnWhlZn1 : 15|1@0+ (1,0) [0|1] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,VIS2_FO
 SG_ WSWshSwAtv : 11|1@0+ (1,0) [0|1] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,AMM_FO,VIS_FO,VIS2_FO
 SG_ SysPwrMdV : 8|1@0+ (1,0) [0|1] ""  NVS_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO,LRSRR_FO,CIPM_FO,_DOFIMU2_FO,_DOFIMU1_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,VIS2_FO
 SG_ SysPwrMd : 10|2@0+ (1,0) [0|3] ""  NVS_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO,LRSRR_FO,CIPM_FO,_DOFIMU2_FO,_DOFIMU1_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,VIS2_FO
 SG_ WSWprAct : 2|1@0+ (1,0) [0|1] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,AMM_FO,NVS_FO,VIS_FO,VIS2_FO
 SG_ RtLwBmFld : 4|1@0+ (1,0) [0|1] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,AMM_FO,NVS_FO,VIS_FO,VIS2_FO
 SG_ OtsdAmbtLtLvlStatV : 5|1@0+ (1,0) [0|1] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,AMM_FO,NVS_FO,VIS_FO,VIS2_FO
 SG_ OtsdAmbtLtLvlStat : 7|2@0+ (1,0) [0|3] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,AMM_FO,NVS_FO,VIS_FO,VIS2_FO
 SG_ LowBmAct : 1|1@0+ (1,0) [0|1] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,AMM_FO,NVS_FO,VIS_FO,VIS2_FO
 SG_ LftLwBmFld : 3|1@0+ (1,0) [0|1] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,AMM_FO,NVS_FO,VIS_FO,VIS2_FO
 SG_ HighBmAct : 0|1@0+ (1,0) [0|1] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,DMS_FO,AMM_FO,NVS_FO,VIS_FO,VIS2_FO
 SG_ TrStLgMdAtv : 12|1@0+ (1,0) [0|1] ""  CIPM_FO,NVS_FO,RRSRR_FO,LRSRR_FO,_DOFIMU2_FO,_DOFIMU1_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,LRR_FO,RFSRR_FO,LFSRR_FO,RSRR_FO,VIS_FO,VIS2_FO

BO_ 1539 RF_SRR_Trace_Data: 8 RFSRR_FO
 SG_ RFSRRYear : 7|8@0+ (1,0) [0|0] ""  EOCM_F_FO
 SG_ RFSRRSerialNmbr : 39|32@0+ (1,0) [0|0] ""  EOCM_F_FO
 SG_ RFSRRJulianDate : 15|24@0+ (1,0) [0|0] ""  EOCM_F_FO

BO_ 776 F_Vehicle_Path_Data_2: 7 EOCM_F_FO
 SG_ Vpath_Data2ModeInfo : 44|2@0+ (1,0) [0|3] ""  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath2_Checksum : 42|11@0+ (1,0) [0|2047] ""  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RFSRR_FO,LFSRR_FO,RSRR_FO,LRR_FO
 SG_ Vpath_Data2RollCnt : 46|2@0+ (1,0) [0|3] ""  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_Data2YawRateV : 2|1@0+ (1,0) [0|1] ""  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,NVS_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_Data2LongVelV : 1|1@0+ (1,0) [0|1] ""  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,NVS_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_Data2LatVelV : 47|1@0+ (1,0) [0|1] ""  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_Data2TravlDirctn : 4|2@0+ (1,0) [0|3] ""  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_Data2LongVel : 15|12@0- (0.0625,0) [-128|127.9375] "m/s"  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,NVS_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_Data2YawRate : 19|12@0- (0.0625,0) [-128|127.9375] "deg/s"  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,NVS_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_Data2LatVel : 39|8@0- (0.05,0) [-6.4|6.35] "m/s"  CIPM_FO,DMS_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO

BO_ 161 F_Master_Time_Sync: 7 EOCM_F_FO
 SG_ FTimeSyncMstrChksm : 35|12@0+ (1,0) [0|4095] ""  AMM_FO,CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ FTimeSyncMstrClock : 7|32@0+ (1,0) [0|4294967295] "ms"  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ SensorModeCmdLRR : 39|3@0+ (1,0) [0|7] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ SensorModeCmdSRR : 50|3@0+ (1,0) [0|7] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ SensorModeCmdFCamera : 53|3@0+ (1,0) [0|7] ""  CIPM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ FTimeSyncMstrClockV : 36|1@0+ (1,0) [0|1] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,LRR_FO,RFSRR_FO,LFSRR_FO

BO_ 774 F_Vehicle_Path_Estimate: 8 EOCM_F_FO
 SG_ Vpath_RollingCount : 7|2@0+ (1,0) [0|3] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_TrnCtrLngOfstV : 1|1@0+ (1,0) [0|1] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,LRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LFSRR_FO
 SG_ Vpath_Checksum : 50|11@0+ (1,0) [0|2047] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_TrnCtrLngOfst : 15|8@0- (0.1,0) [-12.8|12.7] "m"  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,LRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LFSRR_FO
 SG_ Vpath_TrnCtrLatOfst : 21|14@0- (1,0) [-8192|8191] "m"  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,LRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LFSRR_FO
 SG_ Vpath_Curvature : 39|16@0- (1E-005,0) [-0.32768|0.32767] "1/m"  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,RSRR_FO,RFSRR_FO,LRR_FO,LFSRR_FO
 SG_ Vpath_CurvatureV : 0|1@0+ (1,0) [0|1] ""  CIPM_FO,AMM_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,VIS2_FO,RRSRR_FO,VIS_FO,LRR_FO,RSRR_FO,RFSRR_FO,LFSRR_FO

BO_ 848 F_Vision_Environment: 8 VIS_FO
 SG_ FwdVsnEnvIllum : 37|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTngtOfHdngLnRtV : 1|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTngtOfHdngLnRt : 31|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnChngStatus : 39|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBurstChecksum : 55|16@0+ (1,0) [0|65535] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseRollingCount : 7|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseSystemOK : 4|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSnsLLnPosValid : 2|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSenseDistToLLnEdge : 14|7@0+ (0.05,0) [0|6.35] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsRLnPosValid : 0|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsDistToRLnEdge : 22|7@0+ (0.05,0) [0|6.35] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseTimeStampV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseTimeStamp : 34|11@0+ (1,0) [0|2047] "ms"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseSystemOKV : 3|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 849 F_Vision_Environment_2: 8 VIS_FO
 SG_ LnSnsLatVRelToRgtMrkg : 23|8@0- (0.02,0) [-2.56|2.54] "m/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ LnSnsRtLnMrkgTypChgDst : 61|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsCrvtGrdntRtV : 63|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnMrkgWdthRt : 62|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsRtAnchrLn : 57|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLtAnchrLn : 56|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnCrvtrRghtV : 0|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnCrvtrRght : 47|16@0- (9.53E-007,0) [-0.031227904|0.031226951] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsCrvtGrdntRt : 31|16@0- (5.96E-008,0) [-0.0019529728|0.0019529132] "1/rad/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBurstID : 2|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLatVRelToLftMrkg : 15|8@0- (0.02,0) [-2.56|2.54] "m/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 1184 R_SRR_Object_Header: 8 RSRR_FO
 SG_ RSRRNumValidTargets : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ RSrrRollingCnt : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSRRModeCmdFdbk : 4|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTimeStampV : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSrrTimeStamp : 10|11@0+ (1,0) [0|2047] "ms"  EOCM_F_FO
 SG_ RSrrBurstChecksum : 55|16@0+ (1,0) [0|65535] ""  EOCM_F_FO
 SG_ RSRRSnstvFltPrsntInt : 24|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRPlntAlgnInProc : 37|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRMsalgnYawRt : 47|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRMsalgnYawLt : 46|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRMsalgnRllRt : 35|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRMsalgnRllLt : 34|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRMsalgnPtchUp : 32|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRMsalgnPtchDn : 33|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRInitDiagCmplt : 40|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRHWFltPrsntInt : 25|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRExtIntrfrnc : 36|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRDiagSpare : 30|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRCANSgnlSpvFld : 29|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRCANRxErr : 28|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRCANIDAddrsUnsbl : 27|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRAntTngFltPrsnt : 26|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRAmbTmpOutRngLw : 42|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRAmbTmpOutRngHi : 41|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRAlgnFltPrsnt : 39|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRVltgOutRngLo : 44|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRVltgOutRngHi : 43|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRSvcAlgnInPrcs : 38|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RSRRSnsrBlckd : 45|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 1185 R_SRR_Object_Track1: 8 RSRR_FO
 SG_ RSrrBurstID1 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange1 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate1 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth1 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID1 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation1 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp1 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus1 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange1 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth1 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1186 R_SRR_Object_Track2: 8 RSRR_FO
 SG_ RSrrBurstID2 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange2 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate2 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth2 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID2 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation2 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp2 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus2 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange2 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth2 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1187 R_SRR_Object_Track3: 8 RSRR_FO
 SG_ RSrrBurstID3 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange3 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate3 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth3 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID3 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation3 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp3 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus3 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange3 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth3 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1188 R_SRR_Object_Track4: 8 RSRR_FO
 SG_ RSrrBurstID4 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange4 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate4 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth4 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID4 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation4 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp4 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus4 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange4 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth4 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1189 R_SRR_Object_Track5: 8 RSRR_FO
 SG_ RSrrBurstID5 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange5 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate5 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth5 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID5 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation5 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp5 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus5 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange5 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth5 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1190 R_SRR_Object_Track6: 8 RSRR_FO
 SG_ RSrrBurstID6 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange6 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate6 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth6 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID6 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation6 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp6 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus6 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange6 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth6 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1191 R_SRR_Object_Track7: 8 RSRR_FO
 SG_ RSrrBurstID7 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange7 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate7 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth7 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID7 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation7 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp7 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus7 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange7 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth7 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1192 R_SRR_Object_Track8: 8 RSRR_FO
 SG_ RSrrBurstID8 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange8 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate8 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth8 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID8 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation8 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp8 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus8 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange8 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth8 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1193 R_SRR_Object_Track9: 8 RSRR_FO
 SG_ RSrrBurstID9 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange9 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate9 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth9 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID9 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation9 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp9 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus9 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange9 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth9 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1194 R_SRR_Object_Track10: 8 RSRR_FO
 SG_ RSrrBurstID10 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkRange10 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RSrrTrkRRate10 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RSrrTrkAzimuth10 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RSrrTrkObjID10 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RSrrTrkObjElevation10 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkDynamProp10 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RSrrTrkMeasStatus10 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RSrrTrkObsRange10 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RSrrTrkRawAzimuth10 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1410 RVB_TVR_Debug: 6 EOCM_F_FO
 SG_ VBBrkRqActv : 7|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ PATOSTTC : 37|12@0+ (0.025,0) [0|102.375] "s"  Dummy_FO
 SG_ BWTOSObjID : 27|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ BWTOSLonPstn : 23|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ VBSwInd : 10|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ VBBrkCtrlSt : 15|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ BrkPlsRqst : 6|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VBOpSt : 12|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ VBAccelOvrrd : 0|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VBUnavail : 1|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VBFld : 2|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VBDisbld : 3|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VBEnbl : 4|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VBBrkPrfReq : 5|1@0+ (1,0) [0|1] ""  Dummy_FO

BO_ 1216 LF_SRR_Object_Header: 8 LFSRR_FO
 SG_ LFSRRNumValidTargets : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ LFSrrRollingCnt : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSRRModeCmdFdbk : 4|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTimeStampV : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSrrTimeStamp : 10|11@0+ (1,0) [0|2047] "ms"  EOCM_F_FO
 SG_ LFSrrBurstChecksum : 55|16@0+ (1,0) [0|65535] ""  EOCM_F_FO
 SG_ LFSRRVltgOutRngLo : 44|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRVltgOutRngHi : 43|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRSvcAlgnInPrcs : 38|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRSnsrBlckd : 45|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRSnstvFltPrsntInt : 24|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRPlntAlgnInProc : 37|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRMsalgnYawRt : 47|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRMsalgnYawLt : 46|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRMsalgnRllRt : 35|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRMsalgnRllLt : 34|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRMsalgnPtchUp : 32|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRMsalgnPtchDn : 33|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRInitDiagCmplt : 40|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRHWFltPrsntInt : 25|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRExtIntrfrnc : 36|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRDiagSpare : 30|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRCANSgnlSpvFld : 29|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRCANRxErr : 28|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRCANIDAddrsUnsbl : 27|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRAntTngFltPrsnt : 26|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRAmbTmpOutRngLw : 42|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRAmbTmpOutRngHi : 41|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ LFSRRAlgnFltPrsnt : 39|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 1217 LF_SRR_Object_Track1: 8 LFSRR_FO
 SG_ LFSrrBurstID1 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange1 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate1 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth1 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID1 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation1 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp1 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus1 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkObsRange1 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth1 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1218 LF_SRR_Object_Track2: 8 LFSRR_FO
 SG_ LFSrrBurstID2 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange2 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate2 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth2 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID2 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation2 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp2 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus2 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkObsRange2 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth2 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1219 LF_SRR_Object_Track3: 8 LFSRR_FO
 SG_ LFSrrBurstID3 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange3 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate3 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth3 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID3 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation3 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp3 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus3 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkObsRange3 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth3 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1220 LF_SRR_Object_Track4: 8 LFSRR_FO
 SG_ LFSrrBurstID4 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange4 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate4 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkObsRange4 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth4 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID4 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation4 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp4 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus4 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth4 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1221 LF_SRR_Object_Track5: 8 LFSRR_FO
 SG_ LFSrrTrkObsRange5 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrBurstID5 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange5 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate5 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth5 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID5 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation5 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp5 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus5 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth5 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1222 LF_SRR_Object_Track6: 8 LFSRR_FO
 SG_ LFSrrTrkObsRange6 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrBurstID6 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange6 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate6 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth6 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID6 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation6 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp6 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus6 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth6 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1223 LF_SRR_Object_Track7: 8 LFSRR_FO
 SG_ LFSrrTrkObsRange7 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrBurstID7 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange7 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate7 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth7 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID7 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation7 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp7 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus7 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth7 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1224 LF_SRR_Object_Track8: 8 LFSRR_FO
 SG_ LFSrrTrkObsRange8 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrBurstID8 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange8 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate8 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth8 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID8 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation8 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp8 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus8 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth8 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1225 LF_SRR_Object_Track9: 8 LFSRR_FO
 SG_ LFSrrTrkObsRange9 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrBurstID9 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange9 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate9 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth9 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID9 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation9 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp9 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus9 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth9 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1226 LF_SRR_Object_Track10: 8 LFSRR_FO
 SG_ LFSrrTrkObsRange10 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ LFSrrBurstID10 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRange10 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ LFSrrTrkRRate10 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ LFSrrTrkAzimuth10 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ LFSrrTrkObjID10 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ LFSrrTrkObjElevation10 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkDynamProp10 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ LFSrrTrkMeasStatus10 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ LFSrrTrkRawAzimuth10 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO

BO_ 1232 RF_SRR_Object_Header: 8 RFSRR_FO
 SG_ RFSRRVltgOutRngLo : 44|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRVltgOutRngHi : 43|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRSvcAlgnInPrcs : 38|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRSnsrBlckd : 45|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRSnstvFltPrsntInt : 24|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRPlntAlgnInProc : 37|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRMsalgnYawRt : 47|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRMsalgnYawLt : 46|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRMsalgnRllRt : 35|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRMsalgnRllLt : 34|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRMsalgnPtchUp : 32|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRMsalgnPtchDn : 33|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRInitDiagCmplt : 40|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRHWFltPrsntInt : 25|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRExtIntrfrnc : 36|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRDiagSpare : 30|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRCANSgnlSpvFld : 29|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRCANRxErr : 28|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRCANIDAddrsUnsbl : 27|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRAntTngFltPrsnt : 26|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRAmbTmpOutRngLw : 42|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRAmbTmpOutRngHi : 41|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRAlgnFltPrsnt : 39|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RFSRRNumValidTargets : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ RFSrrRollingCnt : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSRRModeCmdFdbk : 4|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTimeStamp : 10|11@0+ (1,0) [0|2047] "ms"  EOCM_F_FO
 SG_ RFSrrBurstChecksum : 55|16@0+ (1,0) [0|65535] ""  EOCM_F_FO
 SG_ RFSrrTimeStampV : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 1233 RF_SRR_Object_Track1: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange1 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth1 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID1 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange1 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate1 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth1 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID1 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation1 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp1 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus1 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1234 RF_SRR_Object_Track2: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange2 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth2 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID2 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange2 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate2 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth2 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID2 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation2 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp2 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus2 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1235 RF_SRR_Object_Track3: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange3 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth3 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID3 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange3 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate3 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth3 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID3 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation3 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp3 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus3 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1236 RF_SRR_Object_Track4: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange4 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth4 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID4 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange4 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate4 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth4 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID4 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation4 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp4 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus4 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1237 RF_SRR_Object_Track5: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange5 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth5 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID5 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange5 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate5 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth5 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID5 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation5 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp5 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus5 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1238 RF_SRR_Object_Track6: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange6 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth6 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID6 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange6 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate6 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth6 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID6 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation6 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp6 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus6 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1239 RF_SRR_Object_Track7: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange7 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth7 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID7 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange7 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate7 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth7 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID7 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation7 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp7 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus7 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1240 RF_SRR_Object_Track8: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange8 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth8 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID8 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange8 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate8 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth8 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID8 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation8 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp8 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus8 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1241 RF_SRR_Object_Track9: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange9 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth9 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID9 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange9 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate9 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth9 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID9 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation9 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp9 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus9 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1242 RF_SRR_Object_Track10: 8 RFSRR_FO
 SG_ RFSrrTrkObsRange10 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RFSrrTrkRawAzimuth10 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RFSrrBurstID10 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkRange10 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RFSrrTrkRRate10 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RFSrrTrkAzimuth10 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RFSrrTrkObjID10 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RFSrrTrkObjElevation10 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RFSrrTrkDynamProp10 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RFSrrTrkMeasStatus10 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1056 F_Vision_Obj_Header: 6 VIS_FO
 SG_ FVsnSnsrBlckd : 24|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ ClstInPathVehObjID : 30|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FrtVsnFld : 6|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnIniDiagSuccCmpt : 5|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnSrvAlgnInPrcs : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnUnvlbl : 7|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisionRollingCnt : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVISModeCmdFdbk : 4|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FVisionNumValidTrgts : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FVisionTimeStampV : 31|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisionTimeStamp : 10|11@0+ (1,0) [0|2047] "ms"  EOCM_F_FO
 SG_ VISBurstChecksum : 39|16@0+ (1,0) [0|65535] ""  EOCM_F_FO

BO_ 1057 F_Vision_Obj_Track_1: 8 VIS_FO
 SG_ FwdVsnRngTrk1Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk1Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr1Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnVertPosTrk1 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FVisBurstIDTrk1 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk1 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk1 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk1 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk1 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk1 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk1 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ ObjDirTrk1 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 1058 F_Vision_Obj_Track_2: 8 VIS_FO
 SG_ FwdVsnVertPosTrk2 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk2Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk2Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ ObjDirTrk2 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FwdVsnObjTypTr2Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk2 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk2 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk2 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk2 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk2 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk2 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk2 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1059 F_Vision_Obj_Track_3: 8 VIS_FO
 SG_ FwdVsnVertPosTrk3 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk3Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk3Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr3Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk3 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk3 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk3 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk3 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk3 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk3 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk3 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk3 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1060 F_Vision_Obj_Track_4: 8 VIS_FO
 SG_ FwdVsnVertPosTrk4 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FVisionMeasStatTrk4 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk4 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FwdVsnRngTrk4Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk4Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr4Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk4 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk4 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ ObjDirTrk4 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisionConfTrk4 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk4 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk4 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1061 F_Vision_Obj_Track_5: 8 VIS_FO
 SG_ FwdVsnVertPosTrk5 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk5Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk5Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr5Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk5 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk5 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk5 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk5 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk5 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk5 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk5 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk5 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1062 F_Vision_Obj_Track_6: 8 VIS_FO
 SG_ FwdVsnVertPosTrk6 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk6Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk6Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr6Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk6 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk6 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk6 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk6 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk6 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk6 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk6 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk6 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1538 LF_SRR_Trace_Data: 8 LFSRR_FO
 SG_ LFSRRYear : 7|8@0+ (1,0) [0|0] ""  EOCM_F_FO
 SG_ LFSRRSerialNmbr : 39|32@0+ (1,0) [0|0] ""  EOCM_F_FO
 SG_ LFSRRJulianDate : 15|24@0+ (1,0) [0|0] ""  EOCM_F_FO

BO_ 1537 R_SRR_Trace_data: 8 RSRR_FO
 SG_ RSRRYear : 7|8@0+ (1,0) [0|0] ""  EOCM_F_FO
 SG_ RSRRSerialNmbr : 39|32@0+ (1,0) [0|0] ""  EOCM_F_FO
 SG_ RSRRJulianDate : 15|24@0+ (1,0) [0|0] ""  EOCM_F_FO

BO_ 1089 F_Vision_Obj_Track_7: 8 VIS2_FO
 SG_ FVisBurstIDTrk7 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk7 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk7 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk7 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk7 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk7 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk7 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FwdVsnRngTrk7Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnObjTypTr7Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnAzmthTrk7Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnVertPosTrk7 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ ObjDirTrk7 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 1090 F_Vision_Obj_Track_8: 8 VIS2_FO
 SG_ FVisBurstIDTrk8 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk8 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FwdVsnAzmthTrk8Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnVertPosTrk8 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk8Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnObjTypTr8Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk8 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisionConfTrk8 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk8 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk8 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk8 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk8 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1091 F_Vision_Obj_Track_9: 8 VIS2_FO
 SG_ FwdVsnVertPosTrk9 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk9Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk9Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr9Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk9 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk9 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk9 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk9 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk9 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk9 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk9 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk9 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1092 F_Vision_Obj_Track_10: 8 VIS2_FO
 SG_ FwdVsnRngTrk10Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk10Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr10Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnVertPosTrk10 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ ObjDirTrk10 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk10 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk10 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk10 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk10 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk10 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk10 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk10 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1120 F_LRR_Obj_Header: 8 LRR_FO
 SG_ FLRRRollingCount : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRModeCmdFdbk : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRNumValidTargets : 20|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ FLRRTimeStampV : 31|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRTimeStamp : 2|11@0+ (1,0) [0|2047] "ms"  EOCM_F_FO
 SG_ FLRRRoadTypeInfo : 5|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRBurstChecksum : 55|16@0+ (1,0) [0|65535] ""  EOCM_F_FO
 SG_ FLRRDiagSpare : 30|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRVltgOutRngLo : 44|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRVltgOutRngHi : 43|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRSvcAlgnInPrcs : 38|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRSnsrBlckd : 45|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRSnstvFltPrsntInt : 24|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRPlntAlgnInProc : 37|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRMsalgnYawRt : 47|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRMsalgnYawLt : 46|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRLonVelPlsblityFlt : 35|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRYawRtPlsblityFlt : 34|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRMsalgnPtchUp : 32|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRMsalgnPtchDn : 33|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRInitDiagCmplt : 40|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRHWFltPrsntInt : 25|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRExtIntrfrnc : 36|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRCANSgnlSpvFld : 29|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRCANRxErr : 28|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRTunlDtctd : 27|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRAmbTmpOutRngLw : 42|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRAmbTmpOutRngHi : 41|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRAntTngFltPrsnt : 26|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRAlgnFltPrsnt : 39|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 1121 F_LRR_Obj_Track_1: 8 LRR_FO
 SG_ FLRRTrk1BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk1Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk1RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk1RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk1DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk1Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk1Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk1MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk1ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FLRRTrk1Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1122 F_LRR_Obj_Track_2: 8 LRR_FO
 SG_ FLRRTrk2BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk2Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk2RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk2RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk2DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk2Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk2Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk2MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk2ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FLRRTrk2Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1123 F_LRR_Obj_Track_3: 8 LRR_FO
 SG_ FLRRTrk3BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk3Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk3RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk3RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk3DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk3Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk3Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk3MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk3ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FLRRTrk3Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1124 F_LRR_Obj_Track_4: 8 LRR_FO
 SG_ FLRRTrk4BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk4Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk4RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk4RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk4DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk4Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk4Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk4MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk4ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FLRRTrk4Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1125 F_LRR_Obj_Track_5: 8 LRR_FO
 SG_ FLRRTrk5BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk5Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk5RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk5RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk5DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk5Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk5Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk5MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk5ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FLRRTrk5Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1126 F_LRR_Obj_Track_6: 8 LRR_FO
 SG_ FLRRTrk6BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk6Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk6RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk6Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk6RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk6DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk6Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk6Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk6MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk6ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1127 F_LRR_Obj_Track_7: 8 LRR_FO
 SG_ FLRRTrk7Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk7BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk7Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk7RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk7RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk7DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk7Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk7Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk7MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk7ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1128 F_LRR_Obj_Track_8: 8 LRR_FO
 SG_ FLRRTrk8Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk8BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk8Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk8RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk8RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk8DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk8Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk8Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk8MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk8ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1129 F_LRR_Obj_Track_9: 8 LRR_FO
 SG_ FLRRTrk9Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk9BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk9Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk9RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk9RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk9DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk9Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk9Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk9MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk9ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1130 F_LRR_Obj_Track_10: 8 LRR_FO
 SG_ FLRRTrk10Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk10BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk10Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk10RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk10RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk10DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk10Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk10Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk10MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk10ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1131 F_LRR_Obj_Track_11: 8 LRR_FO
 SG_ FLRRTrk11Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk11BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk11Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk11RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk11RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk11DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk11Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk11Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk11MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk11ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1132 F_LRR_Obj_Track_12: 8 LRR_FO
 SG_ FLRRTrk12Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk12BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk12Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk12RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk12RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk12DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk12Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk12Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk12MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk12ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1133 F_LRR_Obj_Track_13: 8 LRR_FO
 SG_ FLRRTrk13Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk13BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk13Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk13RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk13RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk13DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk13Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk13Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk13MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk13ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1134 F_LRR_Obj_Track_14: 8 LRR_FO
 SG_ FLRRTrk14Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk14BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk14Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk14RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk14RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk14DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk14Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk14Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk14MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk14ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1135 F_LRR_Obj_Track_15: 8 LRR_FO
 SG_ FLRRTrk15Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk15MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk15Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk15ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FLRRTrk15BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk15Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk15RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk15RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk15DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk15Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO

BO_ 1136 F_LRR_Obj_Track_16: 8 LRR_FO
 SG_ FLRRTrk16Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk16BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk16Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk16RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk16RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk16DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk16Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk16Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk16MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk16ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1137 F_LRR_Obj_Track_17: 8 LRR_FO
 SG_ FLRRTrk17Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk17BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk17Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk17RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk17RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk17DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk17Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk17Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk17MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk17ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1138 F_LRR_Obj_Track_18: 8 LRR_FO
 SG_ FLRRTrk18Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk18BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk18Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk18RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk18RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk18DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk18Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk18Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk18MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk18ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1139 F_LRR_Obj_Track_19: 8 LRR_FO
 SG_ FLRRTrk19Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk19BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk19Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk19RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk19RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk19DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk19Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk19Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk19MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk19ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1140 F_LRR_Obj_Track_20: 8 LRR_FO
 SG_ FLRRTrk20Conf : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk20BurstID : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk20Range : 5|11@0+ (0.125,0) [0|255.875] "m"  EOCM_F_FO
 SG_ FLRRTrk20RangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ FLRRTrk20RangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  EOCM_F_FO
 SG_ FLRRTrk20DynProp : 38|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk20Azimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  EOCM_F_FO
 SG_ FLRRTrk20Width : 55|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FLRRTrk20MeasStatus : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk20ObjectID : 61|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1141 Long_Range_Radar_add_Info_1: 8 LRR_FO
 SG_ FLRRAddInfo1BurstID : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ Cut_In_Out_Pot_Objtrk1 : 15|6@0+ (0.02,0) [0|1.26] ""  EOCM_F_FO
 SG_ ObjLossInfoObjTrk1 : 9|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ Cut_In_Out_Pot_Objtrk2 : 23|6@0+ (0.02,0) [0|1.26] ""  EOCM_F_FO
 SG_ ObjLossInfoObjTrk2 : 17|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ Cut_In_Out_Pot_Objtrk3 : 31|6@0+ (0.02,0) [0|1.26] ""  EOCM_F_FO
 SG_ ObjLossInfoObjTrk3 : 25|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ Cut_In_Out_Pot_Objtrk4 : 39|6@0+ (0.02,0) [0|1.26] ""  EOCM_F_FO
 SG_ ObjLossInfoObjTrk4 : 33|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ Cut_In_Out_Pot_Objtrk5 : 47|6@0+ (0.02,0) [0|1.26] ""  EOCM_F_FO
 SG_ ObjLossInfoObjTrk5 : 41|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ Cut_In_Out_Pot_Objtrk6 : 55|6@0+ (0.02,0) [0|1.26] ""  EOCM_F_FO
 SG_ ObjLossInfoObjTrk6 : 49|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1321 F_Fwd_Fus_Obj_TrackB_9: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn9 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB9RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB9Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB9MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB9DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB9RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB9RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB9Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB9ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB9ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB9ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB9ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB9ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB9ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB9NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB9LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB9LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1320 F_Fwd_Fus_Obj_TrackB_8: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn8 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB8RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB8Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB8MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB8DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB8RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB8RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB8Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB8ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB8ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB8ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB8ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB8ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB8ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB8NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB8LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB8LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1319 F_Fwd_Fus_Obj_TrackB_7: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn7 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB7RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB7Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB7MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB7DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB7RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB7RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB7Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB7ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB7ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB7ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB7ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB7ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB7ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB7NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB7LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB7LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1318 F_Fwd_Fus_Obj_TrackB_6: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn6 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB6RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB6Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB6MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB6DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB6RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB6RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB6Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB6ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB6ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB6ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB6ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB6ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB6ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB6NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB6LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB6LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1317 F_Fwd_Fus_Obj_TrackB_5: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn5 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB5RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB5Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB5MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB5DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB5RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB5RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB5Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB5ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB5ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB5ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB5ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB5ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB5ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB5NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB5LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB5LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1316 F_Fwd_Fus_Obj_TrackB_4: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn4 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB4RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB4Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB4MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB4DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB4RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB4RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB4Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB4ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB4ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB4ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB4ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB4ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB4ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB4NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB4LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB4LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1315 F_Fwd_Fus_Obj_TrackB_3: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn3 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB3RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB3Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB3MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB3DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB3RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB3RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB3Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB3ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB3ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB3ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB3ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB3ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB3ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB3NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB3LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB3LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1314 F_Fwd_Fus_Obj_TrackB_2: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn2 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB2RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB2Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB2MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB2DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB2RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB2RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB2Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB2ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB2ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB2ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB2ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB2ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB2ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB2NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB2LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB2LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1327 F_Fwd_Fus_Obj_TrackB_15: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn15 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB15RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB15Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB15MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB15DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB15RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB15RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB15Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB15ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB15ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB15ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB15ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTkB15ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB15ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB15NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB15LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB15LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1326 F_Fwd_Fus_Obj_TrackB_14: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn14 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB14RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB14Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB14MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB14DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB14RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB14RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB14Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB14ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB14ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB14ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB14ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB14ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB14ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB14NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB14LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB14LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1325 F_Fwd_Fus_Obj_TrackB_13: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn13 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB13RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB13Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB13MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB13DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB13RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB13RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB13Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB13ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB13ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB13ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB13ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB13ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB13ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB13NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB13LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB13LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1324 F_Fwd_Fus_Obj_TrackB_12: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn12 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB12RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB12Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB12MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB12DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB12RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB12RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB12Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB12ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB12ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB12ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB12ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB12ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB12ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB12NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB12LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB12LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1323 F_Fwd_Fus_Obj_TrackB_11: 7 EOCM_F_FO
 SG_ FwdFusTrkB11DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB11RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB11RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB11Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB11ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB11ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB11ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB11ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB11ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB11ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB11NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB11LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB11LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkBAnlgRlLn11 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB11RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB11Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB11MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO

BO_ 1322 F_Fwd_Fus_Obj_TrackB_10: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn10 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB10RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB10Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB10MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB10DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB10RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB10RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB10Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB10ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB10ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB10ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB10ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB10ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB10ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB10NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB10LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB10LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1313 F_Fwd_Fus_Obj_TrackB_1: 7 EOCM_F_FO
 SG_ FwdFusTrkBAnlgRlLn1 : 46|8@0- (0.1,0) [-12.8|12.7] ""  Dummy_FO
 SG_ FwdFusTrkB1RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB1Width : 5|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FwdFusTrkB1MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkB1DynProp : 11|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB1RelLnAccl : 8|9@0- (0.125,0) [-32|31.875] "m/s^2"  Dummy_FO
 SG_ FwdFusTrkB1RelLane : 31|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkB1Height : 54|6@0+ (0.25,0) [0|15.75] "m"  Dummy_FO
 SG_ FFusTrkB1ObjSrcLFSrr : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB1ObjSrcLCSrr : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB1ObjSrcRCSrr : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB1ObjSrcRFSrr : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FFusTrkB1ObjSrcVIs : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB1ObjSrcLrr : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FwdFusTrkB1NmCycTrkd : 39|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB1LatPosDev : 36|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkB1LngPosDev : 33|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1305 F_Fwd_Fus_Obj_TrackA_9: 8 EOCM_F_FO
 SG_ FwdFusTrkA9RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA9ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA9MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA9LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA9RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA9Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA9LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA9RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA9MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA9ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1304 F_Fwd_Fus_Obj_TrackA_8: 8 EOCM_F_FO
 SG_ FwdFusTrkA8RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA8ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA8MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA8LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA8RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA8Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA8LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA8RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA8MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA8ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1303 F_Fwd_Fus_Obj_TrackA_7: 8 EOCM_F_FO
 SG_ FwdFusTrkA7RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA7ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA7MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA7LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA7RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA7Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA7LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA7RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA7MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA7ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1302 F_Fwd_Fus_Obj_TrackA_6: 8 EOCM_F_FO
 SG_ FwdFusTrkA6RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA6ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA6MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA6LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA6RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA6Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA6LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA6RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA6MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA6ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1301 F_Fwd_Fus_Obj_TrackA_5: 8 EOCM_F_FO
 SG_ FwdFusTrkA5RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA5ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA5MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA5LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA5RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA5Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA5LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA5RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA5MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA5ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1300 F_Fwd_Fus_Obj_TrackA_4: 8 EOCM_F_FO
 SG_ FwdFusTrkA4RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA4ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA4MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA4LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA4RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA4Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA4LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA4RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA4MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA4ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1299 F_Fwd_Fus_Obj_TrackA_3: 8 EOCM_F_FO
 SG_ FwdFusTrkA3RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA3ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA3MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA3LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA3RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA3Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA3LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA3RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA3MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA3ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1298 F_Fwd_Fus_Obj_TrackA_2: 8 EOCM_F_FO
 SG_ FwdFusTrkA2RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA2ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA2MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA2LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA2RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA2Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA2LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA2RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA2MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA2ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1311 F_Fwd_Fus_Obj_TrackA_15: 8 EOCM_F_FO
 SG_ FwdFusTrkA15RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA15ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA15MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA15LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA15RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA15Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA15LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA15RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA15MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA15ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1310 F_Fwd_Fus_Obj_TrackA_14: 8 EOCM_F_FO
 SG_ FwdFusTrkA14RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA14ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA14MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA14LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA14RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA14Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA14LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA14RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA14MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA14ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1309 F_Fwd_Fus_Obj_TrackA_13: 8 EOCM_F_FO
 SG_ FwdFusTrkA13RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA13ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA13MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA13LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA13RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA13Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA13LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA13RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA13MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA13ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1308 F_Fwd_Fus_Obj_TrackA_12: 8 EOCM_F_FO
 SG_ FwdFusTrkA12RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA12ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA12MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA12LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA12RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA12Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA12LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA12RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA12MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA12ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1307 F_Fwd_Fus_Obj_TrackA_11: 8 EOCM_F_FO
 SG_ FwdFusTrkA11ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA11MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA11LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA11RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA11Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA11LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA11RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA11MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA11ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO
 SG_ FwdFusTrkA11RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO

BO_ 1306 F_Fwd_Fus_Obj_TrackA_10: 8 EOCM_F_FO
 SG_ FwdFusTrkA10RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA10ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA10MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA10LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA10RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA10Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA10LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA10RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA10MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA10ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1297 F_Fwd_Fus_Obj_TrackA_1: 8 EOCM_F_FO
 SG_ FwdFusTrkA1RollingCnt : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA1ObjectID : 5|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ FwdFusTrkA1MsgIndex : 15|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ FwdFusTrkA1LongPos : 11|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO
 SG_ FwdFusTrkA1RelLongVel : 31|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA1Confidence : 36|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA1LatPos : 34|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ FwdFusTrkA1RelLatVel : 55|11@0- (0.125,0) [-128|127.875] "m/s"  Dummy_FO
 SG_ FwdFusTrkA1MeasStatus : 60|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ FwdFusTrkA1ObjType : 58|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 1296 F_Fwd_Fus_Obj_Header: 7 EOCM_F_FO
 SG_ F_FusHeadRollingCount : 7|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ F_FusHeadFuncState : 5|2@0+ (1,0) [0|3] ""  Dummy_FO
 SG_ F_FusHedNmValTargts : 3|4@0+ (1,0) [0|15] ""  Dummy_FO
 SG_ F_FusHead_LrrOK : 15|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ F_FusHead_LFSRROK : 14|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ F_FusHead_VIsOK : 13|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ F_FusHead_MapDataOK : 12|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ F_FusHeadTimStmpV : 11|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ F_FusHeadTimStmp : 10|11@0+ (1,0) [0|2047] "ms"  Dummy_FO
 SG_ F_FusHead_LCSRROK : 31|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ F_FusHead_RCSRROK : 30|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ F_FusHead_RFSRROK : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ F_FusHed_ObjClstrCurv : 28|13@0- (5E-005,0) [-0.2048|0.20475] "1/m"  Dummy_FO
 SG_ F_FusHdObjClstTanHdng : 47|8@0- (0.002,0) [-0.256|0.254] "m/m"  Dummy_FO
 SG_ RoadTypeInfo : 55|3@0+ (1,0) [0|7] ""  Dummy_FO

BO_ 257 USDT_Req_to_All_FO_ECUs: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  LRSRR_FO,_DOFIMU2_FO,_DOFIMU1_FO,DMS_FO,AMM_FO,EOCM2A_K2_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM2A_IMX6_FO,EOCM2A_K1_FO,NVS_FO,CIPM_FO,VIS2_FO,RRSRR_FO,VIS_FO,RFSRR_FO,LRR_FO,LFSRR_FO,RSRR_FO,EOCM_F_FO

BO_ 584 USDT_Req_to_6DOFIMU2_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  _DOFIMU2_FO

BO_ 1348 UUDT_Resp_From_6DOFIMU1_FO: 8 _DOFIMU1_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1604 USDT_Resp_From_6DOFIMU1_FO: 8 _DOFIMU1_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 580 USDT_Req_to_6DOFIMU1_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  _DOFIMU1_FO

BO_ 1349 UUDT_Resp_From_DMS_FO: 8 DMS_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1605 USDT_Resp_From_DMS_FO: 8 DMS_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 581 USDT_Req_to_DMS_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  DMS_FO

BO_ 1372 UUDT_Resp_From_AMM_FO: 8 AMM_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1628 USDT_Resp_From_AMM_FO: 8 AMM_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 604 USDT_Req_to_AMM_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  AMM_FO

BO_ 1622 USDT_Resp_From_EOCM2B_IMX6_FO: 8 EOCM2B_IMX6_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 1366 UUDT_Resp_From_EOCM2B_IMX6_FO: 8 EOCM2B_IMX6_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  TestTool_FO

BO_ 598 USDT_Req_to_EOCM2B_IMX6_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  EOCM2B_IMX6_FO

BO_ 590 USDT_Req_to_Free_4E_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] "" Vector__XXX

BO_ 1338 VPDR_Debug: 8 EOCM_F_FO
 SG_ FrtRWARateDiagFA : 43|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FrtRWABiasDiagFA : 42|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ HWAFrtVal : 41|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ HWADotValFrt : 40|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehAxRangeFA : 39|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehAxRateFA : 38|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehAxBiasFA : 37|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehAxCompFA : 36|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehAxVal : 35|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehAxVal : 34|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FrtRWACorrFA : 33|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FrtRWARangeDiagFA : 32|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehAyBiasFA : 31|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehAyCompFA : 30|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehAyVal : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehAyVal : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehAxCorrFA : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehAxRangeFA : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehAxRateFA : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehAxBiasFA : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehWzVal : 23|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehWzVal : 22|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehAyCorrDiagFA : 21|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehAyRangeFA : 20|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehAyRateFA : 19|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehAyBiasFA : 18|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehAyRangeFA : 17|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehAyRateFA : 16|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehWzCorrDiagFA : 15|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehWzRangeFA : 14|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehWzRateFA : 13|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S1VehWzBiasFA : 12|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehWzRangeFA : 11|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehWzRateFA : 10|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ S2VehWzBiasFA : 9|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehWzCompFA : 8|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ WhlVxLFCorrDiagFA : 7|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ WhlVxRFCorrDiagFA : 6|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ WhlVxLRCorrDiagFA : 5|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ WhlVxRRCorrDiagFA : 4|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ WhlLFVal : 3|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ WhlRFVal : 2|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ WhlLRVal : 1|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ WhlRRVal : 0|1@0+ (1,0) [0|1] ""  Dummy_FO

BO_ 1328 Diag_Debug1: 8 EOCM_F_FO
 SG_ AlrtWrnIndReqFP : 1|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ AlrtWrnIndReqFA : 0|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILRSRRSnsr_FP : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILRSRRSnsr_FA : 23|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILRSRRFrehns_FA : 22|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehPathEstCrvCSFP : 21|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehPathEstCrvCSFA : 20|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehPathWzEstCSFP : 19|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehPathWzEstCSFA : 18|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehPathVyEstCSFP : 17|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehPathVyEstCSFA : 16|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehPathVxEstCSFP : 15|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VehPathVxEstCSFA : 14|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtTTCCSFP : 13|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtTTCCSFA : 12|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLyCSFP : 11|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLyCSFA : 10|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThLxVxAxCSFP : 9|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThLxVxAxCSFA : 8|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ CurSetValDiagFP : 7|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ CurSetValDiagFA : 6|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ CrsAltDvrSlTpDiagFP : 5|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ CrsAltDvrSlTpDiagFA : 4|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FCAStatDiagFP : 3|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FCAStatDiagFA : 2|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ RVBBrkCtrlStFP : 62|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ RVBBrkCtrlStFA : 61|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ RVBOpStFP : 60|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ RVBOpStFA : 59|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ RVBBrkCtrlAccFP : 58|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ RVBBrkCtrlAccFA : 57|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ RVBAxlTrqRqFP : 56|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ RVBAxlTrqRqFA : 55|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRUPATTCFP : 54|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRUPATTCFA : 53|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRHiThrtObjIDFP : 52|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRHiThrtObjIDFA : 51|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRHiThrtTTCFP : 50|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRHiThrtTTCFA : 49|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRHiThrtLxFP : 48|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRHiThrtLxFA : 47|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRHiThrtLyFP : 46|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TVRHiThrtLyFA : 45|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFFuncStFP : 44|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFFuncStFA : 43|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFObjIDFP : 42|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFObjIDFA : 41|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFLatPstnVelRatFP : 40|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFLatPstnVelRatFA : 39|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFLonPstnVelFP : 38|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFLonPstnVelFA : 37|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFRltvLatVelDiagFP : 36|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFRltvLatVelDiagFA : 35|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFRltvLatPstnDiagFP : 34|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFRltvLatPstnDiagFA : 33|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFNrRltvLonPstnFP : 32|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFNrRltvLonPstnFA : 31|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFLonPstnVelRatFP : 30|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFLonPstnVelRatFA : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFConfDiagFP : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFConfDiagFA : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFRltvLnDiagFP : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ROFRltvLnDiagFA : 25|1@0+ (1,0) [0|1] ""  Dummy_FO

BO_ 1335 Diag_Debug3: 8 EOCM_F_FO
 SG_ BrkSysCmdAxDiagFPQ : 63|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ BrkSysCmdAxDiagFAQ : 62|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ AxleTorqReqDiagFPQ : 61|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ AxleTorqReqDiagFAQ : 60|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ AutoBrkTypeDiagFPQ : 59|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ AutoBrkTypeDiagFAQ : 58|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtTTCFPQ : 57|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtTTCFAQ : 56|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtTmpMemFPQ : 55|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtTmpMemFAQ : 54|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLxVxAxFPQ : 53|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLxVxAxFAQ : 52|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtVyFPQ : 51|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtVyFAQ : 50|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLyFPQ : 49|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLyFAQ : 48|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtConPriFPQ : 47|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtConPriFAQ : 46|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtCJLFPQ : 45|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtCJLFAQ : 44|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPDynPropDiagFPQ : 43|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPDynPropDiagFAQ : 42|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFRdTypInfoFPQ : 41|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFRdTypInfoFAQ : 40|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFRltvLnDiagFPQ : 39|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFRltvLnDiagFAQ : 38|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNumCycDiagFPQ : 37|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNumCycDiagFAQ : 36|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNumFusCyclsFPQ : 35|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNumFusCyclsFAQ : 34|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNrRltvLonPstnFPQ : 33|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNrRltvLonPstnFAQ : 32|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonVelAccRatFPQ : 31|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonVelAccRatFAQ : 30|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonPstnVelRatFPQ : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonPstnVelRatFAQ : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonPosVelAccFPQ : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonPosVelAccFAQ : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLatVelDiagFPQ : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLatVelDiagFAQ : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLatPstnDiagFPQ : 23|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLatPstnDiagFAQ : 22|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFConfDiagFPQ : 21|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFConfDiagFAQ : 20|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFOvrlpRtlChk_FAQ : 19|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFOvrlpRtlChk_FAQ : 18|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRROvrlpRtlChk_FAQ : 17|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISOvrlpRtlChk_FAQ : 16|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISAlign_FAQ : 15|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISSnsr_FAQ : 14|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISSnsr_FPQ : 13|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISFrshns_FAQ : 12|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRRAlign_FAQ : 11|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRRFrshns_FAQ : 10|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRRSnsr_FAQ : 9|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRRSnsr_FPQ : 8|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFSRRAlign_FAQ : 7|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFSRRFrehns_FAQ : 6|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFSRRSnsr_FAQ : 5|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFSRRSnsr_FPQ : 4|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFSRRSnsr_FPQ : 3|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFSRRSnsr_FAQ : 2|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFSRRFrshns_FAQ : 1|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFSRRAlign_FAQ : 0|1@0+ (1,0) [0|1] ""  Dummy_FO

BO_ 1331 Diag_Debug2: 8 EOCM_F_FO
 SG_ TCPHiThrtTmpMemFP : 63|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtTmpMemFA : 62|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtConPriFP : 61|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtConPriFA : 60|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtCJLFP : 59|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtTTCFP : 58|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtTTCFA : 57|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtVyFP : 56|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtVyFA : 55|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLyFP : 54|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLyFA : 53|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLxVxAxFP : 52|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtLxVxAxFA : 51|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFRdTypInfoFP : 50|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFRdTypInfoFA : 49|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ AutoBrkTypeDiagFA : 48|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ BrkSysCmdAxDiagFP : 47|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ BrkSysCmdAxDiagFA : 46|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ AxleTorqReqDiagFP : 45|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ AxleTorqReqDiagFA : 44|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPDynPropDiagFP : 43|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPDynPropDiagFA : 42|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ TCPHiThrtCJLFA : 41|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNumFusCyclsFP : 40|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ AutoBrkTypeDiagFP : 39|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNumFusCyclsFA : 38|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLatVelDiagFP : 37|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLatVelDiagFA : 36|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLatPstnDiagFP : 35|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLatPstnDiagFA : 34|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNumCycDiagFP : 33|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNumCycDiagFA : 32|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFRltvLnDiagFP : 31|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFRltvLnDiagFA : 30|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFConfDiagFP : 29|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFConfDiagFA : 28|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonVelAccRatFP : 27|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonVelAccRatFA : 26|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonPstnVelRatFP : 25|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonPstnVelRatFA : 24|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNrRltvLonPstnFP : 23|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFNrRltvLonPstnFA : 22|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonPosVelAccFP : 21|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ FOFLonPosVelAccFA : 20|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISOvrlpRtlChk_FA : 19|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRROvrlpRtlChk_FA : 18|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFOvrlpRtlChk_FA : 17|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFOvrlpRtlChk_FA : 16|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISAlign_FA : 15|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISFrshns_FA : 14|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISSnsr_FP : 13|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ VISSnsr_FA : 12|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRRAlign_FA : 11|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRRFrshns_FA : 10|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRRSnsr_FP : 9|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ LRRSnsr_FA : 8|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFSRRAlign_FA : 7|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFSRRFrehns_FA : 6|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFSRRSnsr_FP : 5|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ IRFSRRSnsr_FA : 4|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFSRRAlign_FA : 3|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFSRRFrshns_FA : 2|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFSRRSnsr_FP : 1|1@0+ (1,0) [0|1] ""  Dummy_FO
 SG_ ILFSRRSnsr_FA : 0|1@0+ (1,0) [0|1] ""  Dummy_FO

BO_ 1787 AL_Test_Tool_Rsp_RFSRR: 8 RFSRR_FO
 SG_ RFSRREngRspDta : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  Dummy_FO

BO_ 1788 AL_Test_Tool_Rsp_LFSRR: 8 LFSRR_FO
 SG_ LFSRREngRspDta : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  Dummy_FO

BO_ 1790 AL_Test_Tool_Req_RFSRR: 8 Dummy_FO
 SG_ RFSRREngCmdDta : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  RFSRR_FO

BO_ 1791 AL_Test_Tool_Req_LFSRR: 8 Dummy_FO
 SG_ LFSRREngCmdDta : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  LFSRR_FO

BO_ 1786 AL_Test_Tool_Rsp_RSRR: 8 RSRR_FO
 SG_ RSRREngRspDta : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  Dummy_FO

BO_ 1789 AL_Test_Tool_Req_RSRR: 8 Dummy_FO
 SG_ RSRREngCmdDta : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  RSRR_FO

BO_ 1149 F_LRR_Azmth_Rate_Info_4: 8 LRR_FO
 SG_ FLRRTrk20AzmthRate : 50|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk19AzmthRate : 45|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk18AzmthRate : 24|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk17AzmthRate : 19|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk16AzmthRate : 14|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRAzRtInf4BurstID : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1148 F_LRR_Azmth_Rate_Info_3: 8 LRR_FO
 SG_ FLRRTrk15AzmthRate : 50|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk14AzmthRate : 45|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk13AzmthRate : 24|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk12AzmthRate : 19|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk11AzmthRate : 14|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRAzRtInf3BurstID : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1147 F_LRR_Azmth_Rate_Info_2: 8 LRR_FO
 SG_ FLRRTrk9AzmthRate : 45|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk8AzmthRate : 24|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk7AzmthRate : 19|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk6AzmthRate : 14|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk10AzmthRate : 50|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRAzRtInf2BurstID : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1146 F_LRR_Azmth_Rate_Info_1: 8 LRR_FO
 SG_ FLRRTrk5AzmthRate : 50|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk4AzmthRate : 45|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk3AzmthRate : 24|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk2AzmthRate : 19|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRTrk1AzmthRate : 14|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FLRRAzRtInf1BurstID : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1143 Long_Range_Radar_add_Info_3: 8 LRR_FO
 SG_ FLRRAddInfo3BurstID : 57|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk13ObstType : 60|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk20ObstTypeConf : 52|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk20ObstType : 55|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk19ObstTypeConf : 44|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk19ObstType : 47|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk18ObstTypeConf : 36|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk18ObstType : 39|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk17ObstTypeConf : 28|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk17ObstType : 31|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk16ObstTypeConf : 20|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk16ObstType : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk15ObstTypeConf : 12|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk15ObstType : 15|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk14ObstTypeConf : 4|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk14ObstType : 7|3@0+ (1,0) [0|7] ""  EOCM_F_FO

BO_ 1142 Long_Range_Radar_add_Info_2: 8 LRR_FO
 SG_ FLRRAddInfo2BurstID : 62|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRTrk13ObstTypeConf : 60|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk12ObstTypeConf : 52|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk12ObstType : 55|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk11ObstTypeConf : 44|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk11ObstType : 47|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk10ObstTypeConf : 36|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk10ObstType : 39|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk9ObstTypeConf : 28|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk9ObstType : 31|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk8ObstTypeConf : 20|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk8ObstType : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk7ObstTypeConf : 12|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk7ObstType : 15|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRTrk1ObstTypeConf : 4|5@0+ (0.0323,0) [0|1.0013] ""  EOCM_F_FO
 SG_ FLRRTrk1ObstType : 7|3@0+ (1,0) [0|7] ""  EOCM_F_FO

BO_ 851 F_Vision_Environment_4: 8 VIS_FO
 SG_ LnMrkg3LnPrvwDst : 45|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTtlNmLnMrkgDetRt : 4|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsRtLinCrsTm : 25|5@0+ (0.1,0) [0|3.1] "s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsNumPrlLnsDetRt : 33|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsNumPrlLnsDetLt : 36|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsCrvtGrdntLftV : 31|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLtLinCrsTm : 30|5@0+ (0.1,0) [0|3.1] "s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnPrvwDst : 50|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnMrkgTypChgDst : 61|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnMrkgTypChgDst : 40|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnMrkgWdth : 62|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnMarkrElvtd : 51|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4AnchrLnLin : 57|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnMrkgWdth : 41|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnMarkrElvtd : 46|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3AnchrLnLin : 52|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBurstID2 : 1|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsCrvtGrdntLft : 15|16@0- (5.96E-008,0) [-0.0019529728|0.0019529132] "1/rad/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 597 USDT_Req_to_VIS2_FO: 8 TestTool_FO
 SG_ DgnInf : 7|64@0+ (1,0) [0|1.84467440737096E+019] ""  CIPM_FO,VIS2_FO

BO_ 1204 RR_SRR_Object_Track4: 8 RRSRR_FO
 SG_ RRSrrTrkRawAzimuth4 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate4 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange4 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange4 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID4 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation4 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus4 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp4 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth4 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RRSrrBurstID4 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1203 RR_SRR_Object_Track3: 8 RRSRR_FO
 SG_ RRSrrTrkRawAzimuth3 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate3 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange3 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange3 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID3 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation3 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus3 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp3 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth3 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO
 SG_ RRSrrBurstID3 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1202 RR_SRR_Object_Track2: 8 RRSRR_FO
 SG_ RRSrrBurstID2 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkRawAzimuth2 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate2 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange2 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange2 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID2 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation2 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus2 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp2 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth2 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO

BO_ 1201 RR_SRR_Object_Track1: 8 RRSRR_FO
 SG_ RRSrrBurstID1 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkRawAzimuth1 : 61|6@0- (1,0) [-32|31] "deg"  EOCM_F_FO
 SG_ RRSrrTrkRRate1 : 18|11@0- (0.125,0) [-128|127.875] "m/s"  EOCM_F_FO
 SG_ RRSrrTrkRange1 : 2|11@0+ (0.02,0) [0|40.94] "m"  EOCM_F_FO
 SG_ RRSrrTrkObsRange1 : 53|6@0- (0.02,0) [-0.64|0.62] "m"  EOCM_F_FO
 SG_ RRSrrTrkObjID1 : 39|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ RRSrrTrkObjElevation1 : 20|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkMeasStatus1 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSrrTrkDynamProp1 : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSrrTrkAzimuth1 : 34|11@0- (0.125,0) [-128|127.875] "deg"  EOCM_F_FO

BO_ 1200 RR_SRR_Object_Header: 8 RRSRR_FO
 SG_ RRSrrTimeStampV : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSrrTimeStamp : 10|11@0+ (1,0) [0|2047] ""  EOCM_F_FO
 SG_ RRSrrRollingCnt : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ RRSRRNumValidTargets : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ RRSRRModeCmdFdbk : 4|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ RRSRRVltgOutRngLo : 44|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRVltgOutRngHi : 43|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRSvcAlgnInPrcs : 38|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRSnsrBlckd : 45|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRSnstvFltPrsntInt : 24|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRPlntAlgnInProc : 37|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRMsalgnYawRt : 47|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRMsalgnYawLt : 46|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRMsalgnRllRt : 35|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRMsalgnRllLt : 34|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRMsalgnPtchUp : 32|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRMsalgnPtchDn : 33|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRInitDiagCmplt : 40|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRHWFltPrsntInt : 25|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRExtIntrfrnc : 36|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRDiagSpare : 30|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRCANSgnlSpvFld : 29|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRCANRxErr : 28|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRCANIDAddrsUnsbl : 27|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRAntTngFltPrsnt : 26|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRAmbTmpOutRngLw : 42|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRAmbTmpOutRngHi : 41|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSRRAlgnFltPrsnt : 39|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ RRSrrBurstChecksum : 55|16@0+ (1,0) [0|65535] ""  EOCM_F_FO

BO_ 850 F_Vision_Environment_3: 8 VIS_FO
 SG_ LnSnsTtlNmLnMrkgDetLt : 58|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLtLnMrkgWdth : 63|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLtLnMrkgTypChgDst : 62|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTngtOfHdngLnLftV : 23|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTngtOfHdngLnLft : 31|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnCrvtrLftV : 15|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnCrvtrLft : 39|16@0- (9.53E-007,0) [-0.031227904|0.031226951] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkrTypRght : 50|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkrTypLft : 53|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkrElvtdRght : 54|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkrElvtdLft : 55|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBurstID1 : 7|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnQltyCnfdncLvlRght : 22|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnQltyCnfdncLvlLft : 14|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnPrvwDstncRght : 2|3@0+ (10,0) [0|70] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnPrvwDstncLft : 5|3@0+ (10,0) [0|70] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 1414 RVB_TVR_Debug2_FO: 7 EOCM_F_FO
 SG_ VBBrkCntlAccel : 45|12@0- (0.01,0) [-20.48|20.47] "m/s^2"  Dummy_FO
 SG_ VBTOSObjID : 35|6@0+ (1,0) [0|63] ""  Dummy_FO
 SG_ VBTOSTTC : 31|12@0+ (0.025,0) [0|102.375] "s"  Dummy_FO
 SG_ VBTOSLatPstn : 11|11@0- (0.125,0) [-128|127.875] "m"  Dummy_FO
 SG_ VBTOSLonPstn : 7|12@0- (0.125,0) [-256|255.875] "m"  Dummy_FO

