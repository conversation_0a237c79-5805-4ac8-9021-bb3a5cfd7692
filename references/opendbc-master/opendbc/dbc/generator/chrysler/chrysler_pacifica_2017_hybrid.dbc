CM_ "IMPORT _stellantis_common.dbc";

BO_ 514 SPEED_1: 8 XXX
 SG_ SPEED_LEFT : 7|12@0+ (0.071028,0) [0|65535] "m/s" XXX
 SG_ SPEED_RIGHT : 23|12@0+ (0.071028,0) [0|1023] "m/s" XXX

BO_ 653 BRAKE_MODULE: 2 XXX
 SG_ BRAKE_PRESSURE : 15|8@0+ (1,0) [0|255] "" XXX
 SG_ BRAKE_PRESSED : 4|1@0+ (1,0) [0|4] "" XXX

BO_ 746 GEAR: 5 XXX
 SG_ PRNDL : 2|3@0+ (1,0) [0|7] "" XXX
 SG_ GEAR_CHECKSUM : 39|8@0+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 31|4@0+ (1,0) [0|15] "" XXX

BO_ 736 TRIP: 8 XXX
 SG_ COUNTER : 7|16@0+ (1,0) [0|65535] "Meters" XXX
 SG_ COUNTER_2 : 23|16@0+ (1,0) [0|65535] "Meters" XXX

BO_ 658 LKAS_COMMAND: 6 XXX
 SG_ COUNTER : 39|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 47|8@0+ (1,0) [0|255] "" XXX
 SG_ STEERING_TORQUE : 2|11@0+ (1,-1024) [0|1] "" XXX
 SG_ LKAS_CONTROL_BIT : 4|1@0+ (1,0) [0|1] "" XXX

BO_ 705 AUTO_PARK_BUTTON: 8 XXX
 SG_ AUTO_PARK_TOGGLE_2 : 8|1@0+ (1,0) [0|1] "" XXX
 SG_ AUTO_PARK_TOGGLE_1 : 11|1@0+ (1,0) [0|1] "" XXX
 SG_ INCREASING_UNKNOWN : 38|7@0+ (1,0) [0|15] "" XXX

BO_ 719 AUTO_PARK_SIGNALS_1: 8 XXX
 SG_ AUTO_PARK_UNKNOWN_1 : 7|16@0+ (1,0) [0|31] "" XXX

BO_ 671 AUTO_PARK_REQUEST: 8 XXX
 SG_ AUTO_PARK_CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ AUTO_PARK_STATUS : 7|5@0+ (1,0) [0|15] "" XXX
 SG_ AUTO_PARK_COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ AUTO_PARK_MODE : 22|2@0+ (1,0) [0|3] "" XXX
 SG_ AUTO_PARK_CMD : 2|11@0+ (1,-1024) [0|1] "NM" XXX

BO_ 784 AUTO_PARK_LESS_INTERESTING: 8 XXX
 SG_ INCREASING_UNKNOWN : 55|8@0+ (1,0) [0|7] "" XXX
 SG_ AUTO_PARK_PERPENDICULAR_2 : 61|1@0+ (1,0) [0|255] "" XXX

BO_ 826 AUTO_PARK_SIGNALS_3: 8 XXX
 SG_ AUTO_PARK_HAS_CONTROL_3 : 1|1@0+ (1,0) [0|1] "" XXX
 SG_ HUMAN_HAS_CONTROL : 2|1@0+ (1,0) [0|1] "" XXX
 SG_ AUTO_PARK_GEAR_1 : 27|4@0+ (1,0) [0|255] "" XXX
 SG_ AUTO_PARK_GEAR_2 : 35|4@0+ (1,0) [0|15] "" XXX
 SG_ AUTO_PARK_GEAR_3 : 51|4@0+ (1,0) [0|15] "" XXX

BO_ 332 STEERING_2: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ ENERGY_RELATED : 39|16@0+ (1,0) [0|65535] "" XXX
 SG_ STEER_ANGLE_2 : 7|13@0+ (0.3187251,-1307.888) [-360|360] "deg" XXX

BO_ 331 BRAKE_3: 8 XXX
 SG_ BRAKE_RELATED_3 : 7|16@0+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX

BO_ 608 PARKSENSE_SIGNAL: 8 XXX
 SG_ PARKSENSE_DISABLED : 34|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ IN_REVERSE : 10|1@0+ (1,0) [0|255] "" XXX
 SG_ AUTO_PARK_HAS_CONTROL_1 : 16|1@0+ (1,0) [0|255] "" XXX
 SG_ HUMAN_HAS_CONTROL : 17|1@0+ (1,0) [0|3] "" XXX

BO_ 729 LKAS_HEARTBIT: 5 XXX
 SG_ LKAS_STATUS_OK : 31|16@0+ (1,0) [0|65535] "" XXX

BO_ 257 ACCEL_RELATED_101: 5 XXX
 SG_ ENERGY_OR_RPM : 31|8@0+ (1,0) [0|255] "" XXX

BO_ 825 AUDIBLE_BEEP_339: 2 XXX
 SG_ BEEP_339 : 7|16@0+ (1,0) [0|65535] "" XXX

BO_ 168 ACCEL_RELATED_a8: 8 XXX
 SG_ ACCEL_RELATED : 23|16@0+ (1,0) [0|65535] "" XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX

BO_ 270 ACCEL_RELATED_10e: 8 XXX
 SG_ ACCEL_OR_RPM : 7|16@0+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ ELECTRIC_MOTOR : 23|16@0+ (1,0) [0|65535] "" XXX

BO_ 291 ENERGY_RELATED_123: 8 XXX
 SG_ ENERGY_GAIN_LOSS : 18|11@0- (1,0) [0|255] "" XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ ENERGY_SMOOTHER_CURVE : 35|12@0+ (1,0) [0|2047] "" XXX

BO_ 294 ENERGY_RELATED_126: 8 XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ UNKNOWN_126_1 : 3|12@0+ (1,0) [0|4095] "" XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ UNKNOWN_126_2 : 35|12@0+ (1,0) [0|4095] "" XXX
 SG_ ENERGY_GAIN_LOSS_NOISY : 19|12@0+ (1,0) [0|2047] "" XXX

BO_ 308 ACCEL_GAS_134: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ ACCEL_134 : 46|7@0+ (1,0) [0|127] "" XXX

BO_ 532 ENERGY_RELATED_214: 8 XXX
 SG_ NOISY_SLOWLY_DECREASING : 16|9@0+ (1,0) [0|255] "" XXX
 SG_ ENERGY_RELATED : 0|9@0+ (1,0) [0|255] "" XXX

BO_ 655 CHARGING_MAYBE_28F: 8 XXX
 SG_ CHARGING : 1|2@0+ (1,0) [0|3] "" XXX

BO_ 660 BRAKE_RELATED_294: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ BRAKE_PERHAPS_294 : 31|8@0+ (1,0) [0|255] "" XXX

BO_ 764 ACCEL_RELATED_2FC: 8 XXX
 SG_ ACCEL_2FC : 13|6@0+ (1,0) [0|255] "" XXX

BO_ 816 TRACTION_BUTTON: 8 XXX
 SG_ TRACTION_OFF : 19|1@0+ (1,0) [0|3] "" XXX
 SG_ TOGGLE_PARKSENSE : 52|1@0+ (1,0) [0|3] "" XXX

BO_ 878 ACCEL_RELATED_36E: 8 XXX
 SG_ ACCEL_OR_RPM_2 : 15|8@0+ (1,0) [0|255] "" XXX
 SG_ ACCEL_OR_RPM_1 : 7|8@0+ (1,0) [0|255] "" XXX

BO_ 324 SPEED_2: 8 XXX
 SG_ SPEED_2 : 31|16@0+ (0.01,0) [0|255] "m/s" XXX

BO_ 832 UNKNOWN_340: 8 XXX
 SG_ SPEED_DIGITAL : 63|8@0+ (1,0) [0|255] "mph" XXX

CM_ SG_ 653 BRAKE_PRESSURE "max seems to be 148";
CM_ SG_ 746 PRNDL "5=L, 4=D, 3=N, 2=R, 1=P";
CM_ SG_ 320 BRAKE_PRESSED_2 "Value is 5 when brake is pressed by human, 1 when ACC brake";
CM_ SG_ 320 BRAKE_PRESSED_ACC "set when ACC brakes";
CM_ SG_ 792 TURN_SIGNALS "1=Left, 2=Right";
CM_ SG_ 264 ACCEL_PEDAL "not in ACC so seems to be actual pedal. Use for gasPressed";
CM_ SG_ 544 LKAS_STATE "2 when autopark has control, 8 when is actuatable by lkas, 4 when there is a fault";
CM_ SG_ 658 LKAS_STEERING_TORQUE "Most sent by stock system is 1024+-230. + is left. typically changes by 2 or 3 each 0.01s";
CM_ SG_ 678 LKAS_ICON_COLOR "3 is yellow, 2 is green, 1 is white, 0 is null";
CM_ SG_ 678 LKAS_LANE_LINES "0x01 transparent lines, 0x02 left white, 0x03 right white, 0x04 left yellow with car on top, 0x05 left yellow with car on top, 0x06 both white, 0x07 left yellow, 0x08 left yellow right white, 0x09 right yellow, 0x0a right yellow left white, 0x0b left yellow with car on top right white, 0x0c right yellow with car on top left white, (0x00, 0x0d, 0x0e, 0x0f) null";
CM_ SG_ 678 LKAS_ALERTS "(0x01, 0x02) lane sense off, (0x03, 0x04, 0x06) place hands on steering wheel, 0x07 lane departure detected + place hands on steering wheel, (0x08, 0x09) lane sense unavailable + clean front windshield, 0x0b lane sense and auto high beam unavailable + clean front windshield, 0x0c lane sense unavailable + service required, (0x00, 0x05, 0x0a, 0x0d, 0x0e, 0x0f) null";
CM_ SG_ 705 AUTO_PARK_TOGGLE_1 "set briefly when turning on or off self-parking";
CM_ SG_ 671 AUTO_PARK_CMD "Request Appears to be in NM";
CM_ SG_ 671 AUTO_PARK_STATUS "1 = IDLE / NO REQUEST 9 = START REQUEST 10 = REQUEST MODE 11 = REQUEST MODE";
CM_ SG_ 826 AUTO_PARK_GEAR_1 "Reverse=0, Forward=f";
CM_ SG_ 826 AUTO_PARK_GEAR_2 "Reverse=0, Forward=f";
CM_ SG_ 826 AUTO_PARK_GEAR_3 "Reverse=0, Forward=f";
CM_ SG_ 332 STEER_ANGLE_2 "slightly lags the other steer_angle signal. also more noisy.";
CM_ SG_ 608 PARKSENSE_DISABLED "set if parksense is disabled";
CM_ SG_ 729 LKAS_STATUS_OK "Set to 0x0820 when LKAS system is plugged in.";
CM_ SG_ 825 BEEP_339 "sent every 0.5s. 0050 is no beep. To beep send 4355 or 4155. Used by ParkSense warning.";
CM_ SG_ 270 ELECTRIC_MOTOR "0x7fff indicates electric motor not in use";
CM_ SG_ 291 ENERGY_GAIN_LOSS "unsure what this actually is";
CM_ SG_ 291 ENERGY_SMOOTHER_CURVE "unsure what it is, but smoother";
CM_ SG_ 308 ACCEL_134 "only set when human presses accel pedal";
CM_ SG_ 532 NOISY_SLOWLY_DECREASING "perhaps battery but do not know";
CM_ SG_ 816 TRACTION_OFF "set when traction off button is enabled";
CM_ SG_ 816 TOGGLE_PARKSENSE "sending 3000071ec0ff9000 enables or disables parksense";
CM_ SG_ 324 SPEED_2 "signal is approx half other speeds";
CM_ SG_ 501 ACC_SPEED_CONFIG_KPH "speed configured for ACC";
CM_ SG_ 501 ACC_SPEED_CONFIG_MPH "speed configured for ACC";
CM_ SG_ 501 CRUISE_STATE "may just be an icon, but seems to indicate different cruise modes: ACC and Non-ACC and engaged state for both.";
CM_ SG_ 625 SPEED "zero on non-acc drives";

VAL_ 501 CRUISE_STATE 0 "Off" 1 "CC On" 2 "CC Engaged" 3 "ACC On" 4 "ACC Engaged";
VAL_ 746 PRNDL 5 "L" 4 "D" 3 "N" 2 "R" 1 "P";
VAL_ 792 TURN_SIGNALS 2 "Right" 1 "Left";
