VERSION ""


NS_ :
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: K109_FCM B233B_LRR NEO VIS_FO VIS2_FO K124_ASCM Vector__XXX EOCM_F_FO EOCM2A_IMX6_FO EOCM2A_K2_FO EOCM2A_K1_FO EOCM2B_IMX6_FO EOCM2B_K2_FO EOCM2B_K1_FO
VAL_TABLE_ RangeMode 1 "Active" 0 "Inactive" ;
VAL_TABLE_ TrkConf 3 "Confident" 2 "Speculative" 1 "Highly speculative" 0 "Invalid" ;
VAL_TABLE_ TrkMeasStatus 3 "Measured current cycle" 2 "Latent track not detected" 1 "New object" 0 "No object" ;
VAL_TABLE_ TrkDynProp 4 "Moving in opposite direction" 3 "Moving in same direction" 2 "Has moved but currently stopped" 1 "Has never moved," 0 "Unknown" ;
VAL_TABLE_ FrntVsnInPthVehBrkNwSt 10 "Active" 5 "Inactive" ;
VAL_TABLE_ FrntVsnClostPedBrkNwSt 10 "Active" 5 "Inactive" ;
VAL_TABLE_ LaneSnsLLnPosValid 1 "Invalid" 0 "Valid" ;
VAL_TABLE_ LnSnsRLnPosValid 1 "Invalid" 0 "Valid" ;
VAL_TABLE_ ObjectType 7 "no object present" 6 "fixed roadside object" 5 "fixed overhead object" 4 "pedestrian" 3 "motocycle  / bicycle" 2 "Large vehicle (semi)" 1 "4 Wheel Vehicle (car, small trk)" 0 "Unknown" ;
VAL_TABLE_ FwVsnCinCoutPotT9Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT8Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT7Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT6Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT5Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT4Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT3Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT2Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT1Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT12Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT11Rev 2 "Right" 1 "Left" 0 "None" ;
VAL_TABLE_ FwVsnCinCoutPotT10Rev 2 "Right" 1 "Left" 0 "None" ;


BO_ ********** VECTOR__INDEPENDENT_SIG_MSG: 0 Vector__XXX
 SG_ Always12 : 0|8@0+ (1,0) [0|0] ""  Vector__XXX
 SG_ TimeStatusChecksum : 0|12@0+ (1,0) [0|0] ""  Vector__XXX

BO_ 161 ASCMTimeStatus: 7 NEO
 SG_ TimeStatus : 7|28@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ RollingCounter : 27|2@0+ (1,0) [0|0] ""  B233B_LRR

BO_ 774 ASCMSteeringStatus: 8 NEO
 SG_ ASCMSterringStatusChecksum : 55|16@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ AlwaysF0 : 15|8@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ Always20 : 23|8@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ RollingCounter : 7|2@0+ (1,0) [0|0] ""  B233B_LRR

BO_ 784 ASCMHeadlight: 2 NEO
 SG_ Always42 : 7|8@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ Always4 : 15|8@0+ (1,0) [0|0] ""  B233B_LRR

BO_ 776 ASCMAccSpeedStatus: 7 NEO
 SG_ AccSpeedChecksum : 42|11@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ RollingCounter : 46|2@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ NearRangeMode : 43|1@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ FarRangeMode : 44|1@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ VehicleAcceleration : 19|12@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ VehicleSpeed : 15|12@0+ (1,0) [0|0] ""  B233B_LRR
 SG_ AlwaysOne : 3|1@0+ (1,0) [0|0] ""  B233B_LRR

BO_ 1120 F_LRR_Obj_Header: 8 LRR_FO
 SG_ FLRRRollingCount : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FLRRModeCmdFdbk : 23|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRNumValidTargets : 20|5@0+ (1,0) [0|31] ""  EOCM_F_FO
 SG_ FLRRTimeStampV : 31|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRTimeStamp : 2|11@0+ (1,0) [0|2047] "ms"  EOCM_F_FO
 SG_ FLRRRoadTypeInfo : 5|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FLRRBurstChecksum : 55|16@0+ (1,0) [0|65535] ""  EOCM_F_FO
 SG_ FLRRDiagSpare : 30|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRVltgOutRngLo : 44|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRVltgOutRngHi : 43|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRSvcAlgnInPrcs : 38|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRSnsrBlckd : 45|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRSnstvFltPrsntInt : 24|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRPlntAlgnInProc : 37|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRMsalgnYawRt : 47|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRMsalgnYawLt : 46|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRLonVelPlsblityFlt : 35|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRYawRtPlsblityFlt : 34|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRMsalgnPtchUp : 32|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRMsalgnPtchDn : 33|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRInitDiagCmplt : 40|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRHWFltPrsntInt : 25|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRExtIntrfrnc : 36|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRCANSgnlSpvFld : 29|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRCANRxErr : 28|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRTunlDtctd : 27|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRAmbTmpOutRngLw : 42|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRAmbTmpOutRngHi : 41|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRAntTngFltPrsnt : 26|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FLRRAlgnFltPrsnt : 39|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 1134 LRRObject14: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1132 LRRObject12: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1131 LRRObject11: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1130 LRRObject10: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1129 LRRObject09: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1128 LRRObject08: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1127 LRRObject07: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1126 LRRObject06: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1125 LRRObject05: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1124 LRRObject04: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1123 LRRObject03: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1140 LRRObject20: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1139 LRRObject19: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1138 LRRObject18: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1137 LRRObject17: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1136 LRRObject16: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1135 LRRObject15: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1133 LRRObject13: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1122 LRRObject02: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO

BO_ 1121 LRRObject01: 8 B233B_LRR
 SG_ TrkRange : 5|11@0+ (0.125,0) [0|255.875] "m"  NEO
 SG_ TrkRangeRate : 10|11@0- (0.125,0) [-128|127.875] "m/s"  NEO
 SG_ TrkRangeAccel : 31|9@0- (0.125,0) [-32|31.875] "m/s^2"  NEO
 SG_ TrkAzimuth : 35|12@0- (0.125,0) [-256|255.875] "deg"  NEO
 SG_ TrkWidth : 55|6@0+ (0.25,0) [0|15.75] "m"  NEO
 SG_ TrkObjectID : 61|6@0+ (1,0) [0|63] ""  NEO
 
 BO_ 1094 F_Vision_Obj_Track_12: 8 VIS2_FO
 SG_ FwdVsnObjTypTr12Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnAzmthTrk12Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk12Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FVisionWidthTrk12 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FVisionMeasStatTrk12 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnVertPosTrk12 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FVisionRelLaneTrk12 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk12 : 34|11@0- (0.125,0) [-128|127.875] "deg/sec"  EOCM_F_FO
 SG_ FVisionConfTrk12 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ ObjDirTrk12 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk12 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk12 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1093 F_Vision_Obj_Track_11: 8 VIS2_FO
 SG_ FwdVsnObjTypTr11Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnAzmthTrk11Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk11Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FVisionWidthTrk11 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FVisionMeasStatTrk11 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnVertPosTrk11 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FVisionRelLaneTrk11 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk11 : 34|11@0- (0.125,0) [-128|127.875] "deg/sec"  EOCM_F_FO
 SG_ FVisionConfTrk11 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ ObjDirTrk11 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk11 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk11 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO

BO_ 1100 F_Vision_Obj_Track_12_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT12Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk12 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk12 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk12 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk12 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr12 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk12 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo12 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1099 F_Vision_Obj_Track_11_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT11Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk11 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk11 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk11 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk11 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr11 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk11 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo11 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1098 F_Vision_Obj_Track_10_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT10Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk10 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk10 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk10 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk10 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr10 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk10 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo10 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1097 F_Vision_Obj_Track_9_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT9Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk9 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk9 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk9 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk9 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr9 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk9 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo9 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1096 F_Vision_Obj_Track_8_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT8Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk8 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk8 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk8 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk8 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr8 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk8 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo8 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1095 F_Vision_Obj_Track_7_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT7Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk7 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk7 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk7 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk7 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr7 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk7 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo7 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1068 F_Vision_Obj_Track_6_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT6Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk6 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk6 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk6 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk6 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr6 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk6 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo6 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1067 F_Vision_Obj_Track_5_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT5Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk5 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk5 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk5 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk5 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr5 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk5 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo5 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1066 F_Vision_Obj_Track_4_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT4Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk4 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk4 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk4 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk4 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr4 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk4 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo4 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1065 F_Vision_Obj_Track_3_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT3Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk3 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk3 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk3 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk3 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr3 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk3 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo3 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1064 F_Vision_Obj_Track_2_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT2Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk2 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk2 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk2 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk2 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr2 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk2 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo2 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1063 F_Vision_Obj_Track_1_B: 8 VIS2_FO
 SG_ FwVsnCinCoutPotT1Rev : 5|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnObjSclChgTrk1 : 15|16@0- (0.0002,0) [-6.5536|6.5534] "pix/sec"  EOCM_F_FO
 SG_ FwdVsnObjAgeTrk1 : 62|7@0+ (1,0) [0|127] ""  EOCM_F_FO
 SG_ FwdVsnLongVlctyTrk1 : 42|12@0- (0.0625,0) [-128|127.9375] "m/sec"  EOCM_F_FO
 SG_ FwdVsnLatOfstTrk1 : 36|10@0- (0.125,0) [-64|63.875] "m"  EOCM_F_FO
 SG_ FwdVsnBrkLtStatTrk1 : 38|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FwdVsnTrnSigStatTr1 : 25|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FrtVsnBrstIDAddInfo1 : 7|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1088 F_Vision_Obj_Header_2: 8 VIS2_FO
 SG_ FrntVsnInPthVehBrkNwSt : 35|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FrntVsnClostPedBrkNwSt : 39|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FrntVsnClostPedObjID : 29|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FrntVsnClostPedAlrtNwFlg : 30|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrntVsnClostPedNotftnFlg : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrntVsnInPthVehAlrtNwFlg : 2|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnVldTgtNum2 : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FrtVsnTmStmp2V : 31|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnTmStmp2 : 10|11@0+ (1,0) [0|2047] ""  EOCM_F_FO
 SG_ FrtVsnRollCnt2 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FrtVsnBrstChksum2 : 55|16@0+ (1,0) [0|65535] ""  EOCM_F_FO

BO_ 854 F_Vision_Environment_7: 3 VIS2_FO
 SG_ FwdVsnCnstrctAreaDst : 13|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnCnstrctZnDet : 15|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnEgoVehLnPos : 17|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnRdTypDet : 9|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnTunnlDetd : 23|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ FwdVsnTunnlDst : 21|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBrstID5 : 1|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 853 F_Vision_Environment_6: 8 VIS2_FO
 SG_ LnMrkg4LnSnsLnHdngTngtV : 7|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnHdngTngt : 23|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnDstV : 56|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnDst : 15|8@0- (0.1,0) [-12.8|12.7] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnCrvtV : 6|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnCrvtGradV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnCrvtGrad : 47|16@0- (5.96e-8,0) [-0.0019529728|0.0019529132] "1/(m*sec)"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnSnsLnCrvt : 31|16@0- (9.53e-7,0) [-0.031227904|0.031226951] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnQltyConfLvl : 63|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnMrkrTyp : 4|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBrstID4 : 1|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 852 F_Vision_Environment_5: 8 VIS2_FO
 SG_ LnMrkg3LnSnsLnHdngTngtV : 7|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnHdngTngt : 23|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnDstV : 56|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnDst : 15|8@0- (0.1,0) [-12.8|12.7] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnCrvtV : 6|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnCrvtGradV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnCrvtGrad : 47|16@0- (5.96e-8,0) [-0.0019529728|0.0019529132] "1/(m*sec)"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnSnsLnCrvt : 31|16@0- (9.53e-7,0) [-0.031227904|0.031226951] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnQltyConfLvl : 63|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnMrkrTyp : 4|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBrstID3 : 1|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 309 LHT_CameraObjConfirmation_FO: 1 VIS_FO
 SG_ HiBmRecmnd : 1|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ CtLghtDet : 0|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 848 F_Vision_Environment: 8 VIS_FO
 SG_ FwdVsnEnvIllum : 37|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTngtOfHdngLnRtV : 1|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTngtOfHdngLnRt : 31|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnChngStatus : 39|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBurstChecksum : 55|16@0+ (1,0) [0|65535] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseRollingCount : 7|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseSystemOK : 4|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSnsLLnPosValid : 2|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSenseDistToLLnEdge : 14|7@0+ (0.05,0) [0|6.35] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsRLnPosValid : 0|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsDistToRLnEdge : 22|7@0+ (0.05,0) [0|6.35] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseTimeStampV : 5|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseTimeStamp : 34|11@0+ (1,0) [0|2047] "ms"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LaneSenseSystemOKV : 3|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 849 F_Vision_Environment_2: 8 VIS_FO
 SG_ LnSnsLatVRelToRgtMrkg : 23|8@0- (0.02,0) [-2.56|2.54] "m/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM_F_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO
 SG_ LnSnsRtLnMrkgTypChgDst : 61|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsCrvtGrdntRtV : 63|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnMrkgWdthRt : 62|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsRtAnchrLn : 57|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLtAnchrLn : 56|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnCrvtrRghtV : 0|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnCrvtrRght : 47|16@0- (9.53e-7,0) [-0.031227904|0.031226951] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsCrvtGrdntRt : 31|16@0- (5.96e-8,0) [-0.0019529728|0.0019529132] "1/rad/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBurstID : 2|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLatVRelToLftMrkg : 15|8@0- (0.02,0) [-2.56|2.54] "m/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

BO_ 1056 F_Vision_Obj_Header: 6 VIS_FO
 SG_ FVsnSnsrBlckd : 24|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ ClstInPathVehObjID : 30|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FrtVsnFld : 6|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnIniDiagSuccCmpt : 5|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnSrvAlgnInPrcs : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FrtVsnUnvlbl : 7|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisionRollingCnt : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVISModeCmdFdbk : 4|3@0+ (1,0) [0|7] ""  EOCM_F_FO
 SG_ FVisionNumValidTrgts : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FVisionTimeStampV : 31|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisionTimeStamp : 10|11@0+ (1,0) [0|2047] "ms"  EOCM_F_FO
 SG_ VISBurstChecksum : 39|16@0+ (1,0) [0|65535] ""  EOCM_F_FO

BO_ 1057 F_Vision_Obj_Track_1: 8 VIS_FO
 SG_ FwdVsnRngTrk1Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk1Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr1Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnVertPosTrk1 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FVisBurstIDTrk1 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk1 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk1 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk1 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk1 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk1 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk1 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ ObjDirTrk1 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 1058 F_Vision_Obj_Track_2: 8 VIS_FO
 SG_ FwdVsnVertPosTrk2 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk2Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk2Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ ObjDirTrk2 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FwdVsnObjTypTr2Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk2 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk2 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk2 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk2 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk2 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk2 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk2 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1059 F_Vision_Obj_Track_3: 8 VIS_FO
 SG_ FwdVsnVertPosTrk3 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk3Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk3Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr3Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk3 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk3 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk3 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk3 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk3 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk3 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk3 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk3 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1060 F_Vision_Obj_Track_4: 8 VIS_FO
 SG_ FwdVsnVertPosTrk4 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FVisionMeasStatTrk4 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk4 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FwdVsnRngTrk4Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk4Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr4Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk4 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk4 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ ObjDirTrk4 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisionConfTrk4 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk4 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk4 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO

BO_ 1061 F_Vision_Obj_Track_5: 8 VIS_FO
 SG_ FwdVsnVertPosTrk5 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk5Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk5Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr5Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk5 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk5 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk5 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk5 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk5 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk5 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk5 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk5 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1062 F_Vision_Obj_Track_6: 8 VIS_FO
 SG_ FwdVsnVertPosTrk6 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk6Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk6Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr6Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk6 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk6 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk6 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk6 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk6 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk6 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk6 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk6 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1089 F_Vision_Obj_Track_7: 8 VIS2_FO
 SG_ FVisBurstIDTrk7 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk7 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk7 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk7 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk7 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk7 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk7 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO
 SG_ FwdVsnRngTrk7Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnObjTypTr7Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnAzmthTrk7Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnVertPosTrk7 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ ObjDirTrk7 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO

BO_ 1090 F_Vision_Obj_Track_8: 8 VIS2_FO
 SG_ FVisBurstIDTrk8 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk8 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FwdVsnAzmthTrk8Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnVertPosTrk8 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk8Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnObjTypTr8Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk8 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisionConfTrk8 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk8 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk8 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk8 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk8 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1091 F_Vision_Obj_Track_9: 8 VIS2_FO
 SG_ FwdVsnVertPosTrk9 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ FwdVsnRngTrk9Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk9Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr9Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ ObjDirTrk9 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk9 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk9 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk9 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk9 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk9 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk9 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk9 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 1092 F_Vision_Obj_Track_10: 8 VIS2_FO
 SG_ FwdVsnRngTrk10Rev : 16|12@0+ (0.1,0) [0|409.5] "m"  EOCM_F_FO
 SG_ FwdVsnAzmthTrk10Rev : 10|10@0- (0.1,0) [-51.2|51.1] "deg"  EOCM_F_FO
 SG_ FwdVsnObjTypTr10Rev : 14|4@0+ (1,0) [0|15] ""  EOCM_F_FO
 SG_ FwdVsnVertPosTrk10 : 53|6@0+ (0.25,-2) [-2|13.75] "deg"  EOCM_F_FO
 SG_ ObjDirTrk10 : 15|1@0+ (1,0) [0|1] ""  EOCM_F_FO
 SG_ FVisBurstIDTrk10 : 1|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionObjectIDTrk10 : 7|6@0+ (1,0) [0|63] ""  EOCM_F_FO
 SG_ FVisionConfTrk10 : 36|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionAzRateTrk10 : 34|11@0- (0.125,0) [-128|127.875] "deg/s"  EOCM_F_FO
 SG_ FVisionRelLaneTrk10 : 55|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionMeasStatTrk10 : 63|2@0+ (1,0) [0|3] ""  EOCM_F_FO
 SG_ FVisionWidthTrk10 : 61|6@0+ (0.25,0) [0|15.75] "m"  EOCM_F_FO

BO_ 851 F_Vision_Environment_4: 8 VIS_FO
 SG_ LnMrkg3LnPrvwDst : 45|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTtlNmLnMrkgDetRt : 4|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsRtLinCrsTm : 25|5@0+ (0.1,0) [0|3.1] "s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsNumPrlLnsDetRt : 33|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsNumPrlLnsDetLt : 36|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsCrvtGrdntLftV : 31|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLtLinCrsTm : 30|5@0+ (0.1,0) [0|3.1] "s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnPrvwDst : 50|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnMrkgTypChgDst : 61|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnMrkgTypChgDst : 40|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnMrkgWdth : 62|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4LnMarkrElvtd : 51|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg4AnchrLnLin : 57|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnMrkgWdth : 41|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3LnMarkrElvtd : 46|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkg3AnchrLnLin : 52|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBurstID2 : 1|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsCrvtGrdntLft : 15|16@0- (5.96e-8,0) [-0.0019529728|0.0019529132] "1/rad/s"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO

 BO_ 850 F_Vision_Environment_3: 8 VIS_FO
 SG_ LnSnsTtlNmLnMrkgDetLt : 58|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLtLnMrkgWdth : 63|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLtLnMrkgTypChgDst : 62|4@0+ (10,0) [0|150] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTngtOfHdngLnLftV : 23|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsTngtOfHdngLnLft : 31|8@0- (0.002,0) [-0.256|0.254] "m/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnCrvtrLftV : 15|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsLnCrvtrLft : 39|16@0- (9.53e-7,0) [-0.031227904|0.031226951] "1/m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkrTypRght : 50|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkrTypLft : 53|3@0+ (1,0) [0|7] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkrElvtdRght : 54|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnMrkrElvtdLft : 55|1@0+ (1,0) [0|1] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnSnsBurstID1 : 7|2@0+ (1,0) [0|3] ""  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnQltyCnfdncLvlRght : 22|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnQltyCnfdncLvlLft : 14|7@0+ (0.7874016,0) [0|100.0000032] "%"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnPrvwDstncRght : 2|3@0+ (10,0) [0|70] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO
 SG_ LnPrvwDstncLft : 5|3@0+ (10,0) [0|70] "m"  EOCM2A_IMX6_FO,EOCM2A_K2_FO,EOCM2A_K1_FO,EOCM2B_IMX6_FO,EOCM2B_K2_FO,EOCM2B_K1_FO,EOCM_F_FO


BO_TX_BU_ 161 : K124_ASCM,NEO;
BO_TX_BU_ 774 : K124_ASCM,NEO;
BO_TX_BU_ 784 : K124_ASCM,NEO;
BO_TX_BU_ 776 : K124_ASCM,NEO;


CM_ BU_ K109_FCM "Frontview Camera Module";
CM_ BU_ B233B_LRR "Radar Sensor Module Long Range";
CM_ BU_ NEO "Comma NEO";
CM_ BU_ VIS_FO "Front Camera Data";
CM_ BU_ VIS2_FO "Front Camera Data2";
CM_ BU_ K124_ASCM "Active Safety Control Module";
CM_ BO_ ********** "This is a message for not used signals, created by Vector CANdb++ DBC OLE DB Provider.";
BA_DEF_  "UseGMParameterIDs" INT 0 0;
BA_DEF_  "ProtocolType" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_DEF_  "UseGMParameterIDs" 1;
BA_DEF_DEF_  "ProtocolType" "GMLAN";
BA_DEF_DEF_  "BusType" "";
BA_ "BusType" "CAN";
BA_ "ProtocolType" "GMLAN";
BA_ "UseGMParameterIDs" 0;
VAL_ 776 NearRangeMode 1 "Active" 0 "Inactive";
VAL_ 776 FarRangeMode 1 "Active" 0 "Inactive";
