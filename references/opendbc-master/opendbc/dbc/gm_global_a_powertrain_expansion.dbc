VERSION ""


NS_ :
    NS_DESC_
    CM_
    BA_DEF_
    BA_
    VAL_
    CAT_DEF_
    CAT_
    FILTER
    BA_DEF_DEF_
    EV_DATA_
    ENVVAR_DATA_
    SGTYPE_
    SGTYPE_VAL_
    BA_DEF_SGTYPE_
    BA_SGTYPE_
    SIG_TYPE_REF_
    VAL_TABLE_
    SIG_GROUP_
    SIG_VALTYPE_
    SIGTYPE_VALTYPE_
    BO_TX_BU_
    BA_DEF_REL_
    BA_REL_
    BA_DEF_DEF_REL_
    BU_SG_REL_
    BU_EV_REL_
    BU_BO_REL_
    SG_MUL_VAL_

BS_:
BU_: K1_APM
BO_ 470 APM_Stats: 7 K1_APM
   SG_ APM_Low_Voltage_Sensed : 16|8@1+ (0.0787402,0) [0|0] "V" Vector__XXX
   SG_ APM_Temperature_1 : 24|8@1+ (1,-40) [0|0] "C" Vector__XXX
   SG_ APM_Temperature_2 : 32|8@1+ (1,-40) [0|0] "C" Vector__XXX
   SG_ APM_Low_Voltage_Current_Output : 40|8@1- (1,0) [0|0] "A" Vector__XXX
   SG_ APM_Status : 7|8@0+ (1,0) [0|0] "" Vector__XXX
   SG_ APM_High_Voltage_Input_Current : 8|8@1- (0.15,-7) [0|0] "A" Vector__XXX
   SG_ APM_Counter : 48|8@1+ (1,0) [0|0] "" Vector__XXX

BO_ 468 APM_Command: 2 K1_APM
   SG_ APM_Status : 0|8@1+ (1,0) [0|0] "" Vector__XXX
   SG_ APM_Voltage_Requested : 8|8@1+ (0.0787402,0) [0|0] "V" Vector__XXX

BA_DEF_ BO_ "GenMsgBackgroundColor" STRING ;
BA_DEF_ BO_ "GenMsgForegroundColor" STRING ;
BA_DEF_ BO_ "isj1939dbc" INT 0 0;
BA_DEF_DEF_ "GenMsgBackgroundColor" "#ffffff";
BA_DEF_DEF_ "GenMsgForegroundColor" "#000000";
BA_DEF_DEF_ "isj1939dbc" 0;
CM_ BU_ K1_APM "14V Power Module";
VAL_ 468 APM_Status 0 "Off" 160 "On";
