VERSION ""


NS_ :
    NS_DESC_
    CM_
    BA_DEF_
    BA_
    VAL_
    CAT_DEF_
    CAT_
    FILTER
    BA_DEF_DEF_
    EV_DATA_
    ENVVAR_DATA_
    SGTYPE_
    SGTYPE_VAL_
    BA_DEF_SGTYPE_
    BA_SGTYPE_
    SIG_TYPE_REF_
    VAL_TABLE_
    SIG_GROUP_
    SIG_VALTYPE_
    SIGTYPE_VALTYPE_
    BO_TX_BU_
    BA_DEF_REL_
    BA_REL_
    BA_DEF_DEF_REL_
    BU_SG_REL_
    BU_EV_REL_
    BU_BO_REL_
    SG_MUL_VAL_

BS_:

BU_: XXX


BO_ 3 STEER_SENSOR: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ STEER_RATE : 19|12@0- (0.5,0) [0|255] "" XXX
 SG_ STEER_DIRECTION : 4|1@0+ (1,2) [0|1] "" XXX
 SG_ STEER_ANGLE : 3|12@0- (-0.5,0) [-500|500] "degrees" XXX

BO_ 5 BRAKE_MODULE: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ BRAKE_HOLD : 2|1@0+ (1,0) [0|1] "" XXX
 SG_ BRAKE_POSITION : 17|10@0+ (1,0) [0|65535] "" XXX
 SG_ DRIVER_BRAKE : 4|1@0+ (1,0) [0|1] "" XXX
 SG_ COMPUTER_BRAKE : 10|1@0+ (1,0) [0|1] "" XXX
 SG_ BRAKE_PRESSED : 0|1@1+ (1,0) [0|1] "" XXX

BO_ 69 DRIVER_CONTROLS: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ STEERING_WHEEL_BUTTONS : 32|16@1+ (1,0) [0|255] "4 directional, 2 volume control & 2 phone buttons" XXX
 SG_ LEFT_BLINKER : 16|1@0+ (1,0) [0|1] "" XXX
 SG_ RIGHT_BLINKER : 17|1@0+ (1,0) [0|1] "" XXX
 SG_ HIGHBEAM_TOGGLE : 18|1@0+ (1,0) [0|1] "" XXX
 SG_ HIGHBEAM_MOMENTARY : 19|1@0+ (1,0) [0|1] "" XXX
 SG_ CRUISE_CONTROL_CANCEL : 0|1@0+ (1,0) [0|1] "" XXX
 SG_ CRUISE_CONTROL_RESUME : 1|1@0+ (1,0) [0|1] "" XXX
 SG_ CRUISE_CONTROL_ACCEL_HIGH : 2|1@0+ (1,0) [0|1] "" XXX
 SG_ CRUISE_CONTROL_DECEL_HIGH : 3|1@0+ (1,0) [0|1] "" XXX
 SG_ CRUISE_CONTROL_ACCEL_LOW : 4|1@0+ (1,0) [0|1] "" XXX
 SG_ CRUISE_CONTROL_DECEL_LOW : 5|1@0+ (1,0) [0|1] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_XFF : 15|8@0+ (1,0) [0|255] "" XXX

BO_ 513 WHEEL_ENCODERS: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ WHEEL_ENCODER_2 : 15|8@0+ (1,0) [0|255] "" XXX
 SG_ WHEEL_ENCODER_3 : 23|8@0+ (1,0) [0|255] "" XXX
 SG_ WHEEL_ENCODER_4 : 31|8@0+ (1,0) [0|255] "" XXX
 SG_ CHECKSUM : 56|8@1+ (1,0) [0|255] "" XXX
 SG_ WHEEL_ENCODER_1 : 7|8@0+ (1,0) [0|255] "" XXX

BO_ 261 GAS_PEDAL: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ ENGINE_RPM : 4|5@0+ (1,0) [0|255] "" XXX
 SG_ GAS_PEDAL : 39|8@0+ (1,0) [0|255] "" XXX
 SG_ COMBINED_GAS : 31|8@0+ (1,0) [0|255] "" XXX

BO_ 643 DOOR_SENSORS: 8 XXX
 SG_ BRAKE_PRESSED : 27|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_OPEN_FR : 3|1@1+ (1,0) [0|3] "" XXX
 SG_ DOOR_OPEN_RL : 5|1@0+ (1,0) [0|3] "" XXX
 SG_ DOOR_OPEN_RR : 7|1@0+ (1,0) [0|3] "" XXX
 SG_ DOOR_OPEN_FL : 1|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_CLOSED_FL : 0|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_CLOSED_FR : 2|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_CLOSED_RL : 4|1@0+ (1,0) [0|1] "" XXX
 SG_ DOOR_CLOSED_RR : 6|1@0+ (1,0) [0|1] "" XXX

BO_ 885 SEATBELT_SENSORS: 8 XXX
 SG_ SEATBELT_DRIVER_LATCHED : 16|1@0+ (1,0) [0|1] "" XXX
 SG_ SEATBELT_PASSENGER_LATCHED : 18|1@0+ (1,0) [0|1] "" XXX

BO_ 257 CRUISE_CONTROL: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_1 : 6|1@0+ (1,0) [0|255] "" XXX
 SG_ CRUISE_DISABLED : 23|1@0+ (1,0) [0|1] "" XXX
 SG_ SET_ME_X002 : 39|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_X00 : 31|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_X003 : 47|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_1 : 5|1@0+ (1,0) [0|1] "" XXX
 SG_ CRUISE_ACCELERATING : 22|1@0+ (1,0) [0|1] "" XXX
 SG_ LONGITUDINAL_ACCEL_REQUEST : 15|8@0- (1,0) [0|127] "" XXX

BO_ 260 CRUISE_CONTROL2: 8 XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_X00 : 47|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_XFF : 31|8@0+ (1,0) [0|65535] "" XXX
 SG_ SET_ME_X02 : 23|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_XFF2 : 39|8@0+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_1 : 7|4@0+ (1,0) [0|255] "" XXX

BO_ 14 STEER_TORQUE: 8 XXX
 SG_ STEER_TORQUE : 15|8@0+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 55|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 63|8@0+ (1,0) [0|255] "" XXX

BO_ 888 CRUISE_CONTROL3: 8 XXX
 SG_ NEW_SIGNAL_2 : 7|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_1 : 38|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_5 : 32|1@0+ (1,0) [0|1] "" XXX
 SG_ CRUISE_DISABLED : 36|1@0+ (1,0) [0|1] "" XXX
 SG_ CRUISE_ENABLED : 34|1@0+ (1,0) [0|1] "" XXX
 SG_ SET_ME_X003 : 47|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_X004 : 63|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_X002 : 31|8@0+ (1,0) [0|255] "" XXX
 SG_ SET_ME_X00 : 23|8@0+ (1,0) [0|255] "" XXX
 SG_ CRUISE_SET_SPEED : 15|8@0+ (1,0) [0|63] "mph" XXX
 SG_ CRUISE_SPEED_CHANGE : 55|1@0+ (1,0) [0|1] "" XXX

BO_ 307 POWER_SEATS: 8 XXX
 SG_ DRIVER_FORWARD : 0|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVER_BACK : 1|1@0+ (1,0) [0|1] "" XXX

BO_ 109 GEAR_LEVER: 8 XXX
 SG_ PARK_BUTTON : 12|1@0+ (1,0) [0|1] "" XXX
 SG_ NEUTRAL_UP : 9|1@0+ (1,0) [0|1] "" XXX
 SG_ NEUTRAL_DOWN : 10|1@0+ (1,0) [0|1] "" XXX
 SG_ DRIVE : 11|1@0+ (1,0) [0|1] "" XXX
 SG_ REVERSE : 8|1@0+ (1,0) [0|1] "" XXX
 SG_ COUNTER : 23|4@0+ (1,0) [0|15] "" XXX
 SG_ CHECKSUM : 31|8@0+ (1,0) [0|255] "" XXX

BO_ 115 GEAR_PACKET: 8 XXX
 SG_ GEAR : 0|4@1+ (1,0) [0|15] "" XXX

BO_ 581 IGNITION: 8 XXX

BO_ 515 WHEEL_SPEEDS: 8 XXX
 SG_ WHEEL_MOVING_FR : 22|1@1+ (1,0) [0|15] "" XXX
 SG_ WHEEL_MOVING_RL : 38|1@0+ (1,0) [0|1] "" XXX
 SG_ WHEEL_MOVING_FL : 6|1@0+ (1,0) [0|1] "" XXX
 SG_ WHEEL_MOVING_RR : 54|1@0+ (1,0) [0|1] "" XXX
 SG_ WHEEL_SPEED_FL : 2|11@0+ (0.0375,0) [0|255] "mph" XXX
 SG_ WHEEL_SPEED_FR : 18|11@0+ (0.0375,0) [0|255] "mph" XXX
 SG_ WHEEL_SPEED_RL : 34|11@0+ (0.0375,0) [0|255] "mph" XXX
 SG_ WHEEL_SPEED_RR : 50|11@0+ (0.0375,0) [0|255] "mph" XXX




CM_ SG_ 3 STEER_DIRECTION "0 = left, 1 = right";
CM_ SG_ 5 BRAKE_POSITION "computer and driver";
CM_ SG_ 5 BRAKE_PRESSED "computer and driver";
CM_ SG_ 261 GAS_PEDAL "user gas input";
CM_ SG_ 261 COMBINED_GAS "computer and driver gas";
CM_ SG_ 257 CRUISE_ACCELERATING "";