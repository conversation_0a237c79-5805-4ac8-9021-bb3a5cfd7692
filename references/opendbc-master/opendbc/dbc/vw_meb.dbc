VERSION ""


NS_ :
    NS_DESC_
    CM_
    BA_DEF_
    BA_
    VAL_
    CAT_DEF_
    CAT_
    FILTER
    BA_DEF_DEF_
    EV_DATA_
    ENVVAR_DATA_
    SGTYPE_
    SGTYPE_VAL_
    BA_DEF_SGTYPE_
    BA_SGTYPE_
    SIG_TYPE_REF_
    VAL_TABLE_
    SIG_GROUP_
    SIG_VALTYPE_
    SIGTYPE_VALTYPE_
    BO_TX_BU_
    BA_DEF_REL_
    BA_REL_
    BA_DEF_DEF_REL_
    BU_SG_REL_
    BU_EV_REL_
    BU_BO_REL_
    SG_MUL_VAL_

BS_:

BU_: BAP_Tester BedienDisp_vo BedienSG_hi CGS DDA Gateway Gateway_PAG GurtMikrofon OTA_FC ZR_High ZR_LIMU ZR_MIB_TOP_ab_Gen3 ZR_Standard


BO_ 64 Airbag_01: 8 Gateway
 SG_ Airbag_01_CRC : 0|8@1+ (1,0) [0|255] "" <PERSON><PERSON>,BMC_MLBevo,BMS_NV,DCDC_800V_PAG,DCDC_HV,DCDC_HV_02,DCDC_IHEV,FCU_MLBevo_FCEV,Ladegeraet_2,Ladegeraet_Konzern,Sub_Gateway
 SG_ Airbag_01_BZ : 8|4@1+ (1,0) [0|15] "" AWC,BMC_MLBevo,BMS_NV,DCDC_800V_PAG,DCDC_HV,DCDC_HV_02,DCDC_IHEV,FCU_MLBevo_FCEV,Ladegeraet_2,Ladegeraet_Konzern,Sub_Gateway
 SG_ AB_RGS_Anst : 12|4@1+ (1,0) [0|15] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Front_Crash : 16|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Heck_Crash : 17|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_SF_Crash : 18|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_SB_Crash : 19|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Rollover_Crash : 20|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Crash_Int : 21|3@1+ (1,0) [0|7] "" FCU_MLBevo_FCEV,Sub_Gateway,TME
 SG_ AB_Lampe : 24|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Deaktiviert : 25|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_VB_deaktiviert : 26|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Systemfehler : 27|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Diagnose : 28|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Stellgliedtest : 29|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway,TME
 SG_ AB_Erh_Auf_VB : 30|2@1+ (1,0) [0|3] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Gurtwarn_VF : 32|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Gurtwarn_VB : 33|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Anzeige_Fussg : 34|2@1+ (1,0) [0|3] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Texte_AKS : 36|2@1+ (1,0) [0|3] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_MKB_gueltig : 39|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_MKB_Anforderung : 40|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Versorgungsspannung : 41|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Deaktivierung_HV : 42|3@1+ (1,0) [0|7] "" AWC,BMC_MLBevo,BMS_NV,DCDC_800V_PAG,DCDC_HV,DCDC_HV_02,DCDC_IHEV,FCU_MLBevo_FCEV,Ladegeraet_2,Ladegeraet_Konzern,Sub_Gateway,TME
 SG_ AB_EDR_Trigger : 45|2@1+ (1,0) [0|3] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ AB_Belegung_VF : 47|2@1+ (1,0) [0|3] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ SC_Masterzeit_Offset : 53|2@1+ (5.08,0) [0|15.24] "Unit_Secon" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ SC_LowSpeedCrashErkannt : 55|2@1+ (1,0) [0|3] "" FCU_MLBevo_FCEV,Sub_Gateway
 SG_ SC_Masterzeit : 57|7@1+ (0.04,0) [0|5.04] "Unit_Secon" FCU_MLBevo_FCEV,Sub_Gateway

BO_ 134 LWI_01: 8 Gateway
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ LWI_Sensorstatus : 12|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ LWI_QBit_Sub_Daten : 13|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LWI_MFL_Abschaltung : 14|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LWI_QBit_Lenkradwinkel : 15|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ LWI_Lenkradwinkel : 16|13@1+ (0.0843,0) [0|800] "Unit_DegreOfArc" OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ LWI_VZ_Lenkradwinkel : 29|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ LWI_VZ_Lenkradw_Geschw : 30|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LWI_Lenkradw_Geschw : 31|9@1+ (5,0) [0|2500] "Unit_DegreOfArcPerSecon" OTA_FC,ZR_High
 SG_ LWI_Sub_Daten : 40|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 159 LH_EPS_03: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ EPS_DSR_Status : 12|4@1+ (1,0) [0|15] "" XXX
 SG_ EPS_Berechneter_LW : 16|12@1+ (0.15,0) [0|613.95] "Unit_DegreOfArc" XXX
 SG_ EPS_BLW_QBit : 30|1@1+ (1,0) [0|1] "" XXX
 SG_ EPS_VZ_BLW : 31|1@1+ (1,0) [0|1] "" XXX
 SG_ EPS_HCA_Status : 32|4@1+ (1,0) [0|15] "" XXX
 SG_ EPS_Lenkmoment : 40|10@1+ (1,0) [0|8] "Unit_centiNewtoMeter" XXX
 SG_ EPS_Lenkmoment_QBit : 54|1@1+ (1,0) [0|1] "" XXX
 SG_ EPS_VZ_Lenkmoment : 55|1@1+ (1,0) [0|1] "" XXX
 SG_ EPS_Lenkungstyp : 60|4@1+ (1,0) [0|15] "" XXX

BO_ 167 Motor_11: 8 Motor_Diesel_MQB
 SG_ Motor_11_CRC : 0|8@1+ (1,0) [0|255] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ Motor_11_BZ : 8|4@1+ (1,0) [0|15] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Soll_Roh : 12|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Ist_Summe : 22|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,SAK_MQB
 SG_ MO_Mom_Traegheit_Summe : 32|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Soll_gefiltert : 42|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Schub : 52|9@1+ (1,-509) [-509|0] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Status_Normalbetrieb_01 : 61|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_erste_Ungenauschwelle : 62|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_QBit_Motormomente : 63|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB

BO_ 168 Motor_12: 8 Motor_Diesel_MQB
 SG_ Motor_12_CRC : 0|8@1+ (1,0) [0|255] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ Motor_12_BZ : 8|4@1+ (1,0) [0|15] "" BMS_MQB,Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ MO_Mom_neg_verfuegbar : 12|9@1+ (1,-509) [-509|0] "Unit_NewtoMeter" Gateway_MQB
 SG_ MO_Mom_Begr_stat : 21|9@1+ (1,0) [0|509] "Unit_NewtoMeter" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Begr_dyn : 30|10@1+ (1,-509) [-509|509] "Unit_NewtoMeter" Gateway_MQB
 SG_ MO_Momentenintegral_02 : 40|7@1+ (1,0) [0|100] "Unit_PerCent" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_QBit_Drehzahl_01 : 47|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ MO_Drehzahl_01 : 48|16@1+ (0.25,0) [0|16383] "Unit_MinutInver" BMS_MQB,Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,SAK_MQB

BO_ 173 Getriebe_11: 8 Gateway
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ GE_MMom_Soll_02 : 12|10@1+ (1,-509) [-509|509] "" Vector__XXX
 SG_ GE_MMom_Vorhalt_02 : 22|10@1+ (1,-509) [-509|509] "" Vector__XXX
 SG_ GE_Uefkt : 32|10@1+ (0.1,0) [0|102.2] "" Vector__XXX
 SG_ GE_Fahrstufe : 42|4@1+ (1,0) [0|15] "" DDA,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ GE_reserv_Fahrstufe : 46|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GE_Schaltablauf : 47|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ GE_Uefkt_unplausibel : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GE_MMom_Status_02 : 50|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ GE_Status_Kraftschluss : 53|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ GE_MMom_Status : 56|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ GE_Freig_MMom_Vorhalt : 58|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GE_Verbot_Ausblendung : 59|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GE_Zielgang : 60|4@1+ (1,0) [0|15] "" OTA_FC,ZR_High

BO_ 184 EM1_HYB_13: 8 Gateway
 SG_ EM1_Freigabe_Info_WFS : 12|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ EM1_Sperr_Info_WFS : 13|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ EM1_AR_aktiv : 14|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ EM1_Eta_Sys : 15|9@1+ (0.2,0) [0|101.8] "Unit_PerCent" Sub_Gateway
 SG_ EM1_IstStrom : 24|11@1+ (1,-1023) [-1023|1022] "Unit_Amper" Sub_Gateway
 SG_ EM1_Fehler_ElAntriebFreilauf_Anf : 35|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ EM1_Abregelung_Temperatur : 36|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ EM1_AnlernenElMotor_Anf : 43|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ EM1_Moment_HVVerbraucher : 44|10@1+ (1,-511) [-511|511] "Unit_NewtoMeter" Sub_Gateway
 SG_ EM1_Freigabe_Verfallsinfo_WFS : 55|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ EM1_Parken_WFS_Status : 56|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ EM1_HV_betriebsbereit : 58|1@1+ (1,0) [0|1] "" Sub_Gateway

BO_ 190 MEB_HVEM_01: 48 XXX
 SG_ CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ CNT : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Engine_RPM_Max : 12|14@1+ (2,-9658) [0|15] "RPM" XXX
 SG_ Engine_RPM_Min : 26|14@1+ (2,-10300) [0|63] "RPM" XXX
 SG_ In_Motion_04 : 48|3@1+ (1,0) [0|7] "" XXX
 SG_ In_Motion_03 : 52|1@0+ (1,0) [0|1] "" XXX
 SG_ In_Motion_02 : 54|1@0+ (1,0) [0|1] "" XXX
 SG_ Engine_Power : 56|12@1+ (0.5,-1023) [0|255] "kW" XXX
 SG_ In_Motion : 68|1@1+ (1,0) [0|3] "" XXX
 SG_ Standstill : 71|1@0+ (1,0) [0|1] "" XXX
 SG_ Unknown_04 : 72|10@1+ (1,0) [0|255] "" XXX
 SG_ Battery_Voltage : 86|12@1+ (0.2,0) [0|3] "Volt" XXX
 SG_ Unknown_01 : 100|9@1+ (1,0) [0|7] "" XXX
 SG_ Battery_Voltage_02 : 113|11@1+ (0.24,0) [0|127] "Volt" XXX
 SG_ Engine_Status : 296|2@1+ (1,0) [0|3] "" XXX
 SG_ Inactive : 300|1@0+ (1,0) [0|1] "" XXX
 SG_ Inactive_02 : 303|1@0+ (1,0) [0|1] "" XXX

BO_ 192 EM1_01: 32 XXX
 SG_ Schubbetrieb : 79|1@0+ (1,0) [0|1] "" XXX

BO_ 219 AWV_03: 48 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ FCW_Active : 64|1@0+ (1,0) [0|1] "" XXX
 SG_ Pre_Brake_Fill : 76|1@0+ (1,0) [0|1] "" XXX

BO_ 247 MEB_HVEM_02: 8 XXX
 SG_ CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ CNT : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_3 : 44|10@1+ (1,0) [0|7] "" XXX
 SG_ NEW_SIGNAL_2 : 54|7@1+ (1,0) [0|3] "" XXX

BO_ 252 ESC_51: 48 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ AEB_Breaking_01 : 24|8@1+ (1,0) [0|255] "" XXX
 SG_ AEB_Breaking_02 : 32|8@1+ (1,0) [0|255] "" XXX
 SG_ Accelerator_Higher_Speed : 40|1@0+ (1,0) [0|1] "" XXX
 SG_ Brake_Pressure : 42|9@1+ (0.195,0) [0|100] "Unit_Percent" XXX
 SG_ HL_Radgeschw : 64|16@1+ (0.0075,0) [0|491.5125] "Unit_KilometerPerHour" XXX
 SG_ HR_Radgeschw : 80|16@1+ (0.0075,0) [0|491.5125] "Unit_KilometerPerHour" XXX
 SG_ VL_Radgeschw : 96|16@1+ (0.0075,0) [0|491.5125] "Unit_KilometerPerHour" XXX
 SG_ VR_Radgeschw : 112|16@1+ (0.0075,0) [0|491.5125] "Unit_KilometerPerHour" XXX
 SG_ HL_Brake_Pressure : 152|8@1+ (1,0) [0|100] "" XXX
 SG_ HR_Brake_Pressure : 160|8@1+ (1,0) [0|100] "" XXX
 SG_ VL_Brake_Pressure : 168|8@1+ (1,0) [0|100] "" XXX
 SG_ VR_Brake_Pressure : 176|8@1+ (1,0) [0|100] "" XXX
 SG_ Steering_Wheel_CW : 184|8@1+ (1.67,0) [0|255] "" XXX
 SG_ Steering_Wheel_CCW : 192|8@1+ (1.67,0) [0|255] "" XXX

BO_ 253 ESP_21: 8 Gateway
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ BR_Eingriffsmoment : 12|10@1+ (1,-509) [-509|509] "" Vector__XXX
 SG_ ESP_Diagnose : 23|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ESC_v_Signal_Qualifier_High_Low : 24|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ ESP_Vorsteuerung : 28|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ OBD_Schlechtweg : 30|1@1+ (1,0) [0|1] "" ZR_High
 SG_ OBD_QBit_Schlechtweg : 31|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ESP_v_Signal : 32|16@1+ (0.01,0) [0|655.32] "Unit_KiloMeterPerHour" BedienSG_hi,DDA,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ASR_Tastung_passiv : 48|1@1+ (1,0) [0|1] "" OTA_FC
 SG_ ESP_Tastung_passiv : 49|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ ESP_Systemstatus : 50|1@1+ (1,0) [0|1] "" OTA_FC
 SG_ ASR_Schalteingriff : 51|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ ESP_QBit_v_Signal : 55|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ABS_Bremsung : 56|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ ASR_Anf : 57|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MSR_Anf : 58|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ EBV_Eingriff : 59|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EDS_Eingriff : 60|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Eingriff : 61|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ ESP_ASP : 62|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ESC_Neutralschaltung : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 258 ESC_50: 48 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Lateral_Accel : 16|8@1+ (0.15,-18.9) [0|255] "Unit_MeterPerSquareSecond" XXX
 SG_ Longitudinal_Accel : 24|10@1+ (0.03125,-16) [0|255] "Unit_MeterPerSquareSecond" XXX
 SG_ Yaw_Rate : 40|14@1+ (0.01,0) [0|16383] "Unit_DegreePerSecond" XXX
 SG_ Yaw_Rate_Sign : 54|1@0+ (1,0) [0|1] "" XXX
 SG_ Regen_Braking : 123|1@1+ (1,0) [0|7] "" XXX
 SG_ Standstill : 171|1@0+ (1,0) [0|1] "" XXX
 SG_ Longitudinal_Speed : 181|10@1+ (0.25,0) [0|255] "Unit_KilometerPerHour" XXX

BO_ 261 VMM_01: 8 XXX
 SG_ CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ CNT : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_5 : 13|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_4 : 20|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_3 : 32|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_1 : 40|2@1+ (1,0) [0|3] "" XXX
 SG_ Brake : 53|7@1+ (1,0) [0|3] "" XXX

BO_ 267 Motor_51: 32 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Accel_Pedal_Pressure : 12|9@1+ (0.4,0) [0|255] "" XXX
 SG_ Accel_Low_Pressed_Support : 21|1@1+ (1,0) [0|7] "" XXX
 SG_ TSK_Status : 88|3@1+ (1,0) [0|7] "" XXX
 SG_ TSK_Limiter_ausgewaehlt : 95|1@1+ (1,0) [0|3] "" XXX

BO_ 278 ESP_10: 8 Gateway_MQB
 SG_ ESP_10_CRC : 0|8@1+ (1,0) [0|255] "" Airbag_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_10_BZ : 8|4@1+ (1,0) [0|15] "" Airbag_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Wegimpuls_VL : 12|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Wegimpuls_VR : 13|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Wegimpuls_HL : 14|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_QBit_Wegimpuls_HR : 15|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Wegimpuls_VL : 16|10@1+ (1,0) [0|1000] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Wegimpuls_VR : 26|10@1+ (1,0) [0|1000] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Wegimpuls_HL : 36|10@1+ (1,0) [0|1000] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_Wegimpuls_HR : 46|10@1+ (1,0) [0|1000] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_VL_Fahrtrichtung : 56|2@1+ (1,0) [0|3] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_VR_Fahrtrichtung : 58|2@1+ (1,0) [0|3] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_HL_Fahrtrichtung : 60|2@1+ (1,0) [0|3] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ ESP_HR_Fahrtrichtung : 62|2@1+ (1,0) [0|3] "" Airbag_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 285 LH_EPS_02: 8 Gateway_D4C7
 SG_ EPS_02_CRC : 0|8@1+ (1,0) [0|255] "" SCU_D4
 SG_ EPS_02_BZ : 8|4@1+ (1,0) [0|15] "" SCU_D4
 SG_ EPS_Notlauf : 12|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ EPS_Lastinfo : 16|8@1+ (1,0) [0|253] "Unit_Amper" Vector__XXX
 SG_ EPS_Unterstuetzungsleistung : 24|8@1+ (0.5,0) [0|100] "Unit_PerCent" SCU_D4
 SG_ EPS_Drehzahlreserve : 32|7@1+ (10,0) [0|1000] "Unit_DegreOfArcPerSecon" SCU_D4
 SG_ EPS_VZ_Drehzahlreserve : 39|1@1+ (1,0) [0|1] "" SCU_D4
 SG_ EPS_Leistungsanforderung : 40|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 294 HCA_01: 8 Frontsensorik
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ HCA_01_Vib_Freq : 12|4@1+ (1,15) [18|30] "Unit_Hertz" Vector__XXX
 SG_ HCA_01_LM_Offset : 16|9@1+ (1,0) [0|511] "Unit_centiNewtoMeter" Vector__XXX
 SG_ EA_ACC_Sollstatus : 25|2@1+ (1,0) [0|3] "" Frontradar
 SG_ EA_Ruckprofil : 27|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ HCA_01_Enable : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HCA_01_LM_OffSign : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HCA_01_Available : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HCA_01_Standby : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HCA_01_Request : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HCA_01_Vib_Amp : 36|4@1+ (0.2,0) [0|3] "Unit_NewtoMeter" Vector__XXX
 SG_ EA_Ruckfreigabe : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EA_ACC_Wunschgeschwindigkeit : 41|10@1+ (0.32,0) [0|327.04] "Unit_KiloMeterPerHour" Frontradar

BO_ 299 GRA_ACC_01: 8 Gateway
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ GRA_Hauptschalter : 12|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_Abbrechen : 13|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_Typ_Hauptschalter : 14|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_Limiter : 15|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_Tip_Setzen : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_Tip_Hoch : 17|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_Tip_Runter : 18|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_Tip_Wiederaufnahme : 19|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_Verstellung_Zeitluecke : 20|2@1+ (1,0) [0|3] "" ZR_High
 SG_ GRA_Codierung : 22|2@1+ (1,0) [0|3] "" ZR_High
 SG_ GRA_Fehler : 24|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_LIM_Taste_verfuegbar : 25|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_Tip_Stufe_2 : 26|1@1+ (1,0) [0|1] "" ZR_High
 SG_ GRA_ButtonTypeInfo : 27|3@1+ (1,0) [0|7] "" ZR_High
 SG_ GRA_TravelAssist : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GRA_reserveByte4 : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ GRA_reserveByte5 : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ GRA_reserveByte6 : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ GRA_reserveByte7 : 48|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ GRA_reserveByte8 : 56|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 312 IPA_01: 32 XXX

BO_ 313 VMM_02: 32 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Brake_Pressed_1 : 16|1@0+ (1,0) [0|1] "" XXX
 SG_ Brake_Pressed_2 : 27|1@0+ (1,0) [0|1] "" XXX
 SG_ AEB_Active : 31|1@0+ (1,0) [0|1] "" XXX
 SG_ ESP_Hold : 35|1@0+ (1,0) [0|1] "" XXX
 SG_ Brake_Pressed_3 : 48|1@0+ (1,0) [0|1] "" XXX
 SG_ FCW_Active : 56|1@1+ (1,0) [0|1] "" XXX
 SG_ Brake_Pressure : 76|11@1+ (1,0) [0|100] "" XXX

BO_ 317 QFK_01: 32 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_5 : 12|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_9 : 14|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_8 : 16|1@0+ (1,0) [0|1] "" XXX
 SG_ LatCon_HCA_Accept : 17|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_2 : 19|1@0+ (1,0) [0|1] "" XXX
 SG_ LatCon_HCA_Status : 20|3@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_1 : 23|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_10 : 24|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_6 : 30|3@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_7 : 34|3@0+ (1,0) [0|1] "" XXX
 SG_ Steering_Angle_VZ : 36|1@0+ (1,0) [0|1] "" XXX
 SG_ Curvature : 40|15@1+ (6.7e-06,0) [0|65535] "" XXX
 SG_ Curvature_VZ : 55|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_3 : 56|7@1+ (1,0) [0|63] "" XXX
 SG_ NEW_SIGNAL_4 : 63|1@0+ (1,0) [0|1] "" XXX
 SG_ Steering_Angle : 76|17@1+ (0.00906,0) [0|32767] "" XXX

BO_ 319 PreCrash_02: 8 Gateway
 SG_ PreCrash_02_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ PreCrash_02_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PreCrash_Charisma_FahrPr : 12|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PreCrash_Charisma_Status : 16|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ PreCrash_Schiebedach_schliessen : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PreCrash_Fenster_schliessen : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PreCrash_Blinken : 20|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ SC_PreSense_FCWP : 23|1@1+ (1,0) [0|1] "" NightVision
 SG_ PreCrash_Tueren_Verriegeln : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PreCrash_Anforderung_AFR : 26|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ SC_PreCrash_LED : 29|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ PreCrash_FS_Pneumatik_ansteuern : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PreCrash_BFS_Pneumatik_ansteuern : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PreCrash_Fo_Pneumatik_ansteuern : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PreCrash_FS_Sitzlehne_verfahren : 34|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ PreCrash_BFS_Sitzlehne_verfahren : 37|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ PreCrash_Fo_Sitzlehne_verfahren : 40|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ PreCrash_FS_KSV_verfahren : 43|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PreCrash_BFS_KSV_verfahren : 47|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PreCrash_Fo_KSV_verfahren : 51|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ SC_PreCrash_Warnung : 56|4@1+ (1,0) [0|15] "" NightVision
 SG_ SC_PreCrash_Texte : 60|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 332 Motor_54: 32 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Accelerator_Pressure : 175|8@0+ (0.391,-14.467) [0|100] "Unit_Percent" XXX

BO_ 333 ACC_18: 32 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ ACC_limitierte_Anfahrdyn : 12|1@1+ (1,0) [0|1] "" XXX
 SG_ ACC_nachtr_Stopp_Anf : 13|1@1+ (1,0) [0|1] "" XXX
 SG_ ACC_DynErhoehung : 14|1@1+ (1,0) [0|1] "" XXX
 SG_ ACC_Freilaufstrategie_TSK : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ ACC_zul_Regelabw_unten : 16|6@1+ (0.024,0) [0|1.512] "Unit_MeterPerSeconSquar" XXX
 SG_ ACC_StartStopp_Info : 22|2@1+ (1,0) [0|3] "" XXX
 SG_ ACC_Sollbeschleunigung_02 : 24|11@1+ (0.005,-7.22) [-7.22|3.005] "Unit_MeterPerSeconSquar" XXX
 SG_ ACC_zul_Regelabw_oben : 35|5@1+ (0.0625,0) [0|1.9375] "Unit_MeterPerSeconSquar" XXX
 SG_ ACC_neg_Sollbeschl_Grad_02 : 40|8@1+ (0.05,0) [0|12.75] "Unit_MeterPerCubicSecon" XXX
 SG_ ACC_pos_Sollbeschl_Grad_02 : 48|8@1+ (0.05,0) [0|12.75] "Unit_MeterPerCubicSecon" XXX
 SG_ ACC_Anfahren : 56|1@1+ (1,0) [0|1] "" XXX
 SG_ ACC_Anhalten : 57|1@1+ (1,0) [0|1] "" XXX
 SG_ ACC_Typ : 58|2@1+ (1,0) [0|3] "" XXX
 SG_ ACC_Status_ACC : 60|3@1+ (1,0) [0|7] "" XXX
 SG_ ACC_Minimale_Bremsung : 63|1@1+ (1,0) [0|1] "" XXX
 SG_ ACC_Anhalteweg : 64|11@1+ (0.01,0) [0|2046] "" XXX
 SG_ ACC_Anforderung_HMS : 77|3@1+ (1,0) [0|7] "" XXX
 SG_ SET_ME_0XFE : 80|8@1+ (1,0) [0|255] "" XXX
 SG_ ACC_AKTIV_regelt : 90|1@0+ (1,0) [0|1] "" XXX
 SG_ SET_ME_0X1 : 92|1@0+ (1,0) [0|1] "" XXX
 SG_ SET_ME_0X9 : 232|4@1+ (1,0) [0|15] "" XXX
 SG_ Speed : 236|11@1+ (0.1,0) [0|15] "" XXX
 SG_ Accel_Boost : 248|6@1+ (1,0) [0|3] "" XXX
 SG_ Reversing : 254|1@0+ (1,0) [0|1] "" XXX

BO_ 339 MSG_HYB_30: 8 Gateway
 SG_ MSG_HYB_30_CRC : 0|8@1+ (1,0) [0|255] "" Ladegeraet_Konzern
 SG_ MSG_HYB_30_BZ : 8|4@1+ (1,0) [0|15] "" Ladegeraet_Konzern
 SG_ MO_HVEM_Eskalation : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_ErwGrenzen_Anf : 13|1@1+ (1,0) [0|1] "" BMC_MLBevo
 SG_ MO_Fehler_Notentladung_Anf : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_HVEM_MaxLeistung : 15|9@1+ (50,0) [0|25450] "Unit_Watt" Vector__XXX
 SG_ MO_HVK_EmIstzustand : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ MO_HVK_AntriebFehlerstatus : 37|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ MO_MVK_Bordnetz_Anf : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_HVK_AntriebZustand : 41|3@1+ (1,0) [0|7] "" TME
 SG_ MO_HVK_EmFehlerstatus : 44|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ MO_MVK_AntriebFehlerstatus : 47|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ MO_MVK_AntriebZustand : 50|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ MO_MVK_EmFehlerstatus : 53|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ MO_MVK_EmIstzustand : 56|8@1+ (1,0) [0|255] "" Vector__XXX

BO_ 387 MEB_Camera_01: 64 XXX
 SG_ NEW_SIGNAL_1 : 191|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 200|9@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_3 : 209|9@1+ (1,0) [0|127] "" XXX
 SG_ NEW_SIGNAL_9 : 218|6@1+ (1,0) [0|63] "" XXX
 SG_ NEW_SIGNAL_10 : 224|8@1+ (1,0) [0|127] "" XXX
 SG_ NEW_SIGNAL_4 : 232|9@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_5 : 241|9@1+ (1,0) [0|127] "" XXX
 SG_ NEW_SIGNAL_8 : 250|6@1+ (1,0) [0|63] "" XXX
 SG_ Lane_Center_Offset : 256|12@1+ (0.001,-2.5) [0|255] "Unit_Meter" XXX
 SG_ NEW_SIGNAL_6 : 268|12@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_7 : 280|12@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_11 : 292|12@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_12 : 304|8@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_13 : 320|8@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_14 : 332|12@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_15 : 344|12@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_16 : 356|12@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_17 : 368|10@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_18 : 384|12@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_19 : 396|12@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_20 : 408|12@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_21 : 420|12@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_22 : 432|5@1+ (1,0) [0|31] "" XXX
 SG_ NEW_SIGNAL_23 : 448|12@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_24 : 460|12@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_25 : 472|12@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_26 : 484|12@1+ (1,0) [0|15] "" XXX

BO_ 420 EA_01: 8 Gateway
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ EA_Parken_beibehalten_HMS : 12|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ EA_Warnruckprofil : 28|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ EA_eCall_Anf : 31|2@1+ (1,0) [0|3] "" ZR_High
 SG_ EA_Funktionsstatus : 40|4@1+ (1,0) [0|15] "" ZR_High
 SG_ EA_Gurtstraffer_Anf : 44|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ EA_Anforderung_HMS : 48|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ EA_Sollbeschleunigung : 53|11@1+ (0.005,-7.22) [-7.22|3.005] "Unit_MeterPerSeconSquar" Vector__XXX

BO_ 496 EA_02: 8 Gateway
 SG_ EA_02_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ EA_02_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ EA_Texte : 12|4@1+ (1,0) [0|15] "" ZR_High
 SG_ ACF_Lampe_Hands_Off : 16|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ EA_Infotainment_Anf : 22|2@1+ (1,0) [0|3] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ EA_Tueren_Anf : 24|1@1+ (1,0) [0|1] "" ZR_High
 SG_ EA_Innenraumlicht_Anf : 25|1@1+ (1,0) [0|1] "" ZR_High
 SG_ zFAS_Warnblinken : 26|2@1+ (1,0) [0|3] "" ZR_High
 SG_ STP_Primaeranz : 28|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ EA_Bremslichtblinken : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ EA_Blinken : 32|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ EA_Unknown : 60|3@0+ (1,0) [0|7] "" XXX

BO_ 517 SAM_01: 8 XXX
 SG_ Brake_Light : 36|1@0+ (1,0) [0|1] "" XXX
 SG_ Left_Blinker : 37|1@0+ (1,0) [0|1] "" XXX
 SG_ Right_Blinker : 38|1@0+ (1,0) [0|1] "" XXX

BO_ 518 Parken_01: 24 XXX
 SG_ CHK : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ CNT : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ AEB_Active : 16|1@0+ (1,0) [0|1] "" XXX

BO_ 522 EML_06: 64 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Yaw_Rate_VZ : 33|1@1+ (1,0) [0|1] "" XXX
 SG_ Yaw_Rate : 200|16@1+ (0.007,-229.34) [0|255] "" XXX

BO_ 564 MEB_Camera_02: 64 XXX
 SG_ NEW_SIGNAL_1 : 12|6@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_2 : 18|6@1+ (1,0) [0|63] "" XXX
 SG_ NEW_SIGNAL_3 : 24|6@1+ (1,0) [0|63] "" XXX
 SG_ NEW_SIGNAL_4 : 30|6@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_7 : 178|13@1+ (1,0) [0|127] "" XXX
 SG_ NEW_SIGNAL_6 : 192|12@1+ (1,0) [0|255] "" XXX
 SG_ NEW_SIGNAL_8 : 286|9@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_5 : 295|9@1+ (1,0) [0|255] "" XXX

BO_ 588 MEB_Side_Assist_01: 16 XXX
 SG_ Blind_Spot_Right : 12|7@1+ (1,0) [0|15] "" XXX
 SG_ Blind_Spot_Left : 19|7@1+ (1,0) [0|15] "" XXX
 SG_ Blind_Spot_Info_Right : 26|1@0+ (1,0) [0|1] "" XXX
 SG_ Blind_Spot_Warn_Right : 27|1@0+ (1,0) [0|1] "" XXX
 SG_ Blind_Spot_Info_Left : 29|1@0+ (1,0) [0|1] "" XXX
 SG_ Blind_Spot_Warn_Left : 30|1@0+ (1,0) [0|1] "" XXX
 SG_ Lower_Speed_01 : 32|1@0+ (1,0) [0|1] "" XXX
 SG_ Higher_Speed_01 : 33|1@0+ (1,0) [0|1] "" XXX
 SG_ Higher_Speed_02 : 83|1@0+ (1,0) [0|1] "" XXX
 SG_ Lower_Speed_02 : 84|1@0+ (1,0) [0|1] "" XXX
 SG_ Standstill : 86|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_1 : 98|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_6 : 119|1@0+ (1,0) [0|1] "" XXX

BO_ 589 MEB_Side_Assist_02: 64 XXX
 SG_ Unknown_01 : 100|3@0+ (1,0) [0|7] "" XXX
 SG_ Unknown_02 : 108|3@0+ (1,0) [0|7] "" XXX

BO_ 591 MEB_Distance_01: 64 XXX
 SG_ Unknown_01 : 12|1@0+ (1,0) [0|1] "" XXX
 SG_ Distance_Status : 13|2@1+ (1,0) [0|3] "" XXX
 SG_ Same_Lane_01_ObjectID : 16|6@1+ (1,0) [0|63] "Unit_ObjectID" XXX
 SG_ Left_Lane_01_ObjectID : 22|6@1+ (1,0) [0|63] "Unit_ObjectID" XXX
 SG_ Right_Lane_01_ObjectID : 28|6@1+ (1,0) [0|63] "Unit_ObjectID" XXX
 SG_ Same_Lane_02_ObjectID : 34|6@1+ (1,0) [0|63] "Unit_ObjectID" XXX
 SG_ Left_Lane_02_ObjectID : 40|6@1+ (1,0) [0|63] "Unit_ObjectID" XXX
 SG_ Right_Lane_02_ObjectID : 46|6@1+ (1,0) [0|63] "Unit_ObjectID" XXX
 SG_ Unknown_02 : 52|2@1+ (1,0) [0|3] "" XXX
 SG_ Unknown_03 : 54|10@1+ (1,0) [0|3] "" XXX
 SG_ Same_Lane_01_Long_Distance : 64|12@1+ (0.07,-6) [0|300] "Unit_Meter" XXX
 SG_ Same_Lane_01_Lat_Distance : 76|10@1+ (0.065,-33.28) [-50|50] "Unit_Meter" XXX
 SG_ Same_Lane_01_Rel_Velo : 86|10@1+ (0.25,-128) [-100|100] "Unit_MeterPerSecond" XXX
 SG_ Left_Lane_01_Long_Distance : 96|12@1+ (0.07,-6) [0|300] "Unit_Meter" XXX
 SG_ Left_Lane_01_Lat_Distance : 108|10@1+ (0.065,-33.28) [-50|50] "Unit_Meter" XXX
 SG_ Left_Lane_01_Rel_Velo : 118|10@1+ (0.25,-128) [-100|100] "Unit_MeterPerSecond" XXX
 SG_ Right_Lane_01_Long_Distance : 128|12@1+ (0.07,-6) [0|300] "Unit_Meter" XXX
 SG_ Right_Lane_01_Lat_Distance : 140|10@1+ (0.065,-33.28) [-50|50] "Unit_Meter" XXX
 SG_ Right_Lane_01_Rel_Velo : 150|10@1+ (0.25,-128) [-100|100] "Unit_MeterPerSecond" XXX
 SG_ Same_Lane_02_Long_Distance : 160|12@1+ (0.07,-6) [0|300] "Unit_Meter" XXX
 SG_ Same_Lane_02_Lat_Distance : 172|10@1+ (0.065,-33.28) [-50|50] "Unit_Meter" XXX
 SG_ Same_Lane_02_Rel_Velo : 182|10@1+ (0.25,-128) [-100|100] "Unit_MeterPerSecond" XXX
 SG_ Left_Lane_02_Long_Distance : 192|12@1+ (0.07,-6) [0|300] "Unit_Meter" XXX
 SG_ Left_Lane_02_Lat_Distance : 204|10@1+ (0.065,-33.28) [-50|50] "Unit_Meter" XXX
 SG_ Left_Lane_02_Rel_Velo : 214|10@1+ (0.25,-128) [-100|100] "Unit_MeterPerSecond" XXX
 SG_ Right_Lane_02_Long_Distance : 224|12@1+ (0.07,-6) [0|300] "Unit_Meter" XXX
 SG_ Right_Lane_02_Lat_Distance : 236|10@1+ (0.065,-33.28) [-50|50] "Unit_Meter" XXX
 SG_ Right_Lane_02_Rel_Velo : 246|10@1+ (0.25,-128) [-100|100] "Unit_MeterPerSecond" XXX
 SG_ Unknown_04 : 256|8@1+ (1,-128) [0|31] "" XXX
 SG_ Unknown_05 : 264|6@1+ (1,-15) [0|31] "" XXX
 SG_ Unknown_06 : 270|6@1+ (1,0) [0|127] "" XXX
 SG_ Unknown_07 : 277|6@1+ (1,0) [0|7] "" XXX
 SG_ Unknown_08 : 284|6@1+ (1,0) [0|1] "" XXX

BO_ 605 KLR_01: 8 Gateway
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ KLR_Fehler : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KLR_ResponseError : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KLR_Lokalaktiv : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KLR_Fehler_Codierung : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KLR_Touchintensitaet_1 : 16|8@1+ (1,0) [0|250] "Unit_None" Vector__XXX
 SG_ KLR_Touchintensitaet_2 : 24|8@1+ (1,0) [0|250] "Unit_None" Vector__XXX
 SG_ KLR_Touchintensitaet_3 : 32|8@1+ (1,0) [0|250] "Unit_None" Vector__XXX
 SG_ KLR_Touchauswertung : 40|4@1+ (1,0) [0|15] "" ZR_High

BO_ 619 TA_01: 8 XXX
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Travel_Assist_Status : 13|3@1+ (1,0) [0|3] "" XXX
 SG_ Travel_Assist_Request : 19|3@1+ (1,0) [0|7] "" XXX
 SG_ Travel_Assist_Available : 23|1@1+ (1,0) [0|1] "" XXX

BO_ 695 RCTA_01: 8 XXX
 SG_ RCTA_01_CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ RCTA_01_BZ : 8|4@1+ (1,0) [0|15] "" XXX

BO_ 706 Motor_41: 8 Gateway
 SG_ MO_Anzeige_StSt_Text : 12|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MO_Anzeige_StSt_Symbol : 16|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ MO_ADR_Status : 19|2@1+ (1,0) [0|3] "" ZR_High
 SG_ MO_AGA_Sound_Texte : 21|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ MO_Anzeige_FMAus_Text : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_Fehler_MSpG : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PEA_Texte : 25|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ TSK_Ueberstimmt_vMax_FahrerInfo : 28|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ MO_Avus_Motorschutz : 30|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ MO_Rekuperationsstufe : 32|3@1+ (1,0) [0|7] "" OTA_FC
 SG_ TSK_Einheit_vMax_FahrerInfo : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TSK_Status_vMax_FahrerInfo : 36|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ MO_Red_Fahrleistung : 38|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ MO_Anz_Kuehlerluefter : 47|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ MO_im_Leerlauf : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Enable_Oeldr_Motor : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_OelMessung_Dauer : 52|4@1+ (15,15) [15|225] "Unit_Secon" Vector__XXX
 SG_ TSK_vMax_FahrerInfo : 56|8@1+ (1,15) [16|270] "" Vector__XXX

BO_ 768 MEB_ACC_01: 48 XXX
 SG_ ACC_Tempolimit : 64|5@1+ (1,0) [0|31] "" OTA_FC
 SG_ ACC_Wunschgeschw_Farbe : 69|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ACC_Warnung_Verkehrszeichen_1 : 70|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ACA_Querfuehrung : 71|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ Unknown_02 : 73|1@0+ (1,0) [0|1] "" XXX
 SG_ ACC_Regelung_AIO : 75|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ ACC_Wunschgeschw_02 : 76|10@1+ (0.32,0) [0|327.04] "Unit_KiloMeterPerHour" Vector__XXX
 SG_ ACC_Abstandsindex_02 : 86|10@1+ (1,0) [1|1021] "" Vector__XXX
 SG_ ACC_Display_Prio : 96|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ ACC_rel_Objekt_Zusatzanz : 98|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ ACC_Gesetzte_Zeitluecke : 101|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ ACC_Optischer_Fahrerhinweis : 104|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ACC_Warnhinweis : 105|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ACC_EGO_Fahrzeug : 106|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ ACC_Relevantes_Objekt_02 : 109|2@1+ (1,0) [0|3] "" OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ ACC_Wunschgeschw_erreicht : 112|1@1+ (1,0) [0|1] "" OTA_FC
 SG_ ACC_Anzeige_Zeitluecke : 113|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ACC_Texte_Primaeranz_02 : 114|6@1+ (1,0) [0|63] "" Vector__XXX
 SG_ ACC_Texte_Zusatzanz_02 : 120|6@1+ (1,0) [0|63] "" Vector__XXX
 SG_ STA_Primaeranz : 126|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ SET_ME_0X3FF : 140|10@1+ (1,0) [0|15] "" XXX
 SG_ Heartbeat : 150|9@1+ (1,0) [0|3] "" XXX
 SG_ SET_ME_0XFFFF : 160|16@1+ (1,0) [0|65535] "" XXX
 SG_ ACC_Enabled : 186|1@0+ (1,0) [0|1] "" XXX
 SG_ Zeitluecke_Farbe : 189|1@0+ (1,0) [0|1] "" XXX
 SG_ SET_ME_0X1 : 199|1@0+ (1,0) [0|1] "" XXX
 SG_ ACC_Status_ACC : 208|3@1+ (1,0) [0|7] "" XXX
 SG_ ACC_Akustischer_Fahrerhinweis : 211|2@1+ (1,0) [0|1] "" XXX
 SG_ Unknown_08 : 224|1@0+ (1,0) [0|1] "" XXX
 SG_ Unknown_01 : 225|1@0+ (1,0) [0|1] "" XXX
 SG_ Unknown_06 : 226|1@0+ (1,0) [0|1] "" XXX
 SG_ Unknown_07 : 228|1@0+ (1,0) [0|1] "" XXX
 SG_ SET_ME_0X7FFF : 240|16@1+ (1,0) [0|65535] "" XXX
 SG_ Unknown_09 : 262|1@0+ (1,0) [0|3] "" XXX
 SG_ Lead_Type_Detected : 265|1@0+ (1,0) [0|1] "" XXX
 SG_ ACC_Standby_Override : 266|1@0+ (1,0) [0|1] "" XXX
 SG_ Street_Color : 267|1@0+ (1,0) [0|1] "" XXX
 SG_ ACC_Limiter_Mode : 268|1@0+ (1,0) [0|1] "" XXX
 SG_ Lead_Brightness : 269|4@1+ (1,0) [0|7] "" XXX
 SG_ SET_ME_0X6A : 273|8@1+ (1,0) [0|7] "" XXX
 SG_ Lead_Type : 287|3@1+ (1,0) [0|3] "" XXX
 SG_ Lead_Distance : 290|10@1+ (0.2,0) [0|7] "Unit_Meter" XXX
 SG_ ACC_Events : 332|4@0+ (1,0) [0|3] "Unit_Meter" XXX
 SG_ Zeitluecke_1 : 334|9@1+ (0.171,0) [0|100] "Unit_Meter" XXX
 SG_ Zeitluecke_2 : 344|9@1+ (0.171,0) [0|100] "Unit_Meter" XXX
 SG_ Zeitluecke_3 : 354|9@1+ (0.171,0) [0|100] "Unit_Meter" XXX
 SG_ Zeitluecke_4 : 364|9@1+ (0.171,0) [0|100] "Unit_Meter" XXX
 SG_ Zeitluecke_5 : 374|9@1+ (0.171,0) [0|100] "Unit_Meter" XXX

BO_ 771 HCA_03: 24 XXX
 SG_ RequestStatus : 12|4@1+ (1,0) [0|15] "" XXX
 SG_ Power : 16|8@1+ (0.4,0) [0.0|100.0] "percent" XXX
 SG_ Curvature : 24|15@1+ (6.7e-06,0) [0|0.219] "Unit_rad/m" XXX
 SG_ Curvature_VZ : 39|1@1+ (1,0) [0|1] "" XXX
 SG_ Unknown_01 : 53|1@0+ (1,0) [0|1] "" XXX
 SG_ Vibration : 56|1@0+ (1,0) [0|1] "" XXX
 SG_ HighSendRate : 66|1@1+ (1,0) [0|1] "" XXX

BO_ 792 MEB_Camera_03: 8 XXX

BO_ 795 ESP_24: 8 Gateway
 SG_ ESP_Lampe : 12|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ ABS_Lampe : 13|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ BK_Lampe_02 : 14|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ TC_Lampe : 16|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ ESP_m_Raddrehz : 17|15@1+ (0.002,0) [0|65.278] "Unit_Hertz" Sub_Gateway
 SG_ ESP_Textanzeigen_03 : 32|5@1+ (1,0) [0|31] "" Sub_Gateway
 SG_ ESP_Meldungen : 37|3@1+ (1,0) [0|7] "" Sub_Gateway
 SG_ ESP_Wegimp_VA : 40|11@1+ (1,0) [0|2047] "" Sub_Gateway
 SG_ ESP_Fehlerstatus_Wegimp : 51|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ ESP_Wegimp_Ueberlauf : 52|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ ESP_QBit_Wegimp_VA : 53|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ ESP_HDC_Geschw_Farbe : 54|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ ESP_Off_Lampe : 55|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ ESP_HDC_Regelgeschw : 56|7@1+ (0.32,0) [0.32|39.68] "Unit_KiloMeterPerHour" Sub_Gateway
 SG_ ESP_BKV_Warnung : 63|1@1+ (1,0) [0|1] "" Sub_Gateway

BO_ 817 MFL_01: 8 Gateway
 SG_ MFL_Zaehler : 0|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MFL_Toggle : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MFL_Lokalaktiv : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MFL_M_Taste : 6|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Paddle_Verbau : 7|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Tastencode_1 : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ MFL_Tastencode_2 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ MFL_Eventcode_1 : 24|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MFL_Eventcode_2 : 28|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MFL_Marke : 32|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MFL_Variante : 36|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MFL_Dummy_0_Signal_1 : 40|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ MFL_Tip_Down : 48|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Tip_Up : 49|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_SatModul_links_Err : 50|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_SatModul_rechts_Err : 51|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Dummy_0_Signal_2 : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MFL_Dummy_0_Signal_3 : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MFL_LR_HZG_Status : 54|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_LR_HZG_Err : 55|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Signalhorn : 56|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ MFL_Signalhorn_Err : 57|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Tip_links_Err : 58|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Tip_rechts_Err : 59|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Taste_links_Err : 60|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Taste_rechts_Err : 61|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_ECU_Err : 62|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MFL_Response_Err : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 850 Parken_SM_03: 8 Gateway
 SG_ Parken_SM_03_MUX M : 0|3@1+ (1,0) [0|7] "" DDA
 SG_ Parken_SM_03_Traj_Trans_ID_00 m0 : 3|4@1+ (1,0) [0|15] "" DDA
 SG_ Parken_Traj_P1_Pos_X m0 : 7|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P1_Pos_Y m0 : 20|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P1_Tangent m0 : 33|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" DDA
 SG_ Parken_Traj_P2_Pos_X m0 : 43|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_SM_03_Traj_Trans_ID_01 m1 : 3|4@1+ (1,0) [0|15] "" DDA
 SG_ Parken_Traj_P2_Pos_Y m1 : 7|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P2_Tangent m1 : 20|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" DDA
 SG_ Parken_Traj_P3_Pos_X m1 : 30|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P3_Pos_Y m1 : 43|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_SM_03_Traj_Trans_ID_02 m2 : 3|4@1+ (1,0) [0|15] "" DDA
 SG_ Parken_Traj_P3_Tangent m2 : 7|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" DDA
 SG_ Parken_Traj_P4_Pos_X m2 : 17|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P4_Pos_Y m2 : 30|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P4_Tangent m2 : 43|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" DDA
 SG_ Parken_SM_03_Traj_Trans_ID_03 m3 : 3|4@1+ (1,0) [0|15] "" DDA
 SG_ Parken_Traj_P5_Pos_X m3 : 7|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P5_Pos_Y m3 : 20|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P5_Tangent m3 : 33|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" DDA
 SG_ Parken_Traj_P6_Pos_X m3 : 43|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_SM_03_Traj_Trans_ID_04 m4 : 3|4@1+ (1,0) [0|15] "" DDA
 SG_ Parken_Traj_P6_Pos_Y m4 : 7|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P6_Tangent m4 : 20|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" DDA
 SG_ Parken_Traj_P7_Pos_X m4 : 30|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P7_Pos_Y m4 : 43|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_SM_03_Traj_Trans_ID_05 m5 : 3|4@1+ (1,0) [0|15] "" DDA
 SG_ Parken_Traj_P7_Tangent m5 : 7|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" DDA
 SG_ Parken_Traj_P8_Pos_X m5 : 17|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P8_Pos_Y m5 : 30|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P8_Tangent m5 : 43|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" DDA
 SG_ Parken_SM_03_Traj_Trans_ID_06 m6 : 3|4@1+ (1,0) [0|15] "" DDA
 SG_ Parken_Traj_P9_Pos_X m6 : 7|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P9_Pos_Y m6 : 20|13@1+ (0.5,-2047.5) [-2047.5|2048] "Unit_Centimeter" DDA
 SG_ Parken_Traj_P9_Tangent m6 : 33|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" DDA

BO_ 869 NVEM_05: 8 Gateway
 SG_ NVEM_05_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ NVEM_05_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ NVEM_Pilot_Info : 12|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ NVEM_P_Generator_Status : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BEM_P_Generator : 16|8@1+ (50,0) [0|12700] "Unit_Watt" Vector__XXX
 SG_ BEM_n_LLA : 24|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BEM_Anf_KL : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BEM_StartStopp_Info : 30|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BEM_DFM : 32|5@1+ (3.225,0.025) [0.025|100] "" Vector__XXX
 SG_ BEM_Batt_Ab : 38|1@1+ (1,0) [0|1] "" DCDC_800V_PAG,DCDC_IHEV
 SG_ BEM_Hybrid_Info : 44|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ NVEM_Red_KL : 46|2@1+ (1,0) [0|3] "" TME
 SG_ NVEM_Freilauf_Info : 48|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BEM_HYB_DC_uSollLV : 50|6@1+ (0.1,10.6) [10.6|16] "Unit_Volt" DCDC_800V_PAG,DCDC_HV,LE_MLBevo
 SG_ BEM_HYB_DC_uMinLV : 56|8@1+ (0.1,0) [0|25.3] "Unit_Volt" Vector__XXX

BO_ 870 Blinkmodi_02: 8 Gateway
 SG_ BM_ZV_auf : 12|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_ZV_zu : 13|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_DWA_ein : 14|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_DWA_Alarm : 15|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_Crash : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_Panik : 17|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_Not_Bremsung : 18|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_GDO : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BM_Warnblinken : 20|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_Taxi_Notalarm : 21|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_Telematik : 22|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_links : 23|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_rechts : 24|1@1+ (1,0) [0|1] "" ZR_High
 SG_ Blinken_li_Fzg_Takt : 25|1@1+ (1,0) [0|1] "" ZR_High
 SG_ Blinken_re_Fzg_Takt : 26|1@1+ (1,0) [0|1] "" ZR_High
 SG_ Blinken_li_Kombi_Takt : 27|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ Blinken_re_Kombi_Takt : 28|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ BM_NBA_n_codiert_n_aktiv : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BM_NBA_Status : 30|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BM_WBT_Beleuchtung : 32|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_HD_Oeffnung_angelernt : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BM_Autobahn : 34|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ BM_Rollenmodus_Blinken : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BM_Recas : 36|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_Wischblinken : 37|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BM_Telematik_Abbruchgrund : 38|6@1+ (1,0) [0|63] "" Vector__XXX
 SG_ BM_PiloPa : 44|1@1+ (1,0) [0|1] "" ZR_High
 SG_ DWA_Alarmquelle : 59|5@1+ (1,0) [0|31] "" ZR_High

BO_ 888 GNSS_04: 8 Gateway
 SG_ GNSS_Nachrichtenpaket_ID4 : 0|2@1+ (1,0) [0|3] "Unit_Bit" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GNSS_Ortung_Zeit_in_GPSWoche : 2|30@1+ (1,0) [0|604800001] "Unit_MilliSecon" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GNSS_Ortung_Hoehe : 32|12@1+ (2,-500) [-500|7686] "Unit_Meter" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard

BO_ 891 GNSS_05: 8 Gateway
 SG_ GNSS_UTC_Zeit : 0|32@1+ (1,0) [1|4294967295] "Unit_Secon" OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GNSS_Empfaenger_Status : 32|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GNSS_GPS_in_Nutzung : 33|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GNSS_GLONASS_in_Nutzung : 34|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GNSS_Empfangbare_Satelliten : 35|5@1+ (1,0) [1|30] "Unit_None" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GNSS_Sichtbare_Satelliten : 40|5@1+ (1,0) [1|30] "Unit_None" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GNSS_Genutzte_Satelliten : 45|5@1+ (1,0) [1|30] "Unit_None" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GNSS_Nachrichtenpaket_ID5 : 50|2@1+ (1,0) [0|3] "Unit_Bit" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard

BO_ 896 APS_Master: 8 XXX
 SG_ Active : 54|1@0+ (1,0) [0|1] "" XXX
 SG_ Distance : 55|9@1+ (1,0) [0|255] "" XXX

BO_ 916 WBA_03: 8 Gateway
 SG_ WBA_03_CRC : 0|8@1+ (1,0) [0|255] "" Sub_Gateway
 SG_ WBA_03_BZ : 8|4@1+ (1,0) [0|15] "" Sub_Gateway
 SG_ WBA_Fahrstufe_02 : 12|4@1+ (1,0) [0|15] "" Sub_Gateway
 SG_ WBA_ZielFahrstufe : 16|4@1+ (1,0) [0|15] "" Sub_Gateway
 SG_ WBA_GE_Warnung_02 : 20|4@1+ (1,0) [0|15] "" Sub_Gateway
 SG_ WBA_eing_Gang_02 : 24|4@1+ (1,0) [0|15] "" Sub_Gateway
 SG_ WBA_GE_Texte : 28|3@1+ (1,0) [0|7] "" Sub_Gateway
 SG_ WBA_Segeln_aktiv : 31|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ WBA_Schaltschema : 32|5@1+ (1,0) [0|31] "" Sub_Gateway
 SG_ WBA_GE_Zusatzwarnungen : 37|3@1+ (1,0) [0|7] "" Sub_Gateway
 SG_ GE_Sollgang : 40|4@1+ (1,0) [0|15] "" Sub_Gateway
 SG_ GE_Tipschaltempf_verfuegbar : 44|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ WBA_GE_Texte_02 : 45|3@1+ (1,0) [0|7] "" Sub_Gateway
 SG_ WBA_GE_Texte_03 : 48|4@1+ (1,0) [0|15] "" Sub_Gateway
 SG_ WBA_Blinken : 54|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ GE_Wiederstart_Anz_Std : 55|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ GE_Stoppverbot_Anz_01 : 56|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ GE_Stoppverbot_Anz_02 : 57|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ GE_Stoppverbot_Anz_03 : 58|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ GE_Stoppverbot_Anz_04 : 59|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ GE_Stoppverbot_Anz_05 : 60|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ GE_Stoppverbot_Anz_06 : 61|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ GE_Stoppverbot_Anz_07 : 62|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ GE_Stoppverbot_Anz_Std : 63|1@1+ (1,0) [0|1] "" Sub_Gateway

BO_ 919 LDW_02: 8 XXX
 SG_ LDW_Gong : 12|2@1+ (1,0) [0|3] "" XXX
 SG_ LDW_SW_Warnung_links : 14|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_SW_Warnung_rechts : 15|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_Texte : 16|4@1+ (1,0) [0|15] "" XXX
 SG_ LDW_Seite_DLCTLC : 20|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_Lernmodus : 21|3@1+ (1,0) [0|7] "" XXX
 SG_ LDW_Anlaufsp_VLR : 24|4@1+ (1,0) [0|15] "" XXX
 SG_ LDW_Vib_Amp_VLR : 28|4@1+ (1,0) [0|15] "" XXX
 SG_ LDW_Anlaufzeit_VLR : 32|4@1+ (1,0) [0|15] "" XXX
 SG_ LDW_Lernmodus_rechts : 36|2@1+ (1,0) [0|3] "" XXX
 SG_ LDW_Lernmodus_links : 38|2@1+ (1,0) [0|3] "" XXX
 SG_ LDW_DLC : 40|8@1+ (0.01,-1.25) [-1.25|1.25] "Unit_Meter" XXX
 SG_ LDW_TLC : 48|5@1+ (0.1,0) [0|3] "Unit_Secon" XXX
 SG_ LDW_Warnung_links : 56|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_Warnung_rechts : 57|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_Codierinfo_fuer_VLR : 58|2@1+ (1,0) [0|3] "" XXX
 SG_ LDW_Frontscheibenheizung_aktiv : 60|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_Status_LED_gelb : 61|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_Status_LED_gruen : 62|1@1+ (1,0) [0|1] "" XXX
 SG_ LDW_KD_Fehler : 63|1@1+ (1,0) [0|1] "" XXX

BO_ 949 Klima_11: 8 Gateway
 SG_ KL_Drehz_Anh : 0|1@1+ (1,0) [0|1] "" TME
 SG_ KL_Vorwarn_Komp_ein : 1|1@1+ (1,0) [0|1] "" TME
 SG_ KL_AC_Schalter : 2|1@1+ (1,0) [0|1] "" TME
 SG_ KL_Komp_Moment_alt : 3|1@1+ (1,0) [0|1] "" TME
 SG_ KL_Vorwarn_Zuheizer_ein : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_Zustand : 7|1@1+ (1,0) [0|1] "" TME
 SG_ KL_Kompressorkupplung_linear : 8|8@1+ (20,0) [0|4000] "Unit_MilliAmper" Vector__XXX
 SG_ KL_Charisma_FahrPr : 16|4@1+ (1,0) [0|15] "" TME
 SG_ KL_Charisma_Status : 20|2@1+ (1,0) [0|3] "" TME
 SG_ KL_nachtr_Stopp_Anf : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_T_Charge : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_Last_Kompr : 24|8@1+ (0.25,0) [0|63.5] "Unit_NewtoMeter" TME
 SG_ KL_Spannungs_Anf : 32|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KL_Thermomanagement : 34|2@1+ (1,0) [0|3] "" TME
 SG_ KL_StartStopp_Info : 36|2@1+ (1,0) [0|3] "" TME
 SG_ KL_Freilauf_Info : 38|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KL_Anf_KL : 40|8@1+ (0.4,0) [0|101.6] "Unit_PerCent" TME
 SG_ KL_el_Zuheizer_Stufe : 48|3@1+ (1,0) [0|7] "" TME
 SG_ KL_Ausstattung_Klima : 51|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ KL_Variante_Standheizung : 54|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 958 Motor_14: 8 Gateway
 SG_ CHECKSUM : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ COUNTER : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ MO_StartStopp_Status : 12|2@1+ (1,0) [0|3] "" Sub_Gateway,TME
 SG_ MO_StartStopp_Wiederstart : 14|1@1+ (1,0) [0|1] "" BMS_NV,Sub_Gateway,TME
 SG_ MO_StartStopp_Motorstopp : 15|1@1+ (1,0) [0|1] "" BMS_NV,Sub_Gateway,TME
 SG_ MO_Freig_Reku : 16|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ MO_Kl_75 : 18|1@1+ (1,0) [0|1] "" Sub_Gateway,TME
 SG_ MO_Kl_50 : 19|1@1+ (1,0) [0|1] "" DCDC_IHEV,Ladegeraet_Konzern,Sub_Gateway
 SG_ MO_Gangposition : 20|4@1+ (1,0) [0|15] "" AWC,Sub_Gateway,TME
 SG_ MO_StartStopp_Fahrerwunsch : 24|2@1+ (1,0) [0|3] "" Sub_Gateway,TME
 SG_ MO_HYB_Fahrbereitschaft : 26|1@1+ (1,0) [0|1] "" AWC,BMS_NV,Ladegeraet_Konzern,Sub_Gateway,TME
 SG_ MO_Ext_E_Fahrt_aktiv : 27|1@1+ (1,0) [0|1] "" Sub_Gateway,TME
 SG_ MO_Fahrer_bremst : 28|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_QBit_Fahrer_bremst : 29|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_BLS : 30|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Konsistenz_Bremsped : 31|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_KomFehler_ESP : 32|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Klima_Eingr : 33|2@1+ (1,0) [0|3] "" Sub_Gateway,TME
 SG_ MO_Aussp_Anlass : 35|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Freig_Anlass : 36|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Kuppl_schalter : 37|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Interlock : 38|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Motor_laeuft : 39|1@1+ (1,0) [0|1] "" Sub_Gateway,TME
 SG_ MO_Kickdown : 40|1@1+ (1,0) [0|1] "" Sub_Gateway,TME
 SG_ MO_QBit_KL_75 : 41|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_EKlKomLeiRed : 42|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ MO_Handshake_STH : 44|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_BKV_Unterdruckwarnung : 45|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Freigabe_Segeln : 46|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_PTC_Status : 47|3@1+ (1,0) [0|7] "" Sub_Gateway
 SG_ MO_QBit_Gangposition : 50|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Signalquelle_Gangposition : 51|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Remotestart_Betrieb : 52|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Remotestart_moeglich : 53|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_FMAus_aktiv : 55|1@1+ (1,0) [0|1] "" BMS_NV,Sub_Gateway
 SG_ MO_FMAus_Startvariante : 56|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ MO_BMS_NV_Anf_stuetzen : 58|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Zylinderabschaltung : 59|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ MO_HYB_VM_aktiv : 61|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_StartVorauss_erfuellt : 62|2@1+ (1,0) [0|3] "" AWC,Ladegeraet_Konzern,Sub_Gateway

BO_ 960 Klemmen_Status_01: 4 Gateway
 SG_ Klemmen_Status_01_CRC : 0|8@1+ (1,0) [0|255] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ Klemmen_Status_01_BZ : 8|4@1+ (1,0) [0|15] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ RSt_Fahrerhinweise : 12|4@1+ (1,0) [0|15] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZAS_Kl_S : 16|1@1+ (1,0) [0|1] "" BedienDisp_vo,BedienSG_hi,GurtMikrofon,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZAS_Kl_15 : 17|1@1+ (1,0) [0|1] "" BedienDisp_vo,BedienSG_hi,DDA,GurtMikrofon,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZAS_Kl_X : 18|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZAS_Kl_50_Startanforderung : 19|1@1+ (1,0) [0|1] "" BedienDisp_vo,BedienSG_hi,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ BCM_Remotestart_Betrieb : 20|1@1+ (1,0) [0|1] "" BedienSG_hi,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZAS_Kl_Infotainment : 21|1@1+ (1,0) [0|1] "" BedienDisp_vo,BedienSG_hi,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ BCM_Remotestart_KL15_Anf : 22|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ BCM_Remotestart_MO_Start : 23|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ KST_Warn_P1_ZST_def : 24|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ KST_Warn_P2_ZST_def : 25|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ KST_Fahrerhinweis_1 : 26|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ KST_Fahrerhinweis_2 : 27|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ BCM_Ausparken_Betrieb : 28|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ KST_Fahrerhinweis_4 : 29|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ KST_Fahrerhinweis_5 : 30|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ KST_Fahrerhinweis_6 : 31|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard

BO_ 967 Motor_26: 8 Gateway
 SG_ MO_Kuehlerluefter_MUX M : 0|1@1+ (1,0) [0|1] "" TME
 SG_ MO_Kuehlerluefter_1 m0 : 1|7@1+ (1,0) [0|100] "Unit_PerCent" TME
 SG_ MO_Kuehlerluefter_2 m1 : 1|7@1+ (1,0) [0|100] "Unit_PerCent" TME
 SG_ MO_EFLEX_Lampe : 8|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ MO_KJS_nicht_bereit : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_ITM_Warnung_Pumpe : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Anzeige_aktiv : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Oelmin_Warn : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Sensorfehler : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Schieflage : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Oelstand : 16|4@1+ (12.5,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ MO_Zustand_HWP : 20|2@1+ (1,0) [0|3] "" TME
 SG_ OLEV_Systemstoerung : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_Oelwarnung_max : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Oelsystem_aktiv : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_nicht_betriebswarm : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Ueberfuell_Warn : 26|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_laufender_Motor : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_E_Warnungen : 28|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MO_Text_Motorstart : 32|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MO_E_Texte : 36|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ WIV_Oeldyn_avl : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_Text_Partikelfil_Reg : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ OLEV_Oelstand_nicht_vorhanden : 42|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Oelmenge : 43|5@1+ (125,0) [0|3875] "Unit_MilliLiter" Vector__XXX
 SG_ MO_Systemlampe : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_OBD2_Lampe : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_Heissleuchte : 50|1@1+ (1,0) [0|1] "" TME
 SG_ MO_Partikel_Lampe : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_RedFahrleistung_Lampe : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Oelstand_nicht_vorhanden : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_nachfuellanzeige_ein : 54|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Ueberfuell_deaktiv : 55|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Unterfuell_Warn : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_Tankdeckel_Lampe : 57|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_Text_Tankdeckelwarn : 58|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_Vorglueh_Lampe : 59|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WIV_Oeldr_Warn_Motor : 60|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_E_Mode : 61|3@1+ (1,0) [0|7] "" Vector__XXX

BO_ 974 TSG_HFS_01: 8 Gateway
 SG_ HFS_Tuer_geoeffnet : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_verriegelt : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_gesafet : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Heckrollotaster_betaetigt : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Tuerschloss_defekt : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Unlock_Taster : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Lock_Taster : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Sperrklinke : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_TAG_betaetigt : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_TIG_betaetigt : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_S_HBFS_AutoHoch : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_S_HBFS_AutoTief : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_S_HBFS_ManHoch : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_S_HBFS_ManTief : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Tuer_Status : 14|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ HFS_SAD_Schalter : 16|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ HFS_FH_S_ManHoch : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_S_AutoHoch : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_S_ManTief : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_S_AutoTief : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_Oeffnung : 24|8@1+ (0.5,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ HFS_FH_Bew_hoch : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_Bew_tief : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_Fang : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_Block : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_Thermo : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_UEKB_aktiviert : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Tueroeffnen_Warnung : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_FH_normiert : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ASW_Warnung_aktiv_HFS : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Zuziehhilfe_aktiv : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Seitenrollo_hoch : 42|1@1+ (1,0) [0|1] "" TME
 SG_ HFS_Seitenrollo_tief : 43|1@1+ (1,0) [0|1] "" TME
 SG_ HFS_Status_KiSi : 44|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SSR_HFS_Pos_Unten : 45|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Oben_Block_erw : 46|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Unten_Block_erw : 47|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MTHFS_M_Taste : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MTHFS_Pos1 : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MTHFS_Pos2 : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MTHFS_Pos3 : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_MRollo_Schalter : 52|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ HFS_Lock_Taster_inv : 55|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_Status_eTAG : 56|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ HFS_Tuer_Status_QBit : 58|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HFS_TCR_Mode_aktiv : 59|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 975 TSG_HBFS_01: 8 Gateway
 SG_ HBFS_Tuer_geoeffnet : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_verriegelt : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_gesafet : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Heckrollotaster_betaetigt : 3|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Tuerschloss_defekt : 4|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Unlock_Taster : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Lock_Taster : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Sperrklinke : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_TAG_betaetigt : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_TIG_betaetigt : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_S_HFS_AutoHoch : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_S_HFS_AutoTief : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_S_HFS_ManHoch : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_S_HFS_ManTief : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Tuer_Status : 14|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ HBFS_SAD_Schalter : 16|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ HBFS_FH_S_ManHoch : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_S_AutoHoch : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_S_ManTief : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_S_AutoTief : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_Oeffnung : 24|8@1+ (0.5,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ HBFS_FH_Bew_hoch : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_Bew_tief : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_Fang : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_Block : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_Thermo : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_UEKB_aktiviert : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Tueroeffnen_Warnung : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_FH_normiert : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ASW_Warnung_aktiv_HBFS : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Zuziehhilfe_aktiv : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Seitenrollo_hoch : 42|1@1+ (1,0) [0|1] "" TME
 SG_ HBFS_Seitenrollo_tief : 43|1@1+ (1,0) [0|1] "" TME
 SG_ HBFS_Status_KiSi : 44|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SSR_HBFS_Pos_Unten : 45|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Oben_Block_erw : 46|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Unten_Block_erw : 47|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MTHBFS_M_Taste : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MTHBFS_Pos1 : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MTHBFS_Pos2 : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MTHBFS_Pos3 : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_MRollo_Schalter : 52|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ HBFS_Lock_Taster_inv : 55|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Status_KiSi_inv : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_Status_eTAG : 57|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ HBFS_Tuer_Status_QBit : 59|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_TIG_betaetigt_schliessen : 60|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 976 TSG_FT_01: 8 Gateway
 SG_ FT_Tuer_geoeffnet : 0|1@1+ (1,0) [0|1] "" Ladegeraet_Konzern,TME
 SG_ FT_verriegelt : 1|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_gesafet : 2|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Schluesselschalter_auf : 3|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV
 SG_ FT_Schluesselschalter_zu : 4|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV
 SG_ FT_Unlock_Taster : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Lock_Taster : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Sperrklinke : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_TAG_betaetigt : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_TIG_betaetigt : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_IRUE_Taste : 10|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_HD_Taste : 11|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_TD_Taste_Fehler : 12|1@1+ (1,0) [0|1] "" Ladegeraet_Konzern
 SG_ FT_TD_Taste : 13|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Ladegeraet_Konzern
 SG_ SSR_Temp_Freigabe : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Sp_Heizung_Anf : 15|1@1+ (100,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ FT_HD_Taste_2 : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_TSG_hinten_verbaut : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Sp_Blk_def : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_S_ManHoch : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_S_AutoHoch : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_S_ManTief : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_S_AutoTief : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_Oeffnung : 24|8@1+ (0.5,0) [0|100] "Unit_PerCent" TME
 SG_ FT_FH_Bew_hoch : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_Bew_tief : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_Fang : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_Block : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_Thermo : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_UEKB_aktiviert : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ASW_HMI_defekt : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_normiert : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Schliesstaster : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Zuziehhilfe_aktiv : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_SP_ausgerastet : 42|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_SP_lr_aktiv : 43|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_SP_ht_aktiv : 44|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ASW_wakeup : 45|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Oben_Block_erw : 46|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Unten_Block_erw : 47|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Kisi_li_aktiv : 48|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Kisi_re_aktiv : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Kisi_Taster_li : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Kisi_Taster_re : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_BFS_Fond_Freigabe : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Kisi_Fehler : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Daemmglas : 54|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_SP_Heizung_Status : 55|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ASW_Warnung_aktiv_FS : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_FH_Pos_oben : 57|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Tuerschloss_defekt : 58|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_SWA_Taster : 59|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SWA_HMI_Diagnose : 60|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ FS_Status_eTAG : 62|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 980 SMLS_01: 8 Gateway
 SG_ SMLS_01_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ SMLS_01_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ BH_Blinker_li : 12|1@1+ (1,0) [0|1] "" ZR_High,ZR_Standard
 SG_ BH_Blinker_re : 13|1@1+ (1,0) [0|1] "" ZR_High,ZR_Standard
 SG_ BH_Lichthupe : 14|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BH_Fernlicht : 15|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WH_Tipwischen : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WH_Intervall : 17|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WH_WischerStufe1 : 18|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WH_WischerStufe2 : 19|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WH_Frontwaschen : 20|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WH_Heckintervall : 21|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WH_Heckwaschen : 22|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WH_Intervallstufen : 23|4@1+ (1,0) [0|15] "" ZR_High
 SG_ FAS_Taster : 27|1@1+ (1,0) [0|1] "" ZR_High,ZR_Standard
 SG_ FAS_Taster_Fehler : 28|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SMLS_Hupe : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LRH_On_Off : 30|2@1+ (1,0) [0|3] "" ZR_High
 SG_ LRH_aktiv : 40|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SMLS_P_verriegelt_plausibel : 43|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WH_SRA : 44|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ WH_Wischer_Fehler : 45|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BH_Blinker_Fehler : 46|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SMLS_PTT : 47|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ WH_Tipwischen_lang : 48|1@1+ (1,0) [0|1] "" ZR_High

BO_ 981 Licht_Anf_01: 8 Gateway
 SG_ Licht_Anf_01_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Licht_Anf_01_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ BCM1_Kurvenlicht_links_Anf : 12|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Kurvenlicht_rechts_Anf : 13|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Standlicht_Anf : 14|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Abblendlicht_Anf : 15|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ BCM1_Fernlicht_Anf : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Nebellicht_Anf : 17|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Parklicht_li_Anf : 18|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Parklicht_re_Anf : 19|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Nebelschluss_Ahg_Anf : 20|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Nebelschluss_Fzg_Anf : 21|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Schlusslicht_Anf : 22|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM_Rueckfahrlicht_Anf : 23|1@1+ (1,0) [0|1] "" ZR_High,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ BCM1_Signaturlicht_Anf : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Umfeldleuchten_Anf : 25|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Tagfahrlicht_Anf : 26|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Regenlicht_Anf : 27|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Autobahnlicht_Anf : 28|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Touristen_Licht_Anf : 29|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_CH_aktiv : 30|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_LH_aktiv : 31|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Gleitende_Leuchtw_Anf : 32|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_GLW_Fernlicht_Anf : 33|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Adaptive_Lichtvert_Anf : 34|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_FoD_Sperrung_WiBli : 35|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BCM1_FOD_Sperrung_Animationen_HL : 37|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BCM1_Animationssperrung : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_CH_LH_aktiv : 40|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Allwetterlicht_Anf : 41|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Schlusslicht_Signatur : 60|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 982 Licht_hinten_01: 8 Gateway
 SG_ BCM2_Bremsl_durch_ECD : 5|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LH_Aussenlicht_def : 7|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Standlicht_H_aktiv : 8|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LH_Parklicht_HL_aktiv : 9|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LH_Parklicht_HR_aktiv : 10|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LH_Bremslicht_H_aktiv : 11|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LH_Nebelschluss_aktiv : 12|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LH_Rueckfahrlicht_aktiv : 13|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LH_Blinker_HL_akt : 14|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LH_Blinker_HR_akt : 15|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LH_Blinker_li_def : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Bremsl_li_def : 17|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Schlusslicht_li_def : 18|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Rueckf_li_def : 19|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Nebel_li_def : 20|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Schluss_Brems_Nebel_li_def : 21|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Schluss_Brems_Nebel_re_def : 22|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Zusatzschlussl_def : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schluss_Brems_li_def : 24|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Schluss_Nebel_li_def : 25|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_SL_BRL_BLK_li_def : 26|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Brems_Blk_li_def : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Diag_Status_re_def : 28|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Diag_Status_li_def : 29|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Diag_LED_li_def : 30|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Diag_LED_re_def : 31|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Blinker_re_def : 32|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Bremsl_re_def : 33|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Schlusslicht_re_def : 34|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Rueckf_re_def : 35|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Nebel_re_def : 36|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Schluss_Brems_mi_def : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Schluss_Brems_re_def : 40|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Schluss_Nebel_re_def : 41|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_SL_BRL_BLK_re_def : 42|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Brems_Blk_re_def : 43|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Kennzl_def : 48|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_3_Bremsl_def : 49|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Nebel_mi_def : 50|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Rueckf_mi_def : 51|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Schlusslicht_mi_def : 52|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Bremsl_mi_def : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LH_Bremsl_li_ges_def : 54|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LH_Bremsl_re_ges_def : 55|1@1+ (1,0) [0|1] "" ZR_High

BO_ 987 Gateway_72: 8 Gateway_MQB
 SG_ BCM_01_alt : 0|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ SMLS_01_alt : 1|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ ZV_02_alt : 2|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ Wischer_01_alt : 3|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ Anhaenger_01_alt : 4|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ Klima_Sensor_02_alt : 5|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ VSG_01_alt : 6|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ Klima_01_alt : 7|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WFS_01_alt : 8|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Licht_Anf_01_alt : 9|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_HFS_offen : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_HBFS_offen : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ VS_VD_offen_ver : 22|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ VS_VD_zu_ver : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_BT_offen : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Rueckfahrlicht_Schalter : 25|1@1+ (1,0) [0|1] "" Airbag_MQB
 SG_ ZV_FT_offen : 26|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ Wischer_vorne_aktiv : 27|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ AAG_Anhaenger_erkannt : 28|1@1+ (1,0) [0|1] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ BCM1_MH_Schalter : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_HD_offen : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Waschen_vorne_aktiv : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_Thermomanagement : 32|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ WFS_Schluessel_Fahrberecht : 34|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ BCM1_RFahrlicht_Fzg_Anf : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_RFahrlicht_Ahg_Anf : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BH_Fernlicht : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BH_Blinker_li : 50|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ BH_Blinker_re : 51|1@1+ (1,0) [0|1] "" Motor_Diesel_MQB,Motor_Otto_MQB
 SG_ BCM1_OBD_FStatus_ATemp : 52|4@1+ (1,0) [0|15] "" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB
 SG_ BCM1_Aussen_Temp_ungef : 56|8@1+ (0.5,-50) [-50|76] "Unit_DegreCelsi" Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB

BO_ 988 Gateway_73: 8 XXX
 SG_ UNKNOWN_1 : 15|2@0+ (1,0) [0|3] "" XXX
 SG_ GE_Fahrstufe : 40|4@1+ (1,0) [0|15] "" XXX
 SG_ EPB_Status : 53|3@1+ (1,0) [0|7] "" XXX
 SG_ UNKNOWN_2 : 58|3@0+ (1,0) [0|7] "" XXX

BO_ 997 TSG_FT_02: 8 Gateway
 SG_ TSG_FT_02_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ TSG_FT_02_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ FT_Tuer_Status : 12|2@1+ (1,0) [0|3] "" OTA_FC,ZR_High
 SG_ FT_Tuer_Status_QBit : 14|1@1+ (1,0) [0|1] "" ZR_High
 SG_ FT_Lock_Taster_02 : 15|1@1+ (1,0) [0|1] "" ZR_High
 SG_ FT_Schluesselschalter_zu_02 : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ FT_BFS_Tuer_Status : 17|2@1+ (1,0) [0|3] "" ZR_High
 SG_ FT_HBFS_Tuer_Status : 19|2@1+ (1,0) [0|3] "" ZR_High
 SG_ FT_HFS_Tuer_Status : 21|2@1+ (1,0) [0|3] "" ZR_High
 SG_ FT_Tueroeffnen_Warnung : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_SP_Heizung_ein : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FT_Kisi_Taster_li_02 : 25|1@1+ (1,0) [0|1] "" ZR_High
 SG_ FT_Kisi_Taster_re_02 : 26|1@1+ (1,0) [0|1] "" ZR_High
 SG_ FT_TD_Taste_Status : 27|2@1+ (1,0) [0|3] "" ZR_High
 SG_ FT_TCR_Mode_aktiv : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FS_Push_Tuergriff : 36|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1004 MEB_TSK_01: 8 XXX
 SG_ TSK_State : 53|3@1+ (1,0) [0|7] "" XXX

BO_ 1031 PLA_04: 8 XXX

BO_ 1122 PSD_04: 8 ZR_High
 SG_ PSD_Segment_ID : 0|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_Vorgaenger_Segment_ID : 6|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_Segmentlaenge : 12|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG,OTA_FC
 SG_ PSD_Strassenkategorie : 19|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG,OTA_FC
 SG_ PSD_Endkruemmung : 22|8@1+ (1,0) [0|254] "Unit_None" Gateway,Gateway_PAG
 SG_ PSD_Endkruemmung_Vorz : 30|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Idenditaets_ID : 31|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_ADAS_Qualitaet : 37|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_wahrscheinlichster_Pfad : 38|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Geradester_Pfad : 39|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Fahrspuren_Anzahl : 40|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG,OTA_FC
 SG_ PSD_Bebauung : 43|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Segment_Komplett : 44|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Rampe : 45|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Anfangskruemmung : 47|8@1+ (1,0) [0|254] "" Gateway,Gateway_PAG
 SG_ PSD_Anfangskruemmung_Vorz : 55|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Abzweigerichtung : 56|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Abzweigewinkel : 57|7@1+ (1.417323,0) [0|180.000021] "" Gateway,Gateway_PAG

BO_ 1123 PSD_05: 8 ZR_High
 SG_ PSD_Pos_Segment_ID : 0|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_Pos_Segmentlaenge : 6|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Pos_Inhibitzeit : 13|5@1+ (10,0) [0|310] "Unit_MilliSecon" Gateway,Gateway_PAG
 SG_ PSD_Pos_Standort_Eindeutig : 18|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Pos_Fehler_Laengsrichtung : 19|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ PSD_Pos_Fahrspur : 22|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG,OTA_FC
 SG_ PSD_Attribut_Segment_ID_05 : 25|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_1_ID : 31|5@1+ (1,0) [1|31] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_1_Wert : 36|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_1_Offset : 40|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Attribut_2_ID : 47|5@1+ (1,0) [1|31] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_2_Wert : 52|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_2_Offset : 56|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Attribute_Komplett_05 : 63|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG

BO_ 1124 PSD_06: 8 ZR_High
 SG_ PSD_06_Mux M : 0|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Segment_ID m0 : 3|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Laendercode m0 : 9|8@1+ (1,0) [0|255] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Geschwindigkeit_Einheit m0 : 17|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Verkehrsrichtung m0 : 18|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Geometrieguete m0 : 19|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Mapmatchingguete m0 : 21|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Alter_Karte m0 : 23|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Zielfuehrung m0 : 26|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG,OTA_FC
 SG_ PSD_Sys_US_State m0 : 27|6@1+ (1,0) [0|63] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Quali_Geometrien m0 : 33|3@1+ (1,0) [0|7] "Unit_None" Gateway,Gateway_PAG
 SG_ PSD_Sys_Quali_Ortsinfo m0 : 36|2@1+ (1,0) [0|3] "Unit_None" Gateway,Gateway_PAG
 SG_ PSD_Sys_Quali_verfuegbar m0 : 38|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Zielfuehrung_geaendert m0 : 39|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Geometrieguete_erweitert m0 : 40|8@1+ (1,0) [0|255] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Quali_sonstige_Attribute m0 : 48|3@1+ (1,0) [0|7] "Unit_None" Gateway,Gateway_PAG
 SG_ PSD_Sys_Quali_Steigungen m0 : 51|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ PSD_Sys_Quali_Strassenkennz m0 : 54|3@1+ (1,0) [0|7] "Unit_None" Gateway,Gateway_PAG
 SG_ PSD_Sys_Quali_Tempolimits m0 : 57|3@1+ (1,0) [0|7] "Unit_None" Gateway,Gateway_PAG
 SG_ PSD_Sys_Quali_Vorfahrtsregelung m0 : 60|3@1+ (1,0) [0|7] "Unit_None" Gateway,Gateway_PAG
 SG_ PSD_Attribut_Segment_ID m1 : 3|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_3_ID m1 : 9|5@1+ (1,0) [1|31] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_3_Offset m1 : 14|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Attribut_3_Wert m1 : 21|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_4_ID m1 : 25|5@1+ (1,0) [1|31] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_4_Wert m1 : 30|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_4_Offset m1 : 34|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Attribut_5_ID m1 : 41|5@1+ (1,0) [1|31] "" Gateway,Gateway_PAG
 SG_ PSD_Attribut_5_Offset m1 : 46|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Attribut_5_Wert m1 : 53|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG
 SG_ PSD_Attribute_Komplett_06 m1 : 57|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Segment_ID m2 : 3|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Offset m2 : 9|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Ges_Geschwindigkeit m2 : 16|5@1+ (1,0) [0|31] "" Gateway,Gateway_PAG,OTA_FC
 SG_ PSD_Ges_Typ m2 : 21|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Spur_Geschw_Begrenzung m2 : 23|6@1+ (1,0) [0|63] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Geschwindigkeit_Gespann m2 : 29|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Geschwindigkeit_Witter m2 : 31|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Geschwindigkeit_Tag_Anf m2 : 33|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Geschwindigkeit_Tag_Ende m2 : 36|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Geschwindigkeit_Std_Anf m2 : 39|5@1+ (1,0) [0|24] "Unit_Hours" Gateway,Gateway_PAG
 SG_ PSD_Ges_Geschwindigkeit_Std_Ende m2 : 44|5@1+ (1,0) [0|24] "Unit_Hours" Gateway,Gateway_PAG
 SG_ PSD_Ges_Ueberholverbot m2 : 49|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Wechselverkehrszeichen m2 : 51|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ PSD_Wechselverkehrszeichen_Typ m2 : 54|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Gesetzlich_Kategorie m2 : 56|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Gesetzlich_Zusatz m2 : 59|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Verkehrszeichen_Quelle m2 : 61|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ PSD_Ges_Attribute_Komplett m2 : 63|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Baum_Laenge_VZ m3 : 3|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Baum_Laenge m3 : 4|25@1+ (1e-05,0) [0|180] "Unit_DegreOfArc" Gateway,Gateway_PAG
 SG_ PSD_Baum_Breite_VZ m3 : 29|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Baum_Breite m3 : 30|24@1+ (1e-05,0) [0|90] "Unit_DegreOfArc" Gateway,Gateway_PAG
 SG_ PSD_Baum_Ausrichtung m3 : 54|10@1+ (0.3515625,0) [0|359.6484375] "Unit_DegreOfArc" Gateway,Gateway_PAG
 SG_ PSD_Steigung_1_Segment_ID m4 : 3|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_Steigung_1_A_Steigung m4 : 9|7@1+ (0.12,0) [0|15] "Unit_PerCent" Gateway,Gateway_PAG
 SG_ PSD_Steigung_1_A_Vorz m4 : 16|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Steigung_1_A_Offset m4 : 17|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Steigung_1_B_Steigung m4 : 24|7@1+ (0.12,0) [0|15] "Unit_PerCent" Gateway,Gateway_PAG
 SG_ PSD_Steigung_1_B_Vorz m4 : 31|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Steigung_1_B_Offset m4 : 32|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Steigung_1_Attribute_kompl m4 : 39|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Steigung_2_Segment_ID m4 : 40|6@1+ (1,0) [2|63] "" Gateway,Gateway_PAG
 SG_ PSD_Steigung_2_Steigung m4 : 46|7@1+ (0.12,0) [0|15] "Unit_PerCent" Gateway,Gateway_PAG
 SG_ PSD_Steigung_2_Vorz m4 : 53|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ PSD_Steigung_2_Offset m4 : 54|7@1+ (2,0) [0|254] "Unit_Meter" Gateway,Gateway_PAG
 SG_ PSD_Steigung_2_Attribute_kompl m4 : 61|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG

BO_ 1153 MainUnit_01: 8 ZR_High
 SG_ ZR_LoGeWa_Event_Kombiwarnung : 0|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG
 SG_ Nav_FoD_Status : 4|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ MIB_Tongenerator_PH_verfuegbar : 9|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ MMI_Counter_Bedienevent : 12|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG
 SG_ DSSS_Warning : 16|5@1+ (1,0) [0|31] "" Gateway,Gateway_PAG
 SG_ ZR_Kindersicherung_RSE : 21|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ ZR_RSE_aktivieren : 22|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ MMI_SDS_aktiv : 23|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ MU_SecondDisplay : 24|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG
 SG_ MMI_Telefon_aktiv : 27|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG
 SG_ MMI_Gurt_Mic_ref : 28|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG,GurtMikrofon
 SG_ ZR_Sta_Inszenierung : 29|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ MMI_Gauges_active : 31|1@1+ (1,0) [0|1] "" BedienDisp_vo,Gateway,Gateway_PAG
 SG_ MU_Update_Time : 32|8@1+ (0.1,0) [0|25.5] "Unit_Hours" Gateway,Gateway_PAG
 SG_ ZR_MXB_Manoever_Ansage : 42|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ ZR_LAPP_Sondermodus_Status : 44|2@1+ (1,0) [0|3] "" DDA,Gateway,Gateway_PAG
 SG_ MMI_StartStopp_Info : 46|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG
 SG_ ZR_Parken_Sondermodus : 48|4@1+ (1,0) [0|15] "" DDA,Gateway,Gateway_PAG
 SG_ ZR_Rundenbewertung : 52|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG
 SG_ ZR_Rundenfortschritt : 56|8@1+ (0.5,0) [0|100] "Unit_PerCent" Gateway,Gateway_PAG

BO_ 1155 Motor_Hybrid_06: 8 Gateway
 SG_ Mo_Powermeter_Grenze : 0|12@1+ (1,0) [0|4092] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ MO_Text_Aktivierung_Antrieb : 12|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ MO_Powermeter_Inszenierung_aktiv : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_Powermeter_Charge_Grenze : 18|10@1+ (1,0) [0|1021] "Unit_None" Vector__XXX
 SG_ MO_Powermeter_Grenze_strategisch : 28|12@1+ (1,0) [0|4093] "Unit_None" Vector__XXX
 SG_ MO_Powermeter_untere_E_Grenze : 40|12@1+ (1,0) [0|4093] "Unit_None" Vector__XXX
 SG_ MO_Powermeter_obere_E_Grenze : 52|12@1+ (1,0) [0|4093] "Unit_None" Vector__XXX

BO_ 1163 BEM_06: 8 XXX
 SG_ CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ CHK : 8|4@1+ (1,0) [0|15] "" XXX

BO_ 1175 Parkhilfe_01: 8 Gateway
 SG_ PH_Visualisierung : 0|3@1+ (1,0) [0|7] "" AWC
 SG_ PDC_Tonausgabe_Front : 4|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PDC_Tonausgabe_Heck : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PH_nachtr_Stopp_Anf : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Abschaltursache : 13|3@1+ (1,0) [0|7] "" AWC
 SG_ PH_Opt_Anzeige_V_ein : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Opt_Anzeige_H_ein : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Opt_Anz_V_Hindernis : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Opt_Anz_H_Hindernis : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Tongeber_V_aktiv : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Tongeber_H_aktiv : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Tongeber_mute : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Anf_Audioabsenkung : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Tongeber_H_verfuegbar : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PLA_Anf_Aufschaltung_RVC : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Taster : 28|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ PH_Anf_Verdeck : 30|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ PH_Frequenz_hinten : 32|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PH_Lautstaerke_hinten : 36|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PH_Frequenz_vorn : 40|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PH_Lautstaerke_vorn : 44|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PH_StartStopp_Info : 49|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ PH_Stoermeldung : 52|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PH_defekt : 56|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_gestoert : 57|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ PH_Systemzustand : 58|3@1+ (1,0) [0|7] "" AWC
 SG_ PH_Display_Kundenwunsch : 61|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 1283 HVK_01: 8 Gateway
 SG_ HVK_01_CRC : 0|8@1+ (1,0) [0|255] "" BMC_MLBevo,DCDC_800V_PAG,DCDC_HV,DCDC_HV_02,FCU_MLBevo_FCEV,Ladegeraet_2,Ladegeraet_Konzern,LE1,LE2,LE_MLBevo,TME
 SG_ HVK_01_BZ : 8|4@1+ (1,0) [0|15] "" BMC_MLBevo,DCDC_800V_PAG,DCDC_HV,DCDC_HV_02,FCU_MLBevo_FCEV,Ladegeraet_2,Ladegeraet_Konzern,LE1,LE2,LE_MLBevo,TME
 SG_ HVK_Istmodus_Anf : 12|1@1+ (1,0) [0|1] "" BMC_MLBevo,DCDC_HV,FCU_MLBevo_FCEV,Ladegeraet_2,Ladegeraet_Konzern,LE1,LE2,LE_MLBevo
 SG_ HVK_TN1_Sollmodus : 13|2@1+ (1,0) [0|3] "" DCDC_HV_02,FCU_MLBevo_FCEV
 SG_ HVK_MO_EmSollzustand : 16|8@1+ (1,0) [0|255] "" FCU_MLBevo_FCEV
 SG_ HVK_BMS_Sollmodus : 24|3@1+ (1,0) [0|7] "" BMC_MLBevo,FCU_MLBevo_FCEV
 SG_ HVK_DCDC_Sollmodus : 27|3@1+ (1,0) [0|7] "" DCDC_800V_PAG,DCDC_HV,FCU_MLBevo_FCEV,LE_MLBevo
 SG_ HVK_EKK_Sollmodus : 30|3@1+ (1,0) [0|7] "" FCU_MLBevo_FCEV,TME
 SG_ HVK_HVPTC_Sollmodus : 33|3@1+ (1,0) [0|7] "" FCU_MLBevo_FCEV,TME
 SG_ HVK_HVLM_Sollmodus : 36|3@1+ (1,0) [0|7] "" DCDC_HV_02,FCU_MLBevo_FCEV,Ladegeraet_Konzern
 SG_ HVK_HV_Netz_Warnungen : 39|2@1+ (1,0) [0|3] "" FCU_MLBevo_FCEV
 SG_ HV_Bordnetz_aktiv : 41|1@1+ (1,0) [0|1] "" DCDC_HV_02,FCU_MLBevo_FCEV,Ladegeraet_Konzern,LE1,LE2,LE_MLBevo
 SG_ HV_Bordnetz_Fehler : 42|1@1+ (1,0) [0|1] "" FCU_MLBevo_FCEV,Ladegeraet_Konzern,TME
 SG_ HVK_Gesamtst_Spgfreiheit : 43|2@1+ (1,0) [0|3] "" FCU_MLBevo_FCEV
 SG_ HVK_AktiveEntladung_Anf : 45|1@1+ (1,0) [0|1] "" DCDC_800V_PAG,DCDC_HV,DCDC_HV_02,FCU_MLBevo_FCEV,LE1,LE2,LE_MLBevo
 SG_ HVK_Iso_Messung_Start : 50|3@1+ (1,0) [0|7] "" BMC_MLBevo,FCU_MLBevo_FCEV
 SG_ HVK_DCDC_EKK_Sollmodus : 62|2@1+ (1,0) [0|3] "" DCDC_800V_PAG,FCU_MLBevo_FCEV

BO_ 1312 Airbag_02: 8 Gateway
 SG_ LoGeWa_Event_Kombiwarnung : 12|4@1+ (1,0) [0|15] "" ZR_High
 SG_ AB_Anforderung_eCall : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ AB_Anprall_Seite_Beifahrer : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AB_Anprall_Rollover : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AB_Anprall_FGS : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AB_Anprall_Front_Beifahrer : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AB_Anprall_Front_Fahrer : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AB_Anprall_Heck_Beifahrer : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AB_Anprall_Heck_Fahrer : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AB_Wickelklappung_Reihe2_MI : 24|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ AB_Belegung_VB : 26|2@1+ (1,0) [0|3] "" BedienSG_hi,GurtMikrofon,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ AB_Abschaltanf_SIH_BF : 28|1@1+ (1,0) [0|1] "" ZR_High
 SG_ AB_Anprall_Seite_Fahrer : 29|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SC_PAO_Schriftzug_Anf : 30|2@1+ (1,0) [0|3] "" ZR_High
 SG_ SC_PAO_ON_Anf : 32|2@1+ (1,0) [0|3] "" ZR_High
 SG_ SC_PAO_OFF_Anf : 34|2@1+ (1,0) [0|3] "" ZR_High
 SG_ AB_Crashschwere : 36|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ AB_Anforderung_USM : 39|1@1+ (1,0) [0|1] "" ZR_Standard
 SG_ AB_Gurtschloss_FA : 40|2@1+ (1,0) [0|3] "" BedienSG_hi,GurtMikrofon,ZR_High
 SG_ AB_Gurtschloss_BF : 42|2@1+ (1,0) [0|3] "" BedienSG_hi,GurtMikrofon,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ AB_Gurtschloss_Reihe2_FA : 44|2@1+ (1,0) [0|3] "" BedienSG_hi,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ AB_Gurtschloss_Reihe2_MI : 46|2@1+ (1,0) [0|3] "" BedienSG_hi,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ AB_Gurtschloss_Reihe2_BF : 48|2@1+ (1,0) [0|3] "" BedienSG_hi,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ AB_Gurtschloss_Reihe3_FA : 50|2@1+ (1,0) [0|3] "" BedienSG_hi,ZR_High
 SG_ AB_Gurtschloss_Reihe3_MI : 52|2@1+ (1,0) [0|3] "" BedienSG_hi,ZR_High
 SG_ AB_Gurtschloss_Reihe3_BF : 54|2@1+ (1,0) [0|3] "" BedienSG_hi,ZR_High
 SG_ AB_Sitzpos_Sens_FA : 56|2@1+ (1,0) [0|3] "" ZR_High
 SG_ AB_Sitzpos_Sens_BF : 58|2@1+ (1,0) [0|3] "" ZR_High
 SG_ AB_Wickelklappung_Reihe2_BF : 60|2@1+ (1,0) [0|3] "" ZR_High
 SG_ AB_Wickelklappung_Reihe2_FA : 62|2@1+ (1,0) [0|3] "" ZR_High

BO_ 1349 Airbag_04: 8 Gateway
 SG_ AB_Gurtwarn_Reihe2_FA : 20|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ AB_Gurtwarn_Reihe2_BF : 22|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ AbstWarn_MV_FAS_Fkt_Status : 24|2@1+ (1,0) [0|3] "" ZR_High
 SG_ WarnBrems_Charisma_Status : 26|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ WarnBrems_Charisma_FahrPr : 28|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AB_Gurtwarn_Reihe2_MI : 32|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ AB_Gurtwarn_Reihe3_FA : 34|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ AB_Gurtwarn_Reihe3_MI : 36|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ AB_Gurtwarn_Reihe3_BF : 38|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ LGI_FAS_Fkt_Status : 40|2@1+ (1,0) [0|3] "" ZR_High
 SG_ PreCrash_FAS_Fkt_Status : 42|3@1+ (1,0) [0|7] "" ZR_High
 SG_ AB_SBR_hinten_verbau : 48|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ AWV_Einstellung_System_ASG : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AWV_Einstellung_Warnung_ASG : 52|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ SC_PreSense_Modus_Warnung_NV : 55|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ SC_PreSense_Modus_Warnung_MV : 58|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ SC_PreSense_Modus_System_MV : 61|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SC_PreSense_Modus_System_NV : 62|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ SC_PreSense_Modus_System_KAS : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1355 Parkhilfe_04: 8 Gateway
 SG_ PH_Verschmutzungsmeldung : 12|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ PH_Aufschaltursache : 16|5@1+ (1,0) [0|31] "" AWC
 SG_ PH_Ton_Ausgabe : 21|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ PH_Ton_Pausenlaenge : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ PH_Ton_Richtung : 49|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ PH_Ton_Lautstaerke : 52|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ BCM_WAH_Meldung : 55|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ PDC_Charisma_Status : 58|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ PDC_Charisma_FahrPr : 60|4@1+ (1,0) [0|15] "" Vector__XXX

BO_ 1361 WFS_01: 8 Gateway
 SG_ WFS_Schluessel_Fahrberecht : 0|4@1+ (1,0) [0|15] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ WFS_ID_Geb_autorisiert : 5|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WFS_ID_Geb_autorisiert_in_Kl15 : 6|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WFS_ID_Geb_steckt : 7|1@1+ (1,0) [0|1] "" ZR_High
 SG_ WFS_Schluessel_Soll : 8|4@1+ (1,0) [0|15] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ WFS_Schluessel_Ist : 12|4@1+ (1,0) [0|15] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ WFS_Safe : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WFS_LZ : 17|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ WFS_ELV_authorisiert : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WFS_LF_Aktiv : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WFS_Betrieb_Lesespule : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WFS_Klemmenfreigabe : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ WFS_Fahrerhinweise : 34|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ WFS_Parken_Status : 36|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 1385 MEB_HVEM_03: 8 XXX
 SG_ NEW_SIGNAL_12 : 0|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_1 : 1|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_10 : 3|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 4|2@1+ (1,0) [0|3] "" XXX
 SG_ PTC_ON : 7|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_3 : 25|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_13 : 32|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_4 : 33|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_11 : 35|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_5 : 36|2@1+ (1,0) [0|3] "" XXX
 SG_ NEW_SIGNAL_6 : 38|1@0+ (1,0) [0|1] "" XXX
 SG_ PTC_ein_02 : 39|1@0+ (1,0) [0|1] "" XXX
 SG_ PTC_ein_03 : 57|1@0+ (1,0) [0|1] "" XXX
 SG_ PTC_ein_04 : 59|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_9 : 61|2@1+ (1,0) [0|3] "" XXX

BO_ 1411 ZV_02: 8 Gateway
 SG_ BCM_FH_Freigabe : 12|1@1+ (1,0) [0|1] "" BedienDisp_vo
 SG_ BCM_Komfortfkt_Freigabe : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_HSK_Freigabe : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM_Verdeck_Freigabe : 15|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_verriegelt_intern_ist : 16|1@1+ (1,0) [0|1] "" DDA,OTA_FC,ZR_High
 SG_ ZV_verriegelt_extern_ist : 17|1@1+ (1,0) [0|1] "" DDA,OTA_FC,ZR_High
 SG_ ZV_verriegelt_intern_soll : 18|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_verriegelt_extern_soll : 19|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZV_gesafet_extern_ist : 20|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_gesafet_extern_soll : 21|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3
 SG_ ZV_Einzeltuerentriegelung : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_Heckeinzelentriegelung : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_FT_offen : 24|1@1+ (1,0) [0|1] "" DDA,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZV_BT_offen : 25|1@1+ (1,0) [0|1] "" DDA,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZV_HFS_offen : 26|1@1+ (1,0) [0|1] "" DDA,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZV_HBFS_offen : 27|1@1+ (1,0) [0|1] "" DDA,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZV_HD_offen : 28|1@1+ (1,0) [0|1] "" DDA,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ ZV_HS_offen : 29|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ IRUE_aktiv : 30|1@1+ (1,0) [0|1] "" ZR_High
 SG_ DWA_aktiv : 31|1@1+ (1,0) [0|1] "" ZR_High
 SG_ HD_Hauptraste : 32|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ HD_Vorraste : 33|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ FFB_CarFinder : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FFB_Komfortoeffnen : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ FFB_Komfortschliessen : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_Schluessel_Zugang : 42|4@1+ (1,0) [0|15] "" ZR_High
 SG_ ZV_SafeFunktion_aktiv : 46|1@1+ (1,0) [0|1] "" ZR_High
 SG_ FBS_Warn_Schluessel_Batt : 47|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_Oeffnungsmodus : 48|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ HFS_verriegelt : 50|1@1+ (1,0) [0|1] "" OTA_FC
 SG_ HFS_gesafet : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HBFS_verriegelt : 52|1@1+ (1,0) [0|1] "" OTA_FC
 SG_ HBFS_gesafet : 53|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ZV_ist_Zustand_verfuegbar : 54|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ IRUE_Taster_Fkts_LED : 55|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_Tankklappe_offen : 56|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_Rollo_auf : 57|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_Rollo_zu : 58|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_SAD_auf : 59|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_SAD_zu : 60|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM_Tankklappensteller_Fehler : 61|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ZV_verriegelt_soll : 62|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 1413 Systeminfo_01: 8 Gateway
 SG_ SI_Sammel_SG_Fehler : 0|6@1+ (1,0) [0|60] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_Diagnose_Aktiv : 7|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_QRS_Mode : 8|1@1+ (1,0) [0|1] "" BedienSG_hi,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_T_Mode : 9|1@1+ (1,0) [0|1] "" BedienDisp_vo,BedienSG_hi,DDA,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_NWDF : 10|1@1+ (1,0) [0|1] "" BedienDisp_vo,BedienSG_hi,DDA,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_NWDF_gueltig : 11|1@1+ (1,0) [0|1] "" BedienDisp_vo,BedienSG_hi,DDA,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_Sammelfehler : 12|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ GW_KD_Fehler : 13|1@1+ (1,0) [0|1] "" ZR_High,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_T_Schutz : 14|1@1+ (1,0) [0|1] "" BedienSG_hi,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_01 : 16|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_02 : 17|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_03 : 18|1@1+ (1,0) [0|1] "" BedienSG_hi,DDA,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_04 : 19|1@1+ (1,0) [0|1] "" BedienSG_hi,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_05 : 20|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_06 : 21|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_07 : 22|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_08 : 23|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_09 : 24|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_10 : 25|1@1+ (1,0) [0|1] "" BedienSG_hi,DDA,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_11 : 26|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_12 : 27|1@1+ (1,0) [0|1] "" DDA,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_13 : 28|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_14 : 29|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_BUS_15 : 30|1@1+ (1,0) [0|1] "" ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_Bus_Identifikation : 32|8@1+ (1,0) [0|255] "" OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard
 SG_ SI_CAB : 40|24@1+ (1,0) [0|16777215] "" BedienSG_hi,OTA_FC,ZR_High,ZR_LIMU,ZR_MIB_TOP_ab_Gen3,ZR_Standard

BO_ 1420 Klemmen_Steuerung_01: 8 Gateway
 SG_ Klemmen_Steuerung_01_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Klemmen_Steuerung_01_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ KST_Txt_P_Gang : 16|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KST_Txt_Panikabschaltung : 17|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KST_Anf_Klemmenfreigabe_ELV : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KST_Txt_Komfortabschaltung : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KST_ZAT_betaetigt : 20|1@1+ (1,0) [0|1] "" ZR_High
 SG_ KST_Unterdr_Zuendungsmeldung : 21|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KST_aut_Abschaltung_Zuendung : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KST_Anf_ZV_Verriegelung : 24|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ Relais_VoKo_angesteuert : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ RSt_Anforderung_HMS : 26|3@1+ (1,0) [0|7] "" Vector__XXX

BO_ 1426 Kessy_04: 8 XXX

BO_ 1440 RLS_01: 8 Gateway
 SG_ LS_Helligkeit_IR : 0|8@1+ (400,0) [0|101200] "Unit_Lux" ZR_High
 SG_ LS_Helligkeit_FW : 8|10@1+ (6,0) [0|6126] "Unit_Lux" ZR_High
 SG_ LS_defekt : 22|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LS_Verbau : 23|1@1+ (1,0) [0|1] "" ZR_High
 SG_ RS_Regenmenge : 24|4@1+ (10,0) [0|100] "Unit_PerCent" OTA_FC,ZR_High
 SG_ RS_Verbau : 29|1@1+ (1,0) [0|1] "" ZR_High
 SG_ RS_Verglasung_schliessen : 30|1@1+ (1,0) [0|1] "" ZR_High
 SG_ RS_defekt : 31|1@1+ (1,0) [0|1] "" ZR_High
 SG_ RS_Wischergeschwindigkeit : 32|3@1+ (1,0) [0|7] "" OTA_FC,ZR_High
 SG_ RLS_Vorfeldhelligkeit_Boost : 35|4@1+ (1,0) [0|15] "" ZR_High

BO_ 1442 BMS_04: 8 BMC_MLBevo
 SG_ BMS_04_CRC : 0|8@1+ (1,0) [0|255] "" Gateway,Gateway_PAG,Sub_Gateway
 SG_ BMS_04_BZ : 8|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG,Sub_Gateway
 SG_ BMS_Status_ServiceDisconnect : 13|1@1+ (1,0) [0|1] "" DCDC_800V_PAG,DCDC_HV,Gateway,Gateway_PAG,Sub_Gateway
 SG_ BMS_Status_Spgfreiheit : 14|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG,Sub_Gateway
 SG_ BMS_OBD_Lampe_Anf : 16|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG,Sub_Gateway
 SG_ BMS_IstModus : 17|3@1+ (1,0) [0|7] "" AWC,Gateway,Gateway_PAG,Ladegeraet_Konzern,LE1,LE2,LE2_3_MLBevo_LB,Sub_Gateway,TME
 SG_ BMS_Fehlerstatus : 20|3@1+ (1,0) [0|7] "" AWC,DCDC_800V_PAG,DCDC_HV,Gateway,Gateway_PAG,Ladegeraet_Konzern,Sub_Gateway
 SG_ BMS_Kapazitaet_02 : 23|11@1+ (0.2,0) [0|409.2] "Unit_AmperHour" Gateway,Gateway_PAG,Ladegeraet_Konzern,Sub_Gateway
 SG_ BMS_Soll_SOC_HiRes : 53|11@1+ (0.05,0) [0|100] "Unit_PerCent" Gateway,Gateway_PAG,Sub_Gateway

BO_ 1447 TM_01: 8 Gateway
 SG_ TM_Spiegel_Anklappen : 47|1@1+ (1,0) [0|1] "" ZR_High
 SG_ TM_Nur_Hupen : 48|1@1+ (1,0) [0|1] "" ZR_High
 SG_ TM_Door_Lock : 49|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TM_Door_Unlock : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TM_Warnblinken : 51|1@1+ (1,0) [0|1] "" ZR_High
 SG_ TM_Panik_Alarm : 52|1@1+ (1,0) [0|1] "" ZR_High
 SG_ TM_ZV_Signatur : 53|11@1+ (1,0) [1|2047] "Unit_None" Vector__XXX

BO_ 1452 HVEM_02: 8 Gateway
 SG_ HVEM_IstStrom_HVVerbraucher : 0|12@1+ (0.1,-204.7) [-204.7|204.6] "Unit_Amper" Vector__XXX
 SG_ HVEM_Energie_Klima_Vorgabe_HighR : 12|3@1+ (10,0) [0|50] "Unit_WattHour" TME
 SG_ HVEM_IstLeistungNA : 15|9@1+ (50,0) [0|25450] "Unit_Watt" Vector__XXX
 SG_ HVEM_Leistung_Klima_Vorgabe : 24|8@1+ (50,0) [0|12650] "Unit_Watt" TME
 SG_ HVEM_Nutzbare_Energie : 32|11@1+ (50,0) [0|102200] "Unit_WattHour" Ladegeraet_Konzern
 SG_ HVEM_Energie_Klima_Vorgabe : 43|8@1+ (50,0) [0|12650] "Unit_WattHour" TME
 SG_ HVEM_MO_MaxLeistungIgnoriert : 63|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1485 DCDC_03: 8 DCDC_800V_PAG
 SG_ DCDC_03_CRC : 0|8@1+ (1,0) [0|255] "" Gateway,Gateway_PAG,Sub_Gateway
 SG_ DCDC_03_BZ : 8|4@1+ (1,0) [0|15] "" Gateway,Gateway_PAG,Sub_Gateway
 SG_ DC_Fehlerstatus : 16|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG,Sub_Gateway
 SG_ DC_Peakstrom_verfuegbar : 19|1@1+ (1,0) [0|1] "" Gateway,Gateway_PAG,Sub_Gateway
 SG_ DC_Abregelung_Temperatur : 20|1@1+ (1,0) [0|1] "" Gateway_PAG,Sub_Gateway,TME
 SG_ DC_IstModus_02 : 21|3@1+ (1,0) [0|7] "" DCDC_HV_02,Gateway,Gateway_PAG,Ladegeraet_Konzern,Sub_Gateway,TME
 SG_ DC_HV_EKK_IstModus : 28|3@1+ (1,0) [0|7] "" Gateway,Gateway_PAG,Sub_Gateway,TME
 SG_ DC_Status_Spgfreiheit_HV : 46|2@1+ (1,0) [0|3] "" Gateway,Gateway_PAG,Sub_Gateway
 SG_ DC_IstSpannung_EKK_HV : 48|8@1+ (2,0) [0|508] "Unit_Volt" Gateway,Gateway_PAG,Sub_Gateway
 SG_ DC_Temperatur : 56|8@1+ (1,-40) [-40|213] "Unit_DegreCelsi" Gateway,Gateway_PAG,Sub_Gateway,TME

BO_ 1505 Klima_Sensor_02: 8 Gateway
 SG_ BCM1_Aussen_Temp_ungef : 0|8@1+ (0.5,-50) [-50|76] "Unit_DegreCelsi" BMC_MLBevo,BMS_NV,FCU_MLBevo_FCEV,Ladegeraet_Konzern,TME
 SG_ BCM_Heizungsabsperrventil_Status : 8|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BCM_Heizungspumpe_Status : 10|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BCM_Kompressorkupplung_Status : 12|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BCM1_PTC_stufig_Status : 28|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ BCM1_FStatus_Aussentemp_ungef : 31|1@1+ (1,0) [0|1] "" TME
 SG_ BCM1_Kompressorstrom_ist : 32|8@1+ (4,0) [0|1000] "Unit_MilliAmper" Vector__XXX
 SG_ BCM1_OBD_FStatus_ATemp : 44|4@1+ (1,0) [0|15] "" BMC_MLBevo,Ladegeraet_Konzern,TME

BO_ 1513 Klima_Sensor_04: 8 Gateway
 SG_ DS_Kaeltemittel_P : 8|11@1+ (0.0161,0) [0|32.9245] "Unit_Bar" TME
 SG_ DS_Status : 19|2@1+ (1,0) [0|3] "" TME
 SG_ ION_Status : 21|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ ION_Status_LED : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ AAU_Geblaese : 24|7@1+ (1,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ ION_Status_Taster : 31|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1520 Dimmung_01: 8 Gateway_MQB
 SG_ DI_KL_58xd : 0|8@1+ (1,0) [0|253] "" Airbag_MQB
 SG_ DI_KL_58xs : 8|7@1+ (1,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ DI_Display_Nachtdesign : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ DI_KL_58xt : 16|7@1+ (1,0) [0|100] "Unit_PerCent" Vector__XXX
 SG_ DI_Fotosensor : 24|16@1+ (1,0) [0|65535] "" Vector__XXX

BO_ 1524 Innenlicht_11: 8 Gateway
 SG_ IL_Dimmung_V_Tuerkontur : 0|8@1+ (1,0) [0|100] "Unit_PerCent" ZR_High
 SG_ IL_Dimmung_H_Tuerkontur : 8|8@1+ (1,0) [0|100] "Unit_PerCent" ZR_High
 SG_ IL_Dimmung_Tuerinnengriff : 16|8@1+ (1,0) [0|100] "Unit_PerCent" ZR_High
 SG_ IL_Dimmung_Umfeldbel : 24|8@1+ (1,0) [0|100] "Unit_PerCent" ZR_High
 SG_ IL_Bel_FS_Ausstieg : 32|1@1+ (1,0) [0|1] "" ZR_High
 SG_ IL_Bel_BFS_Ausstieg : 33|1@1+ (1,0) [0|1] "" ZR_High
 SG_ IL_Bel_HFS_Ausstieg : 34|1@1+ (1,0) [0|1] "" ZR_High
 SG_ IL_Bel_HBFS_Ausstieg : 35|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Innenlicht_gedimmt_V : 36|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Innenlicht_gedimmt_H : 37|1@1+ (1,0) [0|1] "" ZR_High
 SG_ IL_Innenlicht_aktiv : 38|1@1+ (1,0) [0|1] "" ZR_High
 SG_ IL_Klemme_30G_aktiv : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_DI_Rampe_Innenlicht : 40|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_DI_Rampe_Leselicht : 41|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Innenlicht_H : 42|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Innenlicht_V : 43|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Leselicht_Anf_hl : 44|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Leselicht_Anf_hr : 45|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Leselicht_Anf_vl : 46|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Leselicht_Anf_vr : 47|1@1+ (1,0) [0|1] "" ZR_High
 SG_ BCM1_Leuchten_Aus : 48|1@1+ (1,0) [0|1] "" ZR_High
 SG_ AMB_Charisma_FahrPr : 49|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ AMB_Charisma_Status : 53|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ IL_Dimmung_Lautspr : 56|8@1+ (1,0) [0|100] "Unit_PerCent" ZR_High

BO_ 1600 Motor_07: 8 Motor_Diesel_MQB
 SG_ MO_QBit_Ansaugluft_Temp : 0|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_QBit_Oel_Temp : 1|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_QBit_Kuehlmittel_Temp : 2|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ MO_Stellgliedtest_Soundaktuator : 3|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_HYB_Fehler_HV_Netz : 4|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_aktives_Getriebeheizen : 5|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Absperrventil_oeffnen : 6|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Ansaugluft_Temp : 8|8@1+ (0.75,-48) [-48|141.75] "Unit_DegreCelsi" Gateway_MQB
 SG_ MO_Oel_Temp : 16|8@1+ (1,-60) [-60|192] "Unit_DegreCelsi" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Kuehlmittel_Temp : 24|8@1+ (0.75,-48) [-48|141.75] "Unit_DegreCelsi" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB
 SG_ MO_Hoeheninfo : 32|8@1+ (0.00781,0) [0|1.98374] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Kennfeldk : 40|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Versionsinfo : 41|6@1+ (1,0) [0|63] "" Gateway_MQB
 SG_ MO_Getriebe_kuehlen : 47|1@1+ (1,0) [0|1] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Mom_Traegheit_02 : 48|5@1+ (0.01,0) [0|0.31] "Unit_KiloGramMeterSquar" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Heizungspumpenansteuerung : 53|4@1+ (10,0) [0|100] "Unit_PerCent" Gateway_MQB
 SG_ MO_SpannungsAnf : 57|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Nachlaufzeit_Heizungspumpe : 58|6@1+ (15,0) [0|945] "Unit_Secon" Gateway_MQB

BO_ 1601 Motor_Code_01: 8 Motor_Diesel_MQB
 SG_ Motor_Code_01_CRC : 0|8@1+ (1,0) [0|255] "" Gateway_MQB
 SG_ Motor_Code_01_BZ : 8|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_Faktor_Momente_02 : 12|2@1+ (1,0) [0|3] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Hybridfahrzeug : 14|2@1+ (1,0) [0|3] "" Gateway_MQB
 SG_ MO_Code : 16|8@1+ (1,0) [0|255] "" Gateway_MQB,SAK_MQB
 SG_ MO_Getriebe_Code : 24|6@1+ (1,0) [0|63] "" Gateway_MQB
 SG_ MO_StartStopp_Codiert : 30|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Anzahl_Zyl : 32|4@1+ (1,0) [0|15] "" Gateway_MQB
 SG_ MO_Kraftstoffart : 36|4@1+ (1,0) [0|15] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Hubraum : 40|7@1+ (0.1,0) [0|12.7] "Unit_Liter" Gateway_MQB
 SG_ MO_Ansaugsystem : 47|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ MO_Leistung : 48|9@1+ (1,0) [0|511] "Unit_KiloWatt" Gateway_MQB
 SG_ MO_Abgastyp_EOBD : 57|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB,LEH_MQB
 SG_ MO_Abgastyp_OBD : 58|1@1+ (1,0) [0|1] "" BMS_MQB,Gateway_MQB,LEH_MQB
 SG_ MO_DPF_verbaut : 59|1@1+ (1,0) [0|1] "" Gateway_MQB
 SG_ TSK_Codierung : 60|3@1+ (1,0) [0|7] "" Gateway_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB
 SG_ MO_Einspritzart : 63|1@1+ (1,0) [0|1] "" Gateway_MQB

BO_ 1603 Einheiten_01: 8 Gateway
 SG_ KBI_Einheit_Datum : 0|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KBI_Einheit_Druck : 2|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KBI_Einheit_Streckenanz : 4|1@1+ (1,0) [0|1] "" Ladegeraet_Konzern
 SG_ KBI_MFA_v_Einheit_02 : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KBI_Einheit_Temp : 6|1@1+ (1,0) [0|1] "" TME
 SG_ KBI_Einheit_Uhrzeit : 7|1@1+ (1,0) [0|1] "" Ladegeraet_Konzern
 SG_ KBI_Einheit_Verbrauch : 8|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KBI_Einheit_Volumen : 10|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KBI_Einheit_Verbrauch_elektr : 12|3@1+ (1,0) [0|7] "" Vector__XXX
 SG_ KBI_Einheit_Sprache : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ KBI_Einheit_Verbrauch_Gas : 24|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ KBI_Einheit_Masse : 28|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 1622 ELV_01: 8 Gateway
 SG_ ELV_01_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ ELV_01_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ ELV_Anf_Klemme_S : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Anf_Klemme_15 : 13|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Anf_Klemme_50 : 14|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_01_Sendestatus : 15|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Verriegelt : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ELV_Entriegelt : 17|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ELV_ZAT_betaetigt : 18|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Lebenszustand : 19|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Anlernmodus : 20|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Klemmenfreigabe : 22|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Abbruch_Anf_Klemmenfreigabe : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_LED_Rot : 24|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ELV_LED_Gelb : 25|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Txt_Panikabschaltung : 27|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Txt_Lkg_Bewegen : 28|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ELV_Txt_Werkstatt : 29|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ELV_Txt_Defekt : 30|1@1+ (1,0) [0|1] "" ZR_High
 SG_ ELV_Txt_P_Gang : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Txt_PN_Gang : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Txt_Kupplung : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_Txt_Bremse : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ELV_P_verriegelt : 35|1@1+ (1,0) [0|1] "" Vector__XXX

BO_ 1624 Licht_vorne_01: 8 Gateway
 SG_ Licht_Vorne_01_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ Licht_Vorne_01_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ LV_Standlicht_Anzeige : 12|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_Abblendlicht_Anzeige : 13|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_Fernlicht_Anzeige : 14|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_Nebellicht_Anzeige : 15|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_Nebelschlusslicht_Anzeige : 16|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_Tagfahrlicht_Anzeige : 17|1@1+ (1,0) [0|1] "" BedienSG_hi,OTA_FC,ZR_High
 SG_ LV_AFL_aktiv_Anzeige : 18|1@1+ (1,0) [0|1] "" BedienSG_hi,OTA_FC,ZR_High
 SG_ LV_AFL_defekt : 19|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Blinker_li_def : 20|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Standlicht_li_def : 21|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Abblendlicht_li_def : 22|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Fernlicht_li_def : 23|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Nebellicht_li_def : 24|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Blk_li_Seite_def : 25|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Tagfahrlicht_li_def : 26|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_FLA_aktiv_Anzeige : 27|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_FLA_defekt : 28|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_FLA_Sensor_blockiert : 29|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_Blinker_re_def : 30|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Standlicht_re_def : 31|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Abblendlicht_re_def : 32|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Fernlicht_re_def : 33|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Nebellicht_re_def : 34|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Blk_re_Seite_def : 35|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Tagfahrlicht_re_def : 36|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Aussenlicht_def : 37|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Abblendlicht_TFL_li_def : 38|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Nebellicht_TFL_li_def : 39|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Standlicht_TFL_li_def : 40|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Abblend_Fernlicht_li_def : 41|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Abblendlicht_TFL_re_def : 42|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Nebellicht_TFL_re_def : 43|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Standlicht_TFL_re_def : 44|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Abblend_Fernlicht_re_def : 45|1@1+ (1,0) [0|1] "" ZR_High
 SG_ LV_Abbiegelicht_li_def : 46|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LV_Abbiegelicht_re_def : 47|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ BCM1_Linksverkehr : 48|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ BCM1_Licht_Dunkelheit_aktiv : 49|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_LED_Scheinwerfer_li_def : 50|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LV_LED_Scheinwerfer_re_def : 51|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ LV_Blinker_VL_aktiv : 52|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_Blinker_VR_aktiv : 53|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ LV_MXB_Status_Anzeige : 54|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 1629 ESP_20: 8 Gateway
 SG_ ESP_20_CRC : 0|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ ESP_20_BZ : 8|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ BR_Systemart : 12|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ ESP_SpannungsAnf_02 : 14|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ ESP_Zaehnezahl : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ ESP_Charisma_FahrPr : 24|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ ESP_Charisma_Status : 28|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ ESP_Wiederstart_Anz_01 : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Wiederstart_Anz_02 : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Wiederstart_Anz_03 : 32|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Wiederstart_Anz_04 : 33|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Stoppverbot_Anz_01 : 34|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Stoppverbot_Anz_02 : 35|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Stoppverbot_Anz_03 : 36|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Stoppverbot_Anz_04 : 37|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Stoppverbot_Anz_05 : 38|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Stoppverbot_Anz_06 : 39|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Stoppverbot_Anz_07 : 40|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Stoppverbot_Anz_Std : 41|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ ESP_Dachrelingsensor : 42|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ ESP_Stoppverbot_Anz_08 : 44|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ HDC_Charisma_FahrPr : 45|4@1+ (1,0) [0|15] "" Vector__XXX
 SG_ HDC_Charisma_Status : 49|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ BR_QBit_Reifenumfang : 51|1@1+ (1,0) [0|1] "" AWC
 SG_ BR_Reifenumfang : 52|12@1+ (1,0) [0|4095] "Unit_MilliMeter" AWC

BO_ 1631 Motor_16: 8 Gateway
 SG_ TSK_Grundmasse : 0|8@1+ (32,0) [0|8128] "Unit_KiloGram" ZR_High
 SG_ TSK_QBit_Steigung : 12|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TSK_QBit_Fahrzeugmasse : 13|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MO_SpannungsAnf_02 : 14|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ MO_DPF_reg : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ MO_Heizstrom_EKAT : 17|7@1+ (1,0) [0|126] "Unit_Amper" Vector__XXX
 SG_ MO_Heizstrom_SCR : 24|6@1+ (1,0) [0|62] "Unit_Amper" Vector__XXX
 SG_ MO_Anzeige_Kaltleuchte : 30|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ MO_P_Generator_ungefiltert_Anf : 31|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ TSK_Getriebeinfo : 34|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ MO_Energieinhalt_BMS : 36|12@1+ (25,0) [0|102325] "Unit_WattHour" Vector__XXX
 SG_ TSK_Fahrzeugmasse_02 : 48|8@1+ (32,0) [0|8128] "Unit_KiloGram" OTA_FC,ZR_High
 SG_ TSK_Steigung_02 : 56|8@1+ (0.8,-101.6) [-100.8|101.6] "Unit_PerCent" OTA_FC

BO_ 1640 Klima_12: 8 Gateway
 SG_ KL_LRH_Taster : 0|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_LRH_Stufe : 1|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ HSH_Taster : 3|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ FSH_Taster : 5|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ KL_Zuheizer_Freigabe : 6|1@1+ (1,0) [0|1] "" TME
 SG_ KL_Beschlagsgefahr : 7|1@1+ (1,0) [0|1] "" TME
 SG_ KL_SIH_Soll_li : 8|3@1+ (1,0) [0|7] "" TME
 SG_ KL_SIH_Soll_re : 11|3@1+ (1,0) [0|7] "" TME
 SG_ KRH_Soll_li : 14|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KL_SIL_Soll_li : 16|3@1+ (1,0) [0|7] "" TME
 SG_ KL_SIL_Soll_re : 19|3@1+ (1,0) [0|7] "" TME
 SG_ KRH_Soll_re : 22|2@1+ (1,0) [0|3] "" Vector__XXX
 SG_ KL_Geblspng_Soll : 24|8@1+ (0.05,1.45) [1.5|14] "Unit_Volt" Vector__XXX
 SG_ KL_Geblspng_Fond_Soll : 32|8@1+ (0.05,1.45) [1.5|14] "Unit_Volt" Vector__XXX
 SG_ KL_I_Geblaese : 40|8@1+ (0.25,0) [0|63.5] "Unit_Amper" Vector__XXX
 SG_ KL_Kompressorstrom_soll : 48|10@1+ (1,0) [0|1021] "" TME
 SG_ KL_Umluftklappe_Status : 58|4@1+ (1,0) [0|15] "" TME
 SG_ KL_PTC_Verbauinfo : 62|2@1+ (1,0) [0|3] "" Vector__XXX

BO_ 1648 Motor_18: 8 Gateway
 SG_ MO_max_Ladedruck : 0|6@1+ (0.1,0) [0|6.3] "Unit_Bar" Sub_Gateway
 SG_ MO_ANC_Kennfeld_Anf : 6|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ MO_Bremslicht_Reku : 8|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_StartStopp_PopUp : 9|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ MO1_Sperr_Info_WFS : 11|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO1_Freigabe_Info_WFS : 12|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_EPCL : 13|3@1+ (1,0) [0|7] "" Sub_Gateway
 SG_ MO_Zylabsch_Texte_02 : 16|4@1+ (1,0) [0|15] "" Sub_Gateway
 SG_ MO_Fahrzeugtyp : 20|3@1+ (1,0) [0|7] "" BMS_NV,Sub_Gateway
 SG_ MO_NMAX_Schaltanzeige : 23|9@1+ (25,0) [0|12775] "Unit_MinutInver" Sub_Gateway
 SG_ MO_Abstellzeit : 32|8@1+ (8,0) [0|2024] "Unit_Minut" BMS_NV,Sub_Gateway,TME
 SG_ MO_Abstellzeit_Status : 40|2@1+ (1,0) [0|3] "" BMS_NV,Sub_Gateway,TME
 SG_ MO1_Freigabe_Verfallsinfo_WFS : 42|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_Hybrid_StartStopp_LED : 43|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ MO_Fehler_Zylabsch : 45|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ MO_Anzahl_Abgesch_Zyl : 47|3@1+ (1,0) [0|7] "" Sub_Gateway
 SG_ MO_Zylabsch_Texte : 50|2@1+ (1,0) [0|3] "" Sub_Gateway
 SG_ MO_Ethanol_BS_Texte : 52|3@1+ (1,0) [0|7] "" Sub_Gateway
 SG_ MO_Drehzahl_Warnung : 55|1@1+ (1,0) [0|1] "" Sub_Gateway
 SG_ MO_obere_Drehzahlgrenze : 56|8@1+ (50,0) [50|12750] "Unit_MinutInver" Sub_Gateway

BO_ 1710 Spiegel_01: 8 Gateway
 SG_ SP_FT_oben : 0|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_FT_unten : 1|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_FT_links : 2|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_FT_rechts : 3|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_BT_oben : 4|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_BT_unten : 5|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_BT_links : 6|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_BT_rechts : 7|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_abklappen : 8|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_anklappen : 9|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_normieren : 10|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_Hzg_Taster : 12|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ SP_S_oben : 16|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SP_S_unten : 17|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SP_S_links : 18|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SP_S_rechts : 19|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SP_Auswahl_li : 20|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SP_Auswahl_re : 21|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SP_ARA_Status : 22|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SP_S_Klappen : 23|1@1+ (1,0) [0|1] "" ZR_High
 SG_ SP_Verstellschalter_Fehler : 24|1@1+ (1,0) [0|1] "" ZR_High

BO_ 1711 Rear_View_01: 8 Gateway
 SG_ RV_Video_on : 0|1@1+ (1,0) [0|1] "" ZR_High,ZR_Standard
 SG_ RV_Dark_Screen : 1|1@1+ (1,0) [0|1] "" ZR_High,ZR_Standard
 SG_ RV_HMI_Mode : 2|2@1+ (1,0) [0|3] "" ZR_High,ZR_Standard
 SG_ RV_GL_side : 4|2@1+ (1,0) [0|3] "" ZR_High,ZR_Standard
 SG_ RV_System_aktiv : 6|1@1+ (1,0) [0|1] "" ZR_High,ZR_Standard
 SG_ RV_Reinigung_Anf : 7|1@1+ (1,0) [0|1] "" OTA_FC,ZR_High
 SG_ RV_Settings_enabled : 8|1@1+ (1,0) [0|1] "" ZR_High
 SG_ RV_Menu_Item : 9|2@1+ (1,0) [0|3] "" ZR_High
 SG_ SV_Video_on : 11|1@1+ (1,0) [0|1] "" ZR_High,ZR_Standard
 SG_ RV_Calib_Fehler : 12|1@1+ (1,0) [0|1] "" ZR_High
 SG_ RV_GL_Trailer_connect : 13|1@1+ (1,0) [0|1] "" ZR_High,ZR_Standard
 SG_ RV_GL_Trunk_open : 14|1@1+ (1,0) [0|1] "" ZR_High,ZR_Standard
 SG_ RV_GL_LWS_Fehler : 15|1@1+ (1,0) [0|1] "" ZR_High
 SG_ RV_Helligkeit : 16|7@1+ (1,0) [0|100] "Unit_PerCent" ZR_High,ZR_Standard
 SG_ ZFAS_Umfeldbeleuchtung_Anf : 23|1@1+ (1,0) [0|1] "" Vector__XXX
 SG_ RV_Kontrast : 24|7@1+ (1,0) [0|100] "Unit_PerCent" ZR_High,ZR_Standard
 SG_ RV_Farbe : 32|7@1+ (1,0) [0|100] "Unit_PerCent" ZR_High,ZR_Standard

BO_ 1714 Diagnose_01: 8 Gateway_MQB
 SG_ DGN_Verlernzaehler : 0|8@1+ (1,0) [0|254] "" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,SAK_MQB
 SG_ KBI_Kilometerstand : 8|20@1+ (1,0) [0|1048573] "Unit_KiloMeter" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Jahr : 28|7@1+ (1,2000) [2000|2127] "Unit_Year" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Monat : 35|4@1+ (1,0) [1|12] "Unit_Month" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Tag : 39|5@1+ (1,0) [1|31] "Unit_Day" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Stunde : 44|5@1+ (1,0) [0|23] "Unit_Hours" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Minute : 49|6@1+ (1,0) [0|59] "Unit_Minut" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ UH_Sekunde : 55|6@1+ (1,0) [0|59] "Unit_Secon" Airbag_MQB,BMS_MQB,Getriebe_DQ_Hybrid_MQB,Getriebe_DQ_MQB,LEH_MQB,Motor_Diesel_MQB,Motor_Hybrid_MQB,Motor_Otto_MQB,SAK_MQB
 SG_ Kombi_02_alt : 62|1@1+ (1,0) [0|1] "" Airbag_MQB,BMS_MQB,LEH_MQB
 SG_ Uhrzeit_01_alt : 63|1@1+ (1,0) [0|1] "" Airbag_MQB,BMS_MQB,LEH_MQB

BO_ 1716 VIN_01: 8 Gateway_MQB
 SG_ VIN_01_MUX M : 0|2@1+ (1,0) [0|3] "" Airbag_MQB
 SG_ KS_Geheimnis_1 m0 : 8|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ KS_Geheimnis_2 m0 : 16|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ KS_Geheimnis_3 m0 : 24|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ KS_Geheimnis_4 m0 : 32|8@1+ (1,0) [0|255] "" Vector__XXX
 SG_ VIN_1 m0 : 40|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_2 m0 : 48|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_3 m0 : 56|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_4 m1 : 8|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_5 m1 : 16|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_6 m1 : 24|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_7 m1 : 32|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_8 m1 : 40|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_9 m1 : 48|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_10 m1 : 56|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_11 m2 : 8|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_12 m2 : 16|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_13 m2 : 24|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_14 m2 : 32|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_15 m2 : 40|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_16 m2 : 48|8@1+ (1,0) [0|255] "" Airbag_MQB
 SG_ VIN_17 m2 : 56|8@1+ (1,0) [0|255] "" Airbag_MQB

BO_ 316495015 MEB_Camera_04: 32 XXX

BO_ 316495049 SAL_01: 8 XXX
 SG_ CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ CNT : 8|4@1+ (1,0) [0|15] "" XXX
 SG_ Brake_Unknown : 18|1@0+ (1,0) [0|1] "" XXX
 SG_ Brake_Light_01 : 20|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_2 : 22|1@0+ (1,0) [0|1] "" XXX
 SG_ Right_Blinker : 25|1@0+ (1,0) [0|1] "" XXX
 SG_ Left_Blinker : 26|1@1+ (1,0) [0|3] "" XXX
 SG_ Reverse_Light : 27|1@0+ (1,0) [0|1] "" XXX
 SG_ Brake_Light_02 : 30|1@0+ (1,0) [0|1] "" XXX
 SG_ Right_Blinker_02 : 44|1@1+ (1,0) [0|3] "" XXX
 SG_ Left_Blinker_02 : 45|1@0+ (1,0) [0|1] "" XXX
 SG_ NEW_SIGNAL_3 : 52|8@1+ (1,0) [0|15] "" XXX
 SG_ NEW_SIGNAL_4 : 63|1@0+ (1,0) [0|1] "" XXX

BO_ 316495081 MEB_Camera_05: 8 XXX

BO_ 316495106 AAA_01: 8 XXX
 SG_ CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ CHK : 8|4@1+ (1,0) [0|15] "" XXX

BO_ 316495140 MEB_Camera_06: 64 XXX

BO_ 316495165 HVL_01: 8 XXX
 SG_ CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ CHK : 8|4@1+ (1,0) [0|15] "" XXX

BO_ 380195935 IPA_02: 8 XXX

BO_ 380196019 MEB_Camera_07: 16 XXX

BO_ 380196036 MEB_Camera_08: 8 XXX

BO_ 389241616 MEB_Camera_09: 8 XXX

BO_ 389241617 MEB_Camera_10: 8 XXX

BO_ 401604687 MEB_Camera_11: 8 XXX

BO_ 402522959 MEB_Camera_14: 8 XXX

BO_ 441800001 EML_02: 8 XXX
 SG_ CRC : 0|8@1+ (1,0) [0|255] "" XXX
 SG_ CHK : 8|4@1+ (1,0) [0|15] "" XXX

BO_ 441800082 MEB_Camera_12: 8 XXX

BO_ 452984911 MEB_Camera_13: 8 XXX

CM_ BO_ 184 "Motorsteuergerät";
CM_ BO_ 192 "Motorsteuergerät";
CM_ BO_ 317 "Lenkungssteuergerät";
CM_ BO_ 517 "Steuergerät für Motorstart";
CM_ BO_ 522 "Steuergerät für Fahrzeugbewegung";
CM_ BO_ 1622 "Steuergerät für Lenkungsverriegelung";
CM_ BO_ 316495165 "Steuergerät ICAS1";
CM_ BO_ 441800001 "Steuergerät für Fahrzeugbewegung";
VAL_ 64 AB_RGS_Anst 4 "aktiv_Niveau_1" 5 "aktiv_Niveau_2" 6 "aktiv_Niveau_3" 7 "aktiv_Niveau_4" 8 "deaktiviert";
VAL_ 64 AB_Front_Crash 0 "kein_Front_Crash" 1 "Front_Crash";
VAL_ 64 AB_Heck_Crash 0 "kein_Heck_Crash" 1 "Heck_Crash";
VAL_ 64 AB_SF_Crash 0 "kein_Seiten_Crash_Fahrer" 1 "Seiten_Crash_Fahrer";
VAL_ 64 AB_SB_Crash 0 "kein_Seiten_Crash_Beifahrer" 1 "Seiten_Crash_Beifahrer";
VAL_ 64 AB_Rollover_Crash 0 "kein_Rollover" 1 "Rollover";
VAL_ 64 AB_Crash_Int 0 "kein_Crash" 1 "Crash_Intensitaet_1" 2 "Crash_Intensitaet_2_(nur_Stellgliedtest_MLB_B8)" 3 "Crash_Intensitaet_2_(nur_D4_C7_Colorado_NF_PAG__Crash_im_MLB_B8)" 4 "Crash_Intensitaet_3_(alt_VW/AUDI__Stellgliedtest_MLB_B8)" 5 "Crash_Intensitaet_3_(alt_PAG)" 7 "Crash_Intensitaet_3";
VAL_ 64 AB_Lampe 0 "Aus" 1 "Ein";
VAL_ 64 AB_Deaktiviert 0 "aktiv" 1 "deaktiviert";
VAL_ 64 AB_VB_deaktiviert 0 "Beifahrerairbag_aktiv" 1 "Beifahrerairbag_deaktiviert";
VAL_ 64 AB_Systemfehler 0 "kein_Fehler" 1 "Airbag_Systemfehler";
VAL_ 64 AB_Diagnose 0 "nicht_in_Diagnose" 1 "in_Diagnose";
VAL_ 64 AB_Stellgliedtest 0 "nicht_im_Stellgliedtest" 1 "Airbag_im_Stellgliedtest";
VAL_ 64 AB_Erh_Auf_VB 0 "keine_Anzeige" 1 "Beifahrerairbag_deaktiviert" 2 "Beifahrerairbag_aktiviert" 3 "nicht_definiert";
VAL_ 64 AB_Gurtwarn_VF 0 "keine_Warnung" 1 "Gurtwarnung_ausloesen";
VAL_ 64 AB_Gurtwarn_VB 0 "keine_Warnung" 1 "Gurtwarnung_ausloesen";
VAL_ 64 AB_Anzeige_Fussg 0 "keine_FSG_Aktion_ausgeloest" 1 "Motorhaube_offen" 2 "Systemfehler";
VAL_ 64 AB_Texte_AKS 0 "keine_AKS_Ausloesung" 1 "AKS_ausgeloest" 2 "AKS_Systemfehler";
VAL_ 64 AB_MKB_gueltig 0 "Multikollisionsbremsung_nicht_freigeschaltet" 1 "Multikollisionsbremsung_freigeschaltet";
VAL_ 64 AB_MKB_Anforderung 0 "Multikollisionsbremsung_nicht_angefordert" 1 "Multikollisionsbremsung_angefordert";
VAL_ 64 AB_Versorgungsspannung 0 "plausibel" 1 "unplausibel";
VAL_ 64 AB_Deaktivierung_HV 0 "keine_Deaktivierung" 1 "Deaktivierung_1" 2 "Deaktivierung_2" 3 "Deaktivierung_3" 4 "Deaktivierung_4" 5 "Deaktivierung_5" 6 "Deaktivierung_6" 7 "Fehler";
VAL_ 64 AB_EDR_Trigger 0 "No_Event" 1 "Start_Event" 2 "NonDeployment_Event" 3 "Deployment_Event";
VAL_ 64 AB_Belegung_VF 0 "nicht_verfuegbar" 1 "Fehler" 2 "nicht_belegt" 3 "belegt";
VAL_ 64 SC_LowSpeedCrashErkannt 0 "kein_Crash_erkannt" 1 "Crash_Frontbereich_erkannt" 2 "Crash_Heckbereich_erkannt" 3 "Crash_Front_und_Heckbereich_erkannt";
VAL_ 64 SC_Masterzeit 127 "Init";
VAL_ 134 LWI_Sensorstatus 0 "iO" 1 "nicht kalibriert";
VAL_ 134 LWI_QBit_Sub_Daten 0 "LWS-Subinfo real (Lenkradwinkelinformation ADS-tauglich)" 1 "LWS-Subinfo simuliert (Lenkradwinkelinformation nicht ADS-tauglich )";
VAL_ 134 LWI_MFL_Abschaltung 0 "inaktiv" 1 "aktiv";
VAL_ 134 LWI_QBit_Lenkradwinkel 0 "gültiger Wert" 1 "ausserhalb der Spezifikation";
VAL_ 134 LWI_Lenkradwinkel 8190 "Init" 8191 "Fehler";
VAL_ 134 LWI_VZ_Lenkradwinkel 0 "positiv_links_von_der_Nullstellung" 1 "negativ";
VAL_ 134 LWI_VZ_Lenkradw_Geschw 0 "positiv_links_von_der_Nullstellung" 1 "negativ";
VAL_ 134 LWI_Lenkradw_Geschw 510 "Init" 511 "Fehler";
VAL_ 159 EPS_HCA_Status 0 "disabled" 1 "initializing" 2 "fault" 3 "ready" 4 "rejected" 5 "active" 8 "preempted";
VAL_ 167 MO_Mom_Soll_Roh 1022 "Init";
VAL_ 167 MO_Mom_Ist_Summe 1022 "Init";
VAL_ 167 MO_Mom_Traegheit_Summe 1022 "Init";
VAL_ 167 MO_Mom_Soll_gefiltert 1022 "Init";
VAL_ 167 MO_Mom_Schub 510 "Init";
VAL_ 167 MO_Status_Normalbetrieb_01 0 "kein_Normalbetrieb" 1 "Normalbetrieb_erreicht";
VAL_ 167 MO_erste_Ungenauschwelle 0 "genau" 1 "Momente ungenauer >8%";
VAL_ 167 MO_QBit_Motormomente 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 168 MO_Mom_neg_verfuegbar 510 "Init" 511 "Fehler";
VAL_ 168 MO_Mom_Begr_stat 510 "Init";
VAL_ 168 MO_Mom_Begr_dyn 1022 "Init";
VAL_ 168 MO_QBit_Drehzahl_01 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 173 GE_MMom_Soll_02 1022 "keine_Anforderung_Init" 1023 "Fehler";
VAL_ 173 GE_MMom_Vorhalt_02 1022 "Init" 1023 "Fehler";
VAL_ 173 GE_Uefkt 1023 "Fehler";
VAL_ 173 GE_Fahrstufe 0 "Zwischenstellung" 1 "Init" 5 "P" 6 "R" 7 "N" 8 "D" 9 "D" 10 "E" 13 "T" 14 "T" 15 "Fehler";
VAL_ 173 GE_Schaltablauf 0 "keine_Schaltung" 1 "Momentenueberschneidung" 2 "Befuellphase" 3 "Drehzahlueberfuehrung";
VAL_ 173 GE_Uefkt_unplausibel 0 "Uefkt_plausibel" 1 "Uefkt_unplausibel";
VAL_ 173 GE_MMom_Status_02 0 "kein_Eingriff" 1 "abs_reduzierender_Eingriff_auf_Gesamt_Antrieb_nur_schneller_Pfad" 2 "abs_erhoehender_Eingriff_auf_Gesamt_Antrieb" 3 "relativer_Eingriff_auf_Gesamt_Antrieb" 4 "relativer_Eingriff_auf_E_Maschine" 5 "abs_red_Eingr_auf_Gesamt_Antrieb_nur_Luftpfad" 6 "abs_red_Eingr_auf_Gesamt_Antrieb_Luft-_und_schnellen_Pfad" 7 "abs_Eingriff_auf_Gesamtantrieb_erhoehend_und_reduzierend";
VAL_ 173 GE_Status_Kraftschluss 0 "offen_kein_Kraftschluss" 1 "offen_mit_Kraftschluss" 2 "schlupfend_geregelt" 3 "abgesichert_offen_kein_Kraftschl" 4 "geschlossen_mit_Mikroschlupf" 5 "geschlossen_mit_Ueberanpressung" 6 "sonstige_Fehler" 7 "fehlerhaft_geschlossen";
VAL_ 173 GE_MMom_Status 0 "keine Anforderung" 1 "reduzierender Getriebeeingriff" 2 "erhöhender Getriebeeingriff" 3 "Įderung mit Sprung";
VAL_ 173 GE_Freig_MMom_Vorhalt 0 "nicht_freigegeben" 1 "freigegeben";
VAL_ 173 GE_Verbot_Ausblendung 0 "kein_Verbot" 1 "Verbot";
VAL_ 173 GE_Zielgang 0 "Gang P/N (ausgekuppelt)" 1 "Gang 1" 2 "Gang 2" 3 "Gang 3" 4 "Gang 4" 5 "Gang 5" 6 "Gang 6" 7 "Gang 7" 8 "Gang R" 11 "Gang 8" 12 "Gang 9" 13 "Gang10" 14 "Istgang nicht definiert" 15 "Fehler";
VAL_ 184 EM1_Freigabe_Info_WFS 0 "ungueltig" 1 "gueltig";
VAL_ 184 EM1_Sperr_Info_WFS 0 "nicht_gesperrt" 1 "gesperrt";
VAL_ 184 EM1_AR_aktiv 0 "Init" 1 "Ruckeldaempfer_aktiv";
VAL_ 184 EM1_Eta_Sys 510 "Init" 511 "Fehler";
VAL_ 184 EM1_IstStrom 2046 "Init" 2047 "Fehler";
VAL_ 184 EM1_Fehler_ElAntriebFreilauf_Anf 0 "Init" 1 "Fehler_EM_im_Freilauf";
VAL_ 184 EM1_Abregelung_Temperatur 0 "Init" 1 "Abregelung_Temperatur";
VAL_ 184 EM1_AnlernenElMotor_Anf 0 "kein_Diagnose_Request" 1 "Diagnose_Request";
VAL_ 184 EM1_Moment_HVVerbraucher 1023 "Init";
VAL_ 184 EM1_Freigabe_Verfallsinfo_WFS 0 "Aus" 1 "Ein";
VAL_ 184 EM1_Parken_WFS_Status 0 "Limitierung_Inaktiv" 1 "Limitierung_Aktiv" 2 "Abbruch";
VAL_ 184 EM1_HV_betriebsbereit 0 "Init" 1 "Ready_HV";
VAL_ 190 Engine_Status 1 "Ready" 2 "Online";
VAL_ 253 BR_Eingriffsmoment 1022 "Init" 1023 "Fehler";
VAL_ 253 ESP_Diagnose 0 "ESP_nicht_in_Diagnose" 1 "ESP_in_Diagnose";
VAL_ 253 ESC_v_Signal_Qualifier_High_Low 0 "Gueteschwelle_kleiner_1kmh" 1 "Gueteschwelle_kleiner_3kmh" 2 "Gueteschwelle_kleiner_5kmh" 3 "Gueteschwelle_kleiner_10kmh" 4 "Gueteschwelle_kleiner_20kmh" 5 "Gueteschwelle_groessergleich_20kmh" 6 "Init" 7 "Fehler";
VAL_ 253 ESP_Vorsteuerung 0 "keine_Vorsteuerung_aktiv" 1 "Vorsteuerung_aktiv";
VAL_ 253 OBD_Schlechtweg 0 "kein_Schlechtweg_erkannt" 1 "Schlechtweg_erkannt";
VAL_ 253 OBD_QBit_Schlechtweg 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 253 ESP_v_Signal 65533 "Unterspannung" 65534 "Init" 65535 "Fehler";
VAL_ 253 ASR_Tastung_passiv 0 "ASR_aktiviert" 1 "ASR_passiv_getastet_oder_Schwellen_geaendert";
VAL_ 253 ESP_Tastung_passiv 0 "ESP_aktiviert" 1 "ESP_passiv_getastet_oder_Schwellen_geaendert";
VAL_ 253 ESP_Systemstatus 0 "iO" 1 "Fehler";
VAL_ 253 ASR_Schalteingriff 0 "keine_Anforderung" 1 "ASR_Schaltkennfeld" 2 "Rueckschaltung" 3 "Schaltverbot";
VAL_ 253 ESP_QBit_v_Signal 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 253 ABS_Bremsung 0 "keine_ABS_Regelung" 1 "ABS_Regelung_aktiv";
VAL_ 253 ASR_Anf 0 "keine_Anforderung" 1 "ASR_Anforderung";
VAL_ 253 MSR_Anf 0 "keine Anfoderung" 1 "MSR-Anforderung";
VAL_ 253 EBV_Eingriff 0 "kein_EBV_Eingriff" 1 "EBV_Eingriff";
VAL_ 253 EDS_Eingriff 0 "kein_EDS_Eingriff" 1 "EDS_Eingriff";
VAL_ 253 ESP_Eingriff 0 "kein_ESP_Eingriff" 1 "ESP_Eingriff_aktiv";
VAL_ 253 ESP_ASP 0 "inaktiv" 1 "aktiv";
VAL_ 253 ESC_Neutralschaltung 0 "keine_Anforderung" 1 "Neutralschaltung_angefordert";
VAL_ 267 TSK_Status 0 "init" 1 "disabled" 2 "enabled" 3 "regulating" 4 "accel_pedal_override" 5 "brake_only" 6 "temp_fault" 7 "perm_fault";
VAL_ 278 ESP_QBit_Wegimpuls_VL 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 278 ESP_QBit_Wegimpuls_VR 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 278 ESP_QBit_Wegimpuls_HL 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 278 ESP_QBit_Wegimpuls_HR 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 278 ESP_Wegimpuls_VL 1021 "Init" 1022 "Unterspannung" 1023 "Fehler";
VAL_ 278 ESP_Wegimpuls_VR 1021 "Init" 1022 "Unterspannung" 1023 "Fehler";
VAL_ 278 ESP_Wegimpuls_HL 1021 "Init" 1022 "Unterspannung" 1023 "Fehler";
VAL_ 278 ESP_Wegimpuls_HR 1021 "Init" 1022 "Unterspannung" 1023 "Fehler";
VAL_ 278 ESP_VL_Fahrtrichtung 0 "Vorwaerts" 1 "Rueckwaerts" 2 "Init" 3 "ungueltig_oder_nicht_verbaut";
VAL_ 278 ESP_VR_Fahrtrichtung 0 "Vorwaerts" 1 "Rueckwaerts" 2 "Init" 3 "ungueltig_oder_nicht_verbaut";
VAL_ 278 ESP_HL_Fahrtrichtung 0 "Vorwaerts" 1 "Rueckwaerts" 2 "Init" 3 "ungueltig_oder_nicht_verbaut";
VAL_ 278 ESP_HR_Fahrtrichtung 0 "Vorwaerts" 1 "Rueckwaerts" 2 "Init" 3 "ungueltig_oder_nicht_verbaut";
VAL_ 299 GRA_Hauptschalter 0 "Hauptschalter_aus__Taster_nicht_betaetigt" 1 "Hauptschalter_ein__Taster_betaetigt";
VAL_ 299 GRA_Abbrechen 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 299 GRA_Typ_Hauptschalter 0 "gerasteter_Lenkstockschalter" 1 "getasteter_Lenkstockschalter";
VAL_ 299 GRA_Limiter 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 299 GRA_Tip_Setzen 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 299 GRA_Tip_Hoch 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 299 GRA_Tip_Runter 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 299 GRA_Tip_Wiederaufnahme 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 299 GRA_Verstellung_Zeitluecke 0 "Taste_nicht_betaetigt" 1 "Dist_minus_1" 2 "Dist_plus_1" 3 "Dist_Toggle";
VAL_ 299 GRA_Codierung 0 "kein_Hebel" 1 "GRA_Hebel" 2 "ACC_Hebel" 3 "Limiter_Only";
VAL_ 299 GRA_Fehler 0 "kein_Fehler" 1 "Fehler";
VAL_ 299 GRA_LIM_Taste_verfuegbar 0 "Limiter_Taste_nicht_vorhanden" 1 "Limiter_Taste_vorhanden";
VAL_ 299 GRA_Tip_Stufe_2 0 "Tip_Stufe_1__keine_Betaetigung" 1 "Tip_Stufe_2";
VAL_ 299 GRA_TravelAssist 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 317 LatCon_HCA_Status 0 "disabled" 1 "initializing" 2 "ready" 3 "fault" 4 "active" 5 "preempted" 6 "fault" 7 "rejected";
VAL_ 319 PreCrash_Charisma_FahrPr 0 "keine_Funktion" 1 "Programm_1" 2 "Programm_2" 3 "Programm_3" 4 "Programm_4" 5 "Programm_5" 6 "Programm_6" 7 "Programm_7" 8 "Programm_8" 9 "Programm_9" 10 "Programm_10" 11 "Programm_11" 12 "Programm_12" 13 "Programm_13" 14 "Programm_14" 15 "Programm_15";
VAL_ 319 PreCrash_Charisma_Status 0 "Init" 1 "verfuegbar" 2 "nicht_verfuegbar" 3 "asynchron_durch_Fahrerwunsch";
VAL_ 319 PreCrash_Schiebedach_schliessen 0 "keine_Schliessung" 1 "Schliessung_angefordert";
VAL_ 319 PreCrash_Fenster_schliessen 0 "keine_Schliessung" 1 "Schliessung_angefordert";
VAL_ 319 PreCrash_Blinken 0 "keine_Blinkanforderung" 1 "Warnblinken" 2 "RECAS_Blinken" 3 "Notbremsblinken";
VAL_ 319 SC_PreSense_FCWP 0 "Warnung_gilt_fuer_Objekte" 1 "Warnung_gitl_fuer_Fussgaenger";
VAL_ 319 PreCrash_Tueren_Verriegeln 0 "keine_Anforderung" 1 "Tueren_Verriegeln";
VAL_ 319 PreCrash_Anforderung_AFR 0 "keine_Anforderung" 1 "linke_Seite" 2 "rechte_Seite" 3 "Vorderachse" 4 "Hinterachse" 5 "Vorwarnung" 7 "Init";
VAL_ 319 SC_PreCrash_LED 0 "keine_Anzeige" 1 "Stufe_1_glimmen" 2 "Stufe_2_leuchten" 3 "Stufe_3_blinken";
VAL_ 319 PreCrash_FS_Pneumatik_ansteuern 0 "keine_Anforderung" 1 "Pneumatik_ansteuern";
VAL_ 319 PreCrash_BFS_Pneumatik_ansteuern 0 "keine_Anforderung" 1 "Pneumatik_ansteuern";
VAL_ 319 PreCrash_Fo_Pneumatik_ansteuern 0 "keine_Anforderung" 1 "Pneumatik_ansteuern";
VAL_ 319 PreCrash_FS_Sitzlehne_verfahren 0 "keine_Anforderung" 1 "Sitzlehne_in_pos_x-Richtung_verfahren(vor)" 2 "Sitzlehne_in_neg_x-Richtung_verfahren(zurueck)" 3 "Sitzlehnenkopf_in_pos_x-Richtung_verfahren(vor)" 4 "Sitzlehnenkopf_in_neg_x-Richtung_verfahren(zurueck)" 6 "Lehnenverstellung_ansteuern" 7 "Lehnenkopfverstellung_ansteuern";
VAL_ 319 PreCrash_BFS_Sitzlehne_verfahren 0 "keine_Anforderung" 1 "Sitzlehne_in_pos_x-Richtung_verfahren(vor)" 2 "Sitzlehne_in_neg_x-Richtung_verfahren(zurueck)" 3 "Sitzlehnenkopf_in_pos_x-Richtung_verfahren(vor)" 4 "Sitzlehnenkopf_in_neg_x-Richtung_verfahren(zurueck)" 6 "Lehnenverstellung_ansteuern" 7 "Lehnenkopfverstellung_ansteuern";
VAL_ 319 PreCrash_Fo_Sitzlehne_verfahren 0 "keine_Anforderung" 1 "Sitzlehne_in_pos_x-Richtung_verfahren(vor)" 2 "Sitzlehne_in_neg_x-Richtung_verfahren(zurueck)" 3 "Sitzlehnenkopf_in_pos_x-Richtung_verfahren(vor)" 4 "Sitzlehnenkopf_in_neg_x-Richtung_verfahren(zurueck)";
VAL_ 319 PreCrash_FS_KSV_verfahren 0 "keine_Anforderung" 1 "in_pos_x-Richtung_verfahren" 2 "in_neg_x-Richung_verfahren" 3 "in_pos_z-Richtung_verfahren" 4 "in_neg_z-Richtung_verfahren" 5 "in_pos_x-Richtung_und_neg_z-Richtung_verfahren" 6 "in_pos_x_Richtung_und_pos_z-Richtung_verfahren" 7 "in_neg_x-Richtung_und_neg_z-Richtung_verfahren" 8 "in_neg_x-Richtung_und_pos_z-Richtung_verfahren" 9 "Kopfstuetze_ansteuern";
VAL_ 319 PreCrash_BFS_KSV_verfahren 0 "keine_Anforderung" 1 "in_pos_x-Richtung_verfahren" 2 "in_neg_x-Richung_verfahren" 3 "in_pos_z-Richtung_verfahren" 4 "in_neg_z-Richtung_verfahren" 5 "in_pos_x-Richtung_und_neg_z-Richtung_verfahren" 6 "in_pos_x_Richtung_und_pos_z-Richtung_verfahren" 7 "in_neg_x-Richtung_und_neg_z-Richtung_verfahren" 8 "in_neg_x-Richtung_und_pos_z-Richtung_verfahren" 9 "Kopfstuetze_ansteuern";
VAL_ 319 PreCrash_Fo_KSV_verfahren 0 "keine_Anforderung" 1 "in_pos_x-Richtung_verfahren" 2 "in_neg_x-Richung_verfahren" 3 "in_pos_z-Richtung_verfahren" 4 "in_neg_z-Richtung_verfahren" 5 "in_pos_x-Richtung_und_neg_z-Richtung_verfahren" 6 "in_pos_x_Richtung_und_pos_z-Richtung_verfahren" 7 "in_neg_x-Richtung_und_neg_z-Richtung_verfahren" 8 "in_neg_x-Richtung_und_pos_z-Richtung_verfahren";
VAL_ 319 SC_PreCrash_Warnung 0 "keine_Anzeige" 1 "latente_Vorwarnung" 2 "Vorwarnung" 3 "Akutwarnung" 4 "Eingriff" 5 "Fahreruebernahmeaufforderung" 6 "Abbiegewarnung" 7 "Basiseingriff" 8 "Heckeingriff";
VAL_ 319 SC_PreCrash_Texte 0 "keine_Anzeige" 1 "Systemstoerung" 2 "keine_Sensorsicht" 3 "Demomodus" 4 "System_aus" 5 "Anhaengerbetrieb" 6 "ESC_aus" 7 "zurzeit_eingeschraenkt" 8 "zurzeit_eingeschraenkt_ESP_Aus" 9 "Initialisierung";
VAL_ 333 ACC_limitierte_Anfahrdyn 0 "keine_Limitierung" 1 "Limitierung_Anfahrdynamik_angefordert";
VAL_ 333 ACC_nachtr_Stopp_Anf 0 "nicht_angefordert" 1 "angefordert";
VAL_ 333 ACC_StartStopp_Info 0 "Motorlauf_langfristig_nicht_notwendig_Stoppfreigabe" 1 "Motoranlauf_nicht_zwingend_notwendig_Stoppverbot_keine_Startanforderung" 2 "Motoranlauf_zwingend_notwendig_Startanforderung" 3 "Systemfehler";
VAL_ 333 ACC_Sollbeschleunigung_02 2046 "Neutralwert" 2047 "Fehler";
VAL_ 333 ACC_Anfahren 0 "keine_Anforderung_Anfahren" 1 "Anforderung_Anfahren";
VAL_ 333 ACC_Anhalten 0 "kein_Anhalten_gewuenscht" 1 "Anhalten_gewuenscht";
VAL_ 333 ACC_Typ 0 "Basis_ACC" 1 "ACC_mit_FollowToStop" 2 "ACC_mit_StopAndGo" 3 "ACC_nicht_codiert";
VAL_ 333 ACC_Status_ACC 0 "ACC_OFF_Hauptschalter_aus" 1 "ACC_INIT" 2 "ACC_STANDBY" 3 "ACC_AKTIV_regelt" 4 "ACC_OVERRIDE" 5 "ACC_Abschaltreaktion" 6 "reversibler_Fehler_im_ACC_System" 7 "irreversibler_Fehler_im_ACC_System";
VAL_ 333 ACC_Minimale_Bremsung 0 "Anforderung_Minimale_Bremsung_nicht_aktiv" 1 "Anforderung_Minimale_Bremsung_aktiv";
VAL_ 333 ACC_Anhalteweg 2046 "Neutralwert" 2047 "Fehler";
VAL_ 333 ACC_Anforderung_HMS 0 "keine_Anforderung" 1 "halten" 2 "parken" 3 "halten_Standby" 4 "anfahren" 5 "Loesen_ueber_Rampe";
VAL_ 333 Accel_Boost 3 "Driving" 0 "Stop" 2 "Driving" 1 "Driving";
VAL_ 339 MO_HVEM_Eskalation 0 "keine_Eskalation_HVEM" 1 "Eskalation_HVEM";
VAL_ 339 MO_ErwGrenzen_Anf 0 "normal" 1 "erweitert";
VAL_ 339 MO_Fehler_Notentladung_Anf 0 "Init";
VAL_ 339 MO_HVEM_MaxLeistung 510 "Init";
VAL_ 339 MO_HVK_EmIstzustand 0 "HvOff" 1 "HvStbyReq" 2 "HvStbyOk" 3 "HvBattOnReq" 4 "HvBattOnOk" 10 "HvOnIdle" 18 "HvOnDrvReq" 19 "HvOnDrvOk" 20 "HvOnDrvRdy" 28 "HvStepUpReq" 29 "HvStepUpOk" 30 "HvStepUp" 38 "HvStepDownReq" 39 "HvStepDownOk" 40 "HvStepDown" 46 "HvAcChPreReq" 47 "HvAcChPreOk" 48 "HvAcChReq" 49 "HvAcChOk" 50 "HvAcCh" 56 "HvDcChPreReq" 57 "HvDcChPreOk" 58 "HvDcChReq" 59 "HvDcChOk" 60 "HvDcCh" 67 "HvChOffReq" 68 "HvChOffOk" 69 "HvOnIdleReq" 70 "HvOnIdleOk" 96 "HvCpntOffReq" 97 "HvCpntOffOk" 98 "HvBattOffReq" 99 "HvBattOffOk" 109 "HvDcDcFailOffReq" 110 "HvDcDcFail" 119 "HvElmOffReq" 120 "HvElmOff" 126 "HvFailCpntOffReq" 127 "HvFailCpntOffOk" 128 "HvFailBattOffReq" 129 "HvFailBattOffOk" 130 "HvFailBattOff" 138 "HvFailUCtlReq" 139 "HvFailUCtlOk" 140 "HvFailUCtl" 150 "HvEmgcyOff" 255 "Init";
VAL_ 339 MO_HVK_AntriebFehlerstatus 0 "Komponente_IO" 6 "Keine_Komponentenfunktion" 7 "Init";
VAL_ 339 MO_MVK_Bordnetz_Anf 0 "keine_Anforderung" 1 "Bordnetzaktivitaet_gefordert";
VAL_ 339 MO_HVK_AntriebZustand 0 "Antrieb_AUS" 1 "Antrieb_Startphase" 2 "E_Fahren" 3 "Hybrid_Fahren" 7 "Init";
VAL_ 339 MO_HVK_EmFehlerstatus 0 "Komponente_IO" 1 "Eingeschr_KompFkt_Teilbetrieb" 3 "Eingeschr_KompFkt_Interlock" 6 "Keine_Komponentenfunktion" 7 "Init";
VAL_ 339 MO_MVK_AntriebFehlerstatus 0 "Komponente_IO" 6 "Keine_Komponentenfunktion" 7 "Init";
VAL_ 339 MO_MVK_AntriebZustand 0 "Antrieb_AUS" 1 "Antrieb_Startphase" 2 "FreilaufMotorAus" 7 "Init";
VAL_ 339 MO_MVK_EmFehlerstatus 0 "Komponente_IO" 1 "Eingeschr_KompFkt_Teilbetrieb" 6 "Keine_Komponentenfunktion" 7 "Init";
VAL_ 339 MO_MVK_EmIstzustand 0 "MvOff" 1 "MvStbyReq" 2 "MvStbyOk" 3 "MvBattOnReq" 4 "MvBattOnOk" 10 "MvOnIdle" 18 "MvOnDrvReq" 19 "MvOnDrvOk" 20 "MvOnDrv" 28 "MvStepUpReq" 29 "MvStepUpOk" 30 "MvStepUp" 38 "MvStepDownReq" 39 "MvStepDownOk" 40 "MvStepDown" 96 "MvCpntOffReq" 97 "MvCpntOffOk" 98 "MvBattOffReq" 99 "MvBattOffOk" 109 "MvDcDcFailOffReq" 110 "MvDcDcFail" 119 "MvElmFailOffReq" 120 "MvElmFail" 126 "MvFailCpntOffReq" 127 "MvFailCpntOffOk" 128 "MvFailBattOffReq" 129 "MvFailBattOffOk" 130 "MvFailBattOff" 138 "MvFailUCtlReq" 139 "MvFailUCtlOk" 140 "MvFailUCtl" 150 "MvEmgcyOff" 255 "Init";
VAL_ 420 EA_Parken_beibehalten_HMS 0 "Parken_nicht_beibehalten" 1 "Parken_beibehalten" 2 "Init" 3 "Fehler";
VAL_ 420 EA_Warnruckprofil 0 "keine_Ruckanforderung" 1 "Profil_1" 2 "Profil_2" 3 "Profil_3" 4 "Profil_4" 5 "Profil_5" 6 "Profil_6" 7 "Profil_7";
VAL_ 420 EA_eCall_Anf 0 "Keine_Anforderung" 1 "Ausloesen_eCall";
VAL_ 420 EA_Funktionsstatus 0 "EA_INIT" 1 "EA_OFF" 2 "EA_STANDBY" 3 "EA_PHASE0_AKTIV" 4 "EA_PHASE1_AKTIV" 5 "EA_PHASE2_AKTIV" 6 "EA_PHASE3_AKTIV" 7 "EA_REVERSIBLER_FEHLER" 8 "EA_IRREVERSIBLER_FEHLER";
VAL_ 420 EA_Gurtstraffer_Anf 0 "Keine_Anforderung" 1 "Haptik_1" 2 "Haptik_2" 3 "Haptik_3";
VAL_ 420 EA_Anforderung_HMS 0 "keine_Anforderung" 1 "halten" 2 "parken" 3 "halten_Standby" 4 "anfahren" 5 "Loesen_ueber_Rampe" 6 "Parken_mit_P";
VAL_ 420 EA_Sollbeschleunigung 2046 "Neutralwert" 2047 "Fehler";
VAL_ 496 EA_Texte 0 "keine_Anzeige" 1 "Nothalteassistent_fehlende_Fahreraktivitaet" 2 "Nothalteassistent_aktiv_Fahrzeugfuehrung_uebernehmen" 3 "Nothalteassistent_automatischer_Nothalt_wird_durchgefuehrt" 4 "Nothalteassistent_automatischer_Nothalt_durchgefuehrt" 5 "Nothalteassistent_Verbindung_zum_Notruf_wird_aufgebaut" 6 "Nothalteassistent_deaktiviert" 7 "Nothalteassistent_Eingriff_abgebrochen" 8 "Nothalteassistent_fehlende_Fahreraktivitaet_2" 9 "Sekundenschlaf_erkannt" 10 "LaneAssist_Lenkung_uebernehmen" 11 "ACA_Fahrzeugfuehrung_uebernehmen" 12 "EA_Fahr_Standstreifenwechsel" 14 "Nothalteassistent_nicht_verfuegbar_reversibel" 15 "Nothalteassistent_Stoerung_irreversibel";
VAL_ 496 ACF_Lampe_Hands_Off 0 "keine_Anzeige" 1 "Hands_Off_erkannt";
VAL_ 496 EA_Infotainment_Anf 0 "Init" 1 "Keine_Absenkung" 2 "Absenkung" 3 "Mute";
VAL_ 496 EA_Tueren_Anf 0 "Keine_Anforderung" 1 "Tueren_entriegeln";
VAL_ 496 EA_Innenraumlicht_Anf 0 "Innenraumbeleuchtung_ausschalten" 1 "Innenraumbeleuchtung_einschalten";
VAL_ 496 zFAS_Warnblinken 0 "Aus" 1 "Statisch" 2 "Taster" 3 "Statisch_ohne_WBT";
VAL_ 496 STP_Primaeranz 0 "keine_Anzeige" 1 "Verfuegbar" 2 "Aktiv" 3 "Uebernahme" 4 "Aktiv_Warnung" 5 "Nicht_Verfuegbar";
VAL_ 496 EA_Bremslichtblinken 0 "kein_Blinken" 1 "Anforderung_Bremslichtblinken";
VAL_ 496 EA_Blinken 0 "Kein_Blinken" 1 "Wechselblinken_links" 2 "Wechselblinken_rechts" 3 "Warnblinken" 4 "Warnblinken_Taster";
VAL_ 591 Distance_Status 0 "Valid" 3 "Invalid";
VAL_ 619 Travel_Assist_Status 4 "enabled" 0 "disabled" 2 "ready" 3 "pre_ready";
VAL_ 619 Travel_Assist_Request 4 "enable" 3 "disable" 0 "no_request" 1 "error";
VAL_ 619 Travel_Assist_Available 0 "not_available" 1 "available";
VAL_ 706 MO_Anzeige_StSt_Text 0 "keine_Anzeige" 1 "Systemfehler" 2 "Motor_manuell_starten" 3 "Rueckmeldung_durch_Fahrstufe_einlegen" 4 "Zuendungsabschaltwarnung_Timerstart" 5 "Zum_Motorstart_Bremse_treten" 6 "StSt_Aktivierung_nicht_moeglich_auf_Grund_Fahrprogramm";
VAL_ 706 MO_Anzeige_StSt_Symbol 0 "keine_Anzeige" 1 "StSt_aktiv" 2 "Motorlauf_noetig" 3 "Fahrer_temporaer_abwesend" 4 "Fahrer_vielleicht_anwesend";
VAL_ 706 MO_ADR_Status 0 "nicht_aktiv" 1 "ADR_angefordert" 2 "ADR_aktiv" 3 "ADR_Fehler";
VAL_ 706 MO_AGA_Sound_Texte 0 "keine_Anzeige" 1 "Sound_off" 2 "Sound_on";
VAL_ 706 MO_Anzeige_FMAus_Text 0 "keine_Anzeige" 1 "FMAus_Systemfehler";
VAL_ 706 MO_Fehler_MSpG 0 "i.O." 1 "Gebl䳥 defekt oder Motorraumtemp. zu hoch";
VAL_ 706 PEA_Texte 0 "Keine_Anzeige" 1 "PEA_Fahreruebernahme_noetig" 2 "PEA_Reku_nicht_verfuegbar";
VAL_ 706 TSK_Ueberstimmt_vMax_FahrerInfo 0 "nicht_ueberstimmbar" 1 "ueberstimmbar" 2 "ueberstimmt";
VAL_ 706 MO_Avus_Motorschutz 0 "keine Warnung" 1 "Drehzahlwarnung Stufe 1" 2 "Drehzahlwarnung Stufe 2" 3 "Drehzahlwarnung Stufe 3";
VAL_ 706 MO_Rekuperationsstufe 0 "keine_Anzeige" 1 "Rekuperationsstufe_1" 2 "Rekuperationsstufe_2" 3 "Rekuperationsstufe_3" 4 "Rekuperationsstufe_4" 5 "Rekuperationsstufe_5" 6 "Rekuperationsstufe_auto" 7 "Init";
VAL_ 706 TSK_Einheit_vMax_FahrerInfo 0 "km_h" 1 "mph";
VAL_ 706 TSK_Status_vMax_FahrerInfo 0 "keine_Anzeige" 1 "Anzeige_im_Fahrzeugstatus" 2 "PopUp_ohne_Gong__Eintrag_FhzStat" 3 "PopUp_mit_Gong__Eintrag_FhzStat";
VAL_ 706 MO_Red_Fahrleistung 0 "keine_Anzeige" 1 "kleine_Red" 2 "mittlere_Red_temp" 3 "mittlere_Red" 4 "grosse_Red_temp" 5 "grosse_Red";
VAL_ 706 MO_Anz_Kuehlerluefter 0 "Kein_KuehlerluefterNachlauf" 1 "Text_Luefternachlauf_aktiv" 2 "Text_Luefternachlauf_DPF_aktiv" 3 "Platzhalter_weitere_Signale" 4 "Platzhalter_weitere_Signale" 5 "Platzhalter_weitere_Signale" 6 "Platzhalter_weitere_Signale" 7 "Platzhalter_weitere_Signale";
VAL_ 706 MO_im_Leerlauf 0 "nicht_im_Leerlauf" 1 "im_Leerlauf";
VAL_ 706 WIV_Enable_Oeldr_Motor 0 "Oeldruckauswertung_im_Kombi" 1 "Oeldruckauswertung_im_MSG";
VAL_ 706 MO_OelMessung_Dauer 15 "keine_Messung_aktiv";
VAL_ 706 TSK_vMax_FahrerInfo 0 "Init___kein_Wert";
VAL_ 768 ACC_Tempolimit 0 "keine_Anzeige" 1 "5_zulHoechstgeschw" 2 "7_zulHoechstgeschw" 3 "10_zulHoechstgeschw" 4 "15_zulHoechstgeschw" 5 "20_zulHoechstgeschw" 6 "25_zulHoechstgeschw" 7 "30_zulHoechstgeschw" 8 "35_zulHoechstgeschw" 9 "40_zulHoechstgeschw" 10 "45_zulHoechstgeschw" 11 "50_zulHoechstgeschw" 12 "55_zulHoechstgeschw" 13 "60_zulHoechstgeschw" 14 "65_zulHoechstgeschw" 15 "70_zulHoechstgeschw" 16 "75_zulHoechstgeschw" 17 "80_zulHoechstgeschw" 18 "85_zulHoechstgeschw" 19 "90_zulHoechstgeschw" 20 "95_zulHoechstgeschw" 21 "100_zulHoechstgeschw" 22 "110_zulHoechstgeschw" 23 "120_zulHoechstgeschw" 24 "130_zulHoechstgeschw" 25 "140_zulHoechstgeschw" 26 "150_zulHoechstgeschw" 27 "160_zulHoechstgeschw" 28 "200_zulHoechstgeschw" 30 "250_zulHoechstgeschw" 31 "Ende_zulHoechstgeschw";
VAL_ 768 ACC_Wunschgeschw_Farbe 0 "Grundfarbe" 1 "Farbe_1";
VAL_ 768 ACC_Warnung_Verkehrszeichen_1 0 "keine_Warnung_Initialwert" 1 "Warnung";
VAL_ 768 ACA_Querfuehrung 0 "keine_Anzeige_oder_init" 1 "passiv" 2 "aktiv" 3 "Warnung";
VAL_ 768 ACC_Regelung_AIO 0 "Regelung_Ampel_nicht_aktiv" 1 "Regelung_Ampel_aktiv";
VAL_ 768 ACC_Wunschgeschw_02 1023 "keine_Anzeige";
VAL_ 768 ACC_Abstandsindex_02 0 "Audi (Init), VW (passiv/aktiv_Freifahrt)" 1022 "aus_passiv" 1023 "aktiv_Freifahrt";
VAL_ 768 ACC_Display_Prio 0 "hoechste_Prio" 1 "mittlere_Prio" 2 "geringe_Prio" 3 "keine_Prio";
VAL_ 768 ACC_rel_Objekt_Zusatzanz 0 "keine Anzeige" 1 "Relevantes_Objekt_erkannt" 2 "Relevantes_Objekt_Abstandswarnung";
VAL_ 768 ACC_Gesetzte_Zeitluecke 0 "keine_Anzeige" 1 "Zeitluecke_1" 2 "Zeitluecke_2" 3 "Zeitluecke_3" 4 "Zeitluecke_4" 5 "Zeitluecke_5" 6 "nicht_definiert" 7 "nicht_definiert";
VAL_ 768 ACC_Optischer_Fahrerhinweis 0 "optischer_Fahrerhinweis_AUS" 1 "optischer_Fahrerhinweis_EIN";
VAL_ 768 ACC_Warnhinweis 0 "kein_Warnhinweis" 1 "Warnhinweis";
VAL_ 768 ACC_EGO_Fahrzeug 0 "keine_Anzeige" 1 "aktiv" 2 "Warnung" 3 "aktiv_stop" 4 "passiv";
VAL_ 768 ACC_Relevantes_Objekt_02 0 "keine_Anzeige" 1 "Relevantes_Objekt_erkannt" 2 "Relevantes_Objekt_Warnung" 3 "passiv";
VAL_ 768 ACC_Wunschgeschw_erreicht 0 "Wunschgeschwindigkeit_nicht_erreicht" 1 "Wunschgeschwindigkeit_erreicht";
VAL_ 768 ACC_Anzeige_Zeitluecke 0 "Anzeige_Zeitluecke_nicht_angefordert" 1 "Anzeige_Zeitluecke_angefordert";
VAL_ 768 ACC_Texte_Primaeranz_02 0 "keine Anzeige" 1 "VDA_ACC_Symbol_YYY_kmh_mph" 2 "Kurven_Symbol_YYY_kmh_mph" 3 "Tempolimit_Symbol_YYY_kmh_mph" 4 "ACC_anfahrbereit" 5 "eingestellte_Zeitluecke" 6 "Tuer offen !" 7 "Stehendes Objekt voraus" 8 "o  o  o" 9 "ACC aus" 10 "ACC startet" 11 "ACC Sensor Sicht !" 12 "ACC nicht verfuegbar" 13 "ACC Fehler" 14 "ESP Eingriff" 15 "ESP PASSIV !" 16 "Parkbremse !" 17 "Geschwindigkeitsgrenze" 18 "Waehlhebelposition !" 19 "Fahrer Gurtschloss offen !" 20 "Schalthebelposition !" 21 "Drehzahl !" 22 "HDC aktiv" 23 "Kupplung betaetigt" 24 "Gang einlegen !" 25 "Bremse ueberhitzt !" 26 "Steigung_Gefaelle_zu_gross" 27 "ABSTAND" 28 "Rechtsueberholen_verhindert" 29 "Linksueberholen_verhindert" 30 "Kreuzungs_Symbol" 31 "Kreisverkehr_Symbol" 32 "Gefaelle_Symbol" 33 "Tempolimit_Kurvenassistent_ein" 34 "Kurvenassistent_ein" 35 "Tempolimitassistent_ein" 36 "Achtung_Geschwindigkeitsueberschreitung" 37 "Tempolimit_und_Kurvenassistent_nicht_verfuegbar" 38 "Tempolimit_nicht_verfuegbar" 39 "Kurvenassistent_nicht_verfuegbar" 40 "Autobahnausfahrt_Symbol" 41 "Stauende_Symbol" 42 "Engstelle_Symbol" 43 "STP_verfuegbar" 44 "Ampel_vertikal" 45 "Ampel_horizontal" 46 "STA_verfuegbar";
VAL_ 768 ACC_Texte_Zusatzanz_02 0 "keine_Anzeige" 1 "ACC_AUS" 2 "Standby" 3 "UEBERTRETEN" 4 "ABSTAND" 5 "DISTANZ_1" 6 "DISTANZ_2" 7 "DISTANZ_3" 8 "DISTANZ_4" 9 "DISTANZ_5" 10 "DISTANZ_1__dyn" 11 "DISTANZ_2__dyn" 12 "DISTANZ_3__dyn" 13 "DISTANZ_4__dyn" 14 "DISTANZ_5__dyn" 15 "DISTANZ_1__comf" 16 "DISTANZ_2__comf" 17 "DISTANZ_3__comf" 18 "DISTANZ_4__comf" 19 "DISTANZ_5__comf" 20 "DISTANZ_1__efficiency" 21 "DISTANZ_2__efficiency" 22 "DISTANZ_3__efficiency" 23 "DISTANZ_4__efficiency" 24 "DISTANZ_5__efficiency" 25 "DISTANZ_1__Stau" 26 "DISTANZ_2__Stau" 27 "DISTANZ_3__Stau" 28 "DISTANZ_4__Stau" 29 "DISTANZ_5__Stau" 30 "ACHTUNG" 31 "Abstandsanzeige" 32 "Abstandsanzeige_Warnung_aktiviert" 33 "STA_verfuegbar" 34 "Engstelle" 35 "RUV_aktiv_Rechtsverkehr" 36 "RUV_aktiv_Linksverkehr" 37 "STP_Verfuegbar" 38 "AW_Warnschwelle_1" 39 "AW_Warnschwelle_2" 40 "AW_Warnschwelle_3" 41 "AW_Warnung_1" 42 "AW_Warnung_2" 43 "AW_Warnung_3";
VAL_ 768 STA_Primaeranz 0 "keine_Anzeige" 1 "STA_standby" 2 "STA_aktiv" 3 "STA_Warnung";
VAL_ 768 Heartbeat 1 "ACC_Init_Low" 420 "ACC_Init_High" 221 "ACC_Available_Low" 360 "ACC_Available_High";
VAL_ 768 ACC_Status_ACC 0 "ACC_OFF_Hauptschalter_aus" 1 "ACC_INIT" 2 "ACC_STANDBY" 3 "ACC_AKTIV_regelt" 4 "ACC_OVERRIDE" 5 "ACC_Abschaltreaktion" 6 "reversibler_Fehler_im_ACC_System" 7 "irreversibler_Fehler_im_ACC_System";
VAL_ 768 Lead_Type_Detected 1 "Lead_Detected" 0 "No_Lead_Detected";
VAL_ 768 Lead_Type 5 "Bicycle" 3 "Car" 0 "None" 2 "Truck" 4 "Motorcycle";
VAL_ 768 ACC_Events 3 "Starting_Available" 0 "None" 5 "Speed_Limit_Camera" 9 "Street_Type" 4 "Speed_Limit_in_Nav";
VAL_ 768 Zeitluecke_1 0 "keine Anzeige";
VAL_ 768 Zeitluecke_2 0 "keine Anzeige" 32 "Minimum";
VAL_ 768 Zeitluecke_3 0 "keine Anzeige";
VAL_ 768 Zeitluecke_4 0 "keine Anzeige" 40 "Minimum";
VAL_ 768 Zeitluecke_5 0 "keine Anzeige";
VAL_ 795 ESP_Lampe 0 "Aus" 1 "Ein";
VAL_ 795 ABS_Lampe 0 "Aus" 1 "Ein";
VAL_ 795 BK_Lampe_02 0 "aus" 1 "ein_statisch" 3 "Warnung";
VAL_ 795 TC_Lampe 0 "Aus" 1 "Ein";
VAL_ 795 ESP_m_Raddrehz 32765 "Unterspannung" 32766 "Init" 32767 "Fehler";
VAL_ 795 ESP_Textanzeigen_03 0 "kein_Text" 1 "ESP_Stoerung" 2 "ABS_Stoerung" 3 "ESP_ABS_Stoerung" 4 "Werkstatt_Bremse" 5 "ASR_Stoerung" 6 "ESP_switched_off" 7 "ASR_off" 8 "ESP_ASR_on" 10 "keine_Bremskraftverstaerkung" 11 "ASR_aktiviert" 12 "ABS_ASR_Stoerung" 15 "ESP_offroad" 17 "ESP_sport" 18 "ESP_Zwangsaktivierung" 19 "ESP_Taster_Info" 20 "TC_aktiv" 21 "reserviert_fuer_Verlwarn" 22 "TC_switched_off" 23 "Verlwarn_akt_Rollsicher_inakt" 24 "ESP_SuperSport" 25 "ESP_Offroad_nicht_verfuegbar";
VAL_ 795 ESP_Meldungen 0 "keine_Anzeige" 1 "Autohold_Hinweis_1" 2 "Stoerung_Autohold" 3 "Stoerung_Hillholder" 4 "Uebernehmen" 5 "Autohold_aus" 6 "Autohold_Hinweis_2";
VAL_ 795 ESP_Fehlerstatus_Wegimp 0 "Wegimpulse_iO" 1 "Fehler";
VAL_ 795 ESP_Wegimp_Ueberlauf 0 "Reset_und_kein_Ueberlauf" 1 "mindestens_1x_Ueberlauf";
VAL_ 795 ESP_QBit_Wegimp_VA 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 795 ESP_HDC_Geschw_Farbe 0 "Standard_Farbe" 1 "abweichende_Farbe";
VAL_ 795 ESP_Off_Lampe 0 "Lampe_aus" 1 "Lampe_ein";
VAL_ 795 ESP_HDC_Regelgeschw 0 "nicht_verbaut" 125 "HDC_Standby" 126 "Init" 127 "Fehler";
VAL_ 795 ESP_BKV_Warnung 0 "keine_Anzeige" 1 "keine_Bremskraftverstaerkung";
VAL_ 817 MFL_Lokalaktiv 0 "war_nicht_lokal_aktiv" 1 "war_lokal_aktiv";
VAL_ 817 MFL_M_Taste 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 817 MFL_Paddle_Verbau 0 "verbaut" 1 "nicht_verbaut";
VAL_ 817 MFL_Tastencode_1 0 "Key_Released__No_Key" 1 "Context_Menu" 2 "Menu_Up__Next_Screen" 3 "Menu_Down__Previous_Screen" 4 "Up" 5 "Down" 6 "Up__Down_ThumbWheel" 7 "OK__ThumbWheel_Button" 8 "Cancel__Escape" 9 "Main_Menu" 10 "Side_Menu_left" 11 "Side_Menu_right" 12 "FAS_Menu" 13 "Left__Right_ThumbWheel" 14 "FAS_Menu_ThumbWheel" 16 "Volume_Up" 17 "Volume_Down" 18 "Volume_Up__Down_ThumbWheel" 19 "Volume_ThumbWheel_Button" 20 "Audio_Source" 21 "Arrow_A_Up__Right" 22 "Arrow_A_Down__Left" 23 "Arrow_B_Up__Right" 24 "Arrow_B_Down__Left" 25 "PTT__PushToTalk" 26 "PTT_Cancel" 27 "Route_Info" 28 "Hook" 29 "Hang_Up" 30 "Off_Hook" 31 "Light_On__Off" 32 "Mute" 33 "Joker1" 34 "Joker2" 35 "View" 36 "Arrow_A_Up__Down_ThumbWheel" 37 "Lenkradheizung" 38 "Rekuperation" 39 "Tube_Toggle" 40 "DRS_Drag_Reduction_System" 41 "Stopwatch_Start_Stop" 42 "Stopwatch_Nextlap" 100 "MAP" 101 "MAP_Boost" 102 "Turn_signal_left" 103 "Turn_signal_right" 104 "Turn_signal_off" 105 "Flashlight" 106 "Highbeam" 107 "Washer_button" 108 "Wiper_button_left" 109 "Wiper_button_right" 110 "Wiper_button_cancel" 111 "Exhaust_Sound" 112 "Drive_Select_button" 113 "Sport_button" 114 "ESP_Drift_Selection_ThumbWheel" 115 "PTT_special_vehicle" 116 "TravelAssist" 117 "Launch_Control" 118 "Drift_Mode_Button_Increase" 119 "Drift_Mode_Button_Decrease" 120 "Drift_Mode_Button_Select" 121 "Drive_Mode_Button_Increase" 122 "Drive_Mode_Button_Decrease" 123 "Drive_Mode_Button_Select" 124 "E_Boost_Button_Increase" 125 "E_Boost_Button_Decrease" 126 "E_Boost_Button_Select" 127 "PerformanceHybridButton_Increase" 128 "PerformanceHybridButton_Decrease" 129 "EV_Mode_Button" 130 "HUD_Button" 131 "PASM_Wheel" 132 "PASM_Button" 133 "PTV_Wheel" 134 "PTV_Button" 135 "TC_ESC_Wheel" 136 "TC_ESC_Button" 240 "Startup_Reset" 241 "Initialization";
VAL_ 817 MFL_Tastencode_2 0 "Key_Released__No_Key" 1 "Context_Menu" 2 "Menu_Up__Next_Screen" 3 "Menu_Down__Previous_Screen" 4 "Up" 5 "Down" 6 "Up__Down_ThumbWheel" 7 "OK__ThumbWheel_Button" 8 "Cancel__Escape" 9 "Main_Menu" 10 "Side_Menu_left" 11 "Side_Menu_right" 12 "FAS_Menu" 13 "Left__Right_ThumbWheel" 14 "FAS_Menu_ThumbWheel" 16 "Volume_Up" 17 "Volume_Down" 18 "Volume_Up__Down_ThumbWheel" 19 "Volume_ThumbWheel_Button" 20 "Audio_Source" 21 "Arrow_A_Up__Right" 22 "Arrow_A_Down__Left" 23 "Arrow_B_Up__Right" 24 "Arrow_B_Down__Left" 25 "PTT__PushToTalk" 26 "PTT_Cancel" 27 "Route_Info" 28 "Hook" 29 "Hang_Up" 30 "Off_Hook" 31 "Light_On__Off" 32 "Mute" 33 "Joker1" 34 "Joker2" 35 "View" 36 "Arrow_A_Up_Right_Down_Left_ThumbWheel" 37 "Lenkradheizung" 38 "Rekuperation" 39 "Tube_Toggle" 40 "DRS_Drag_Reduction_System" 41 "Stopwatch_Start_Stop" 42 "Stopwatch_Nextlap" 100 "MAP" 101 "MAP_Boost" 102 "Turn_signal_left" 103 "Turn_signal_right" 104 "Turn_signal_off" 105 "Flashlight" 106 "Highbeam" 107 "Washer_button" 108 "Wiper_button_left" 109 "Wiper_button_right" 110 "Wiper_button_cancel" 111 "Exhaust_Sound" 112 "Drive_Select_button" 113 "Sport_button" 114 "ESP_Drift_Selection_ThumbWheel" 115 "PTT_special_vehicle" 116 "TravelAssist" 117 "Launch_Control" 118 "Drift_Mode_Button_Increase" 119 "Drift_Mode_Button_Decrease" 120 "Drift_Mode_Button_Select" 121 "Drive_Mode_Button_Increase" 122 "Drive_Mode_Button_Decrease" 123 "Drive_Mode_Button_Select" 124 "E_Boost_Button_Increase" 125 "E_Boost_Button_Decrease" 126 "E_Boost_Button_Select" 127 "PerformanceHybridButton_Increase" 128 "PerformanceHybridButton_Decrease" 129 "EV_Mode_Button" 130 "HUD_Button" 131 "PASM_Wheel" 132 "PASM_Button" 133 "PTV_Wheel" 134 "PTV_Button" 135 "TC_ESC_Wheel" 136 "TC_ESC_Button" 240 "Startup_Reset" 241 "Initialization";
VAL_ 817 MFL_Eventcode_1 0 "W_no_event____T_no_event" 1 "W_1_Tick_up__T_pressed_normal" 2 "W_2_Ticks_up__T_touched" 3 "W_3_Ticks_up__T_double_click_nor" 4 "W_4_Ticks_up__T_long_press_nor_1" 5 "W_5_Ticks_up__T_long_press_nor_2" 6 "W_6_Ticks_up__T_long_press_nor_3" 7 "W_7_Ticks_up" 9 "W_7_Ticks_dn__T_pressed_strong" 10 "W_6_Ticks_dn" 11 "W_5_Ticks_dn__T_double_click_str" 12 "W_4_Ticks_dn__T_long_press_str_1" 13 "W_3_Ticks_dn__T_long_press_str_2" 14 "W_2_Ticks_dn__T_long_press_str_3" 15 "W_1_Tick_dn";
VAL_ 817 MFL_Eventcode_2 0 "W_no_event____T_no_event" 1 "W_1_Tick_up__T_pressed_normal" 2 "W_2_Ticks_up__T_touched" 3 "W_3_Ticks_up__T_double_click_nor" 4 "W_4_Ticks_up__T_long_press_nor_1" 5 "W_5_Ticks_up__T_long_press_nor_2" 6 "W_6_Ticks_up__T_long_press_nor_3" 7 "W_7_Ticks_up" 9 "W_7_Ticks_dn__T_pressed_strong" 10 "W_6_Ticks_dn" 11 "W_5_Ticks_dn__T_double_click_str" 12 "W_4_Ticks_dn__T_long_press_str_1" 13 "W_3_Ticks_dn__T_long_press_str_2" 14 "W_2_Ticks_dn__T_long_press_str_3" 15 "W_1_Tick_dn";
VAL_ 817 MFL_Marke 0 "VW" 1 "Audi" 2 "Seat" 3 "Skoda" 4 "VW_Nutzf" 5 "Bugatti" 6 "Lamborghini" 7 "Bentley" 8 "Rolls Royce" 9 "Quattro" 10 "kein_Hersteller_1" 11 "kein_Hersteller_2" 12 "kein_Hersteller_3" 13 "kein_Hersteller_4" 14 "Ford" 15 "Porsche";
VAL_ 817 MFL_Tip_Down 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 817 MFL_Tip_Up 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 817 MFL_SatModul_links_Err 0 "kein_Fehler" 1 "Fehler";
VAL_ 817 MFL_SatModul_rechts_Err 0 "kein_Fehler" 1 "Fehler";
VAL_ 817 MFL_LR_HZG_Status 0 "inaktiv" 1 "aktiv";
VAL_ 817 MFL_LR_HZG_Err 0 "normal" 1 "Fehler";
VAL_ 817 MFL_Signalhorn 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 817 MFL_Signalhorn_Err 0 "normal" 1 "Fehler";
VAL_ 817 MFL_Tip_links_Err 0 "normal" 1 "Fehler";
VAL_ 817 MFL_Tip_rechts_Err 0 "normal" 1 "Fehler";
VAL_ 817 MFL_Taste_links_Err 0 "normal" 1 "Fehler";
VAL_ 817 MFL_Taste_rechts_Err 0 "normal" 1 "Fehler";
VAL_ 817 MFL_ECU_Err 0 "normal" 1 "Fehler";
VAL_ 817 MFL_Response_Err 0 "normal" 1 "Fehler";
VAL_ 850 Parken_SM_03_MUX 0 "MUX_Gruppe_Punktinfo_0" 1 "MUX_Gruppe_Punktinfo_1" 2 "MUX_Gruppe_Punktinfo_2" 3 "MUX_Gruppe_Punktinfo_3" 4 "MUX_Gruppe_Punktinfo_4" 5 "MUX_Gruppe_Punktinfo_5" 6 "MUX_Gruppe_Punktinfo_6";
VAL_ 850 Parken_SM_03_Traj_Trans_ID_00 0 "Transaktions_ID_0" 1 "Transaktions_ID_1" 2 "Transaktions_ID_2" 3 "Transaktions_ID_3" 4 "Transaktions_ID_4" 5 "Transaktions_ID_5" 6 "Transaktions_ID_6" 7 "Transaktions_ID_7" 8 "Transaktions_ID_8" 9 "Transaktions_ID_9" 10 "Transaktions_ID_10" 11 "Transaktions_ID_11" 12 "Transaktions_ID_12" 13 "Transaktions_ID_13" 14 "Transaktions_ID_14" 15 "Transaktions_ID_15";
VAL_ 850 Parken_SM_03_Traj_Trans_ID_01 0 "Transaktions_ID_0" 1 "Transaktions_ID_1" 2 "Transaktions_ID_2" 3 "Transaktions_ID_3" 4 "Transaktions_ID_4" 5 "Transaktions_ID_5" 6 "Transaktions_ID_6" 7 "Transaktions_ID_7" 8 "Transaktions_ID_8" 9 "Transaktions_ID_9" 10 "Transaktions_ID_10" 11 "Transaktions_ID_11" 12 "Transaktions_ID_12" 13 "Transaktions_ID_13" 14 "Transaktions_ID_14" 15 "Transaktions_ID_15";
VAL_ 850 Parken_SM_03_Traj_Trans_ID_02 0 "Transaktions_ID_0" 1 "Transaktions_ID_1" 2 "Transaktions_ID_2" 3 "Transaktions_ID_3" 4 "Transaktions_ID_4" 5 "Transaktions_ID_5" 6 "Transaktions_ID_6" 7 "Transaktions_ID_7" 8 "Transaktions_ID_8" 9 "Transaktions_ID_9" 10 "Transaktions_ID_10" 11 "Transaktions_ID_11" 12 "Transaktions_ID_12" 13 "Transaktions_ID_13" 14 "Transaktions_ID_14" 15 "Transaktions_ID_15";
VAL_ 850 Parken_SM_03_Traj_Trans_ID_03 0 "Transaktions_ID_0" 1 "Transaktions_ID_1" 2 "Transaktions_ID_2" 3 "Transaktions_ID_3" 4 "Transaktions_ID_4" 5 "Transaktions_ID_5" 6 "Transaktions_ID_6" 7 "Transaktions_ID_7" 8 "Transaktions_ID_8" 9 "Transaktions_ID_9" 10 "Transaktions_ID_10" 11 "Transaktions_ID_11" 12 "Transaktions_ID_12" 13 "Transaktions_ID_13" 14 "Transaktions_ID_14" 15 "Transaktions_ID_15";
VAL_ 850 Parken_SM_03_Traj_Trans_ID_04 0 "Transaktions_ID_0" 1 "Transaktions_ID_1" 2 "Transaktions_ID_2" 3 "Transaktions_ID_3" 4 "Transaktions_ID_4" 5 "Transaktions_ID_5" 6 "Transaktions_ID_6" 7 "Transaktions_ID_7" 8 "Transaktions_ID_8" 9 "Transaktions_ID_9" 10 "Transaktions_ID_10" 11 "Transaktions_ID_11" 12 "Transaktions_ID_12" 13 "Transaktions_ID_13" 14 "Transaktions_ID_14" 15 "Transaktions_ID_15";
VAL_ 850 Parken_SM_03_Traj_Trans_ID_05 0 "Transaktions_ID_0" 1 "Transaktions_ID_1" 2 "Transaktions_ID_2" 3 "Transaktions_ID_3" 4 "Transaktions_ID_4" 5 "Transaktions_ID_5" 6 "Transaktions_ID_6" 7 "Transaktions_ID_7" 8 "Transaktions_ID_8" 9 "Transaktions_ID_9" 10 "Transaktions_ID_10" 11 "Transaktions_ID_11" 12 "Transaktions_ID_12" 13 "Transaktions_ID_13" 14 "Transaktions_ID_14" 15 "Transaktions_ID_15";
VAL_ 850 Parken_SM_03_Traj_Trans_ID_06 0 "Transaktions_ID_0" 1 "Transaktions_ID_1" 2 "Transaktions_ID_2" 3 "Transaktions_ID_3" 4 "Transaktions_ID_4" 5 "Transaktions_ID_5" 6 "Transaktions_ID_6" 7 "Transaktions_ID_7" 8 "Transaktions_ID_8" 9 "Transaktions_ID_9" 10 "Transaktions_ID_10" 11 "Transaktions_ID_11" 12 "Transaktions_ID_12" 13 "Transaktions_ID_13" 14 "Transaktions_ID_14" 15 "Transaktions_ID_15";
VAL_ 869 NVEM_Pilot_Info 0 "Init" 1 "keine_Einschraenkung" 2 "Veto_1" 3 "Veto_2";
VAL_ 869 NVEM_P_Generator_Status 0 "gefiltert" 1 "ungefiltert";
VAL_ 869 BEM_P_Generator 255 "Fehler";
VAL_ 869 BEM_n_LLA 0 "keine_Erhoehung" 1 "Stufe_1" 2 "Stufe_2" 3 "Stufe_3";
VAL_ 869 BEM_Anf_KL 0 "keine Anforderung" 1 "Anforderung Kühlerlüfter Ansteuerung";
VAL_ 869 BEM_StartStopp_Info 0 "Motorlauf_nicht_notwendig_(Stoppfreigabe)" 1 "Motoranlauf_nicht_zwingend_notwendig_(Stoppverbot,keine_Startanforderung)" 2 "Motoranlauf_zwingend_notwendig_(Startanforderung)" 3 "Systemfehler";
VAL_ 869 BEM_Batt_Ab 0 "verbunden" 1 "nicht_verbunden";
VAL_ 869 BEM_Hybrid_Info 0 "keine_Einschraenkung_durch_Energiemanagement" 1 "Motorstart_ueber_12V_Starter_nur_bei_0kmh_moeglich" 2 "kein_Motorstart_ueber_12V_Starter_nach_el_Fahrtbetrieb" 3 "Systemfehler";
VAL_ 869 NVEM_Red_KL 0 "Init" 1 "Abschaltung_KL" 2 "Reduzierung_KL";
VAL_ 869 NVEM_Freilauf_Info 0 "Freilauf_freigegeben" 1 "Weiches_Veto_uebertippbar" 2 "Hartes_Veto_Abbruch" 3 "Freilauf_Anforderung";
VAL_ 869 BEM_HYB_DC_uMinLV 254 "Init" 255 "Fehler";
VAL_ 870 BM_ZV_auf 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_ZV_zu 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_DWA_ein 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_DWA_Alarm 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Crash 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Panik 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Not_Bremsung 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_GDO 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Warnblinken 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Taxi_Notalarm 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Telematik 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_links 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_rechts 0 "inaktiv" 1 "aktiv";
VAL_ 870 Blinken_li_Fzg_Takt 0 "Blinker_links_ausgeschaltet" 1 "Blinker_links_eingeschaltet";
VAL_ 870 Blinken_re_Fzg_Takt 0 "Blinker_rechts_ausgeschaltet" 1 "Blinker_rechts_eingeschaltet";
VAL_ 870 Blinken_li_Kombi_Takt 0 "Blinkerkontrolllampe_links_ausgeschaltet" 1 "Blinkerkontrolllampe_links_eingeschaltet";
VAL_ 870 Blinken_re_Kombi_Takt 0 "Blinkerkontrolllampe_rechts_ausgeschaltet" 1 "Blinkerkontrolllampe_rechts_eingeschaltet";
VAL_ 870 BM_NBA_n_codiert_n_aktiv 0 "codiert_AND_kein_Fehler" 1 "nicht_codiert_OR_Fehler_erkannt";
VAL_ 870 BM_NBA_Status 0 "NBA_nicht_aktiv" 1 "BRL_Dunkelphase" 3 "BRL_Hellphase";
VAL_ 870 BM_WBT_Beleuchtung 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_HD_Oeffnung_angelernt 0 "keine_Quittierung" 1 "Quittierung";
VAL_ 870 BM_Autobahn 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Rollenmodus_Blinken 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Recas 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Wischblinken 0 "inaktiv" 1 "aktiv";
VAL_ 870 BM_Telematik_Abbruchgrund 0 "Init" 1 "speed_out_of_range" 2 "defect" 3 "clamp_s_on" 4 "clamp_15_on" 5 "door_open" 6 "engine_hood_open" 7 "trunk_open" 8 "convertible_top_not_locked" 9 "horn_activated_by_user" 10 "Higher_Prioritiy_Function_active" 11 "Central_Lock_status_changed" 12 "Request_Dropped_by_Requester" 13 "Service_Duration_Expired" 14 "not_possible_due_to_coding" 15 "no_reason_or_unknown_timeout";
VAL_ 870 BM_PiloPa 0 "PiloPa_Blinkerquittierung_inaktiv" 1 "PiloPa_Blinkerquittierung_aktiv";
VAL_ 870 DWA_Alarmquelle 0 "kein_Ausloesegrund" 1 "Tuerkontakt_Fahrertuer" 2 "Tuerkontakt_Beifahrertuer" 3 "Tuerkontakt_hinten_links" 4 "Tuerkontakt_hinten_rechts" 5 "Motorhaubenkontakt_vorne" 6 "Kofferraum_hinten" 7 "Masseschleife_der_Heckscheibe" 8 "Innenraumueberwachung" 9 "Alarm_durch_Noteinstieg" 10 "Sounder" 11 "Neigungssensor" 12 "TSG_FT_am_CAN_Bus" 13 "TSG_BT_am_CAN_Bus" 14 "TSG_HFS_am_CAN_Bus" 15 "TSG_HBFS_am_CAN_Bus" 16 "Klemme_15" 17 "Klemme_15sig" 18 "frei" 19 "Anhaengerueberwachung" 20 "Scheinwerferueberwachung_links" 21 "Scheinwerferueberwachung_rechts" 22 "Handschuhkasten" 23 "Verdeckueberwachung" 24 "OBD_Alarm" 25 "Power_on_Reset" 30 "Init" 31 "Fehler";
VAL_ 888 GNSS_Ortung_Hoehe 4094 "Init" 4095 "Fehler";
VAL_ 891 GNSS_UTC_Zeit 0 "Init";
VAL_ 891 GNSS_Empfaenger_Status 0 "Backup_Mode" 1 "Live";
VAL_ 891 GNSS_GPS_in_Nutzung 0 "unbenutzt" 1 "benutzt";
VAL_ 891 GNSS_GLONASS_in_Nutzung 0 "unbenutzt" 1 "benutzt";
VAL_ 891 GNSS_Empfangbare_Satelliten 0 "Init" 31 "31_oder_mehr";
VAL_ 891 GNSS_Sichtbare_Satelliten 0 "Init" 31 "31_oder_mehr";
VAL_ 891 GNSS_Genutzte_Satelliten 0 "Init" 31 "31_oder_mehr";
VAL_ 916 WBA_Fahrstufe_02 0 "Zwischenstellung_keine_Position" 1 "Position_P" 2 "Position_R" 3 "Position_N" 4 "Position_D" 5 "Position_S" 6 "Position_M_Tippfunktion" 7 "Kurzzeit_M" 8 "Position_E" 9 "Position_MS" 10 "Position_S_Plus" 11 "Position_MS_Plus" 12 "Position_Offroad" 13 "Position_B";
VAL_ 916 WBA_ZielFahrstufe 0 "keine_Gangempfehlung_kein_Gang_eingelegt" 1 "Pfeil_nach_S" 2 "Pfeil_nach_D" 3 "Pfeil_nach_M" 4 "Pfeil_nach_E";
VAL_ 916 WBA_GE_Warnung_02 0 "keine_Anzeige" 1 "Fehlereskalationsstufe_I" 2 "Fehlereskalationsstufe_II" 3 "Weiterfahrt_nur_eingeschraenkt_moeglich__Kein_R_Gang" 4 "Getriebefehler_Weiterfahrt_nur_in_D_moeglich__P_bei_Motor_aus" 5 "Wegrollgefahr__P_nicht_moeglich" 6 "Paddlenotbetrieb_Schema+Warnung" 7 "Paddlenotbetrieb_Schema" 8 "Geschwindigkeitsbegrenzung_Notlauf" 9 "Fehlereskalationsst_o_Einschr" 10 "Fehlereskalationsstufe_v_Limit" 11 "Parksperre_Infostufe" 12 "Parksperre_Warnstufe_I" 13 "Parksperre_Warnstufe_II";
VAL_ 916 WBA_eing_Gang_02 0 "keine_Ganganzeige" 1 "Gang_1" 2 "Gang_2" 3 "Gang_3" 4 "Gang_4" 5 "Gang_5" 6 "Gang_6" 7 "Gang_7" 8 "Gang_8" 9 "Gang_9" 10 "Funktion_Ganganzeigeunterdrueckung" 11 "Gang_10";
VAL_ 916 WBA_GE_Texte 0 "keine_Anzeige" 1 "zum_Einlegen_einer_Fahrstufe_Fussbremse_betaetigen__ShiftLock_Information" 2 "zum_Einlegen_von_R_N_D_Fussbremse_betaetigen_und_Motor_starten" 3 "Wegrollgefahr_bitte_P_einlegen" 4 "Achtung_Zeitueberschreitung_P_wird_eingelegt" 5 "Wiederanmeldeprozedur_Bremse_betaetigen" 6 "P_nur_im_Stillstand_moeglich" 7 "Rennstart_aktiv";
VAL_ 916 WBA_Segeln_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 916 WBA_Schaltschema 0 "kein_Schaltschema" 1 "xxxD(S)" 2 "xxxD_S" 3 "xxxS(D)" 4 "xxxS_D" 5 "xxxM(D)" 6 "xxxM_D" 7 "xxxM(S)" 8 "xxxM_S" 9 "xxxE(D)" 10 "xxxE_D" 11 "xxxE(S)" 12 "xxxE_S" 13 "xxxE(M)" 14 "xxxE_M";
VAL_ 916 WBA_GE_Zusatzwarnungen 0 "keine_Anzeige" 1 "Getriebeueberhitzung_Warnstufe_I" 2 "Getriebeueberhitzung_Warnstufe_II" 3 "Getriebefehler_Bitte_anhalten_und_P_einlegen";
VAL_ 916 GE_Sollgang 0 "keine_Empfehlung" 1 "Gang_1" 2 "Gang_2" 3 "Gang_3" 4 "Gang_4" 5 "Gang_5" 6 "Gang_6" 7 "Gang_7" 8 "Gang_8" 9 "Gang_9" 11 "Gang_10";
VAL_ 916 GE_Tipschaltempf_verfuegbar 0 "nicht_verfuegbar" 1 "verfuegbar";
VAL_ 916 WBA_GE_Texte_02 0 "Keine_Anzeige" 1 "Fahrtrichtungswechsel_nur_nach_Stillstandt" 2 "Ladestecker_gesteckt" 3 "zusaetzlicher_Service_erforderlich" 4 "eLaunch_Aktiv" 5 "eLaunch_nicht_moeglich" 6 "WH_S_nicht_moeglich_Offroad" 7 "WH_S_nicht_moeglich_RangeMode";
VAL_ 916 WBA_GE_Texte_03 0 "init" 1 "LC_not_available" 2 "LCperformance_not_possible" 3 "LCperformance_possible" 4 "LCperformance_armed" 5 "LCperformance_preparation" 6 "LCperformance_launch" 7 "LCperformance_aborted" 8 "LCsmoke_not_possible" 9 "LCsmoke_possible" 10 "LCsmoke_armed" 11 "LCsmoke_launch" 12 "LCsmoke_aborted" 15 "Fehler";
VAL_ 916 WBA_Blinken 0 "kein_WBA_Blinken" 1 "WBA_Blinken";
VAL_ 916 GE_Wiederstart_Anz_Std 0 "keine_Anzeige" 1 "Standard_Wiederstartgrund";
VAL_ 916 GE_Stoppverbot_Anz_01 0 "keine_Anzeige" 1 "Temperaturbedingung";
VAL_ 916 GE_Stoppverbot_Anz_02 0 "keine_Anzeige" 1 "Drucksensorausfall";
VAL_ 916 GE_Stoppverbot_Anz_03 0 "keine_Anzeige" 1 "Grundeinstellung";
VAL_ 916 GE_Stoppverbot_Anz_04 0 "keine_Anzeige" 1 "Demontagestellung";
VAL_ 916 GE_Stoppverbot_Anz_05 0 "keine_Anzeige" 1 "Eingeschraenkte_Fahrfunktion_Notlauf_Liegenbleiber";
VAL_ 916 GE_Stoppverbot_Anz_06 0 "keine_Anzeige" 1 "Stoppverbot_ueber_Applikation";
VAL_ 916 GE_Stoppverbot_Anz_07 0 "keine_Anzeige" 1 "Schaltung_aktiv";
VAL_ 916 GE_Stoppverbot_Anz_Std 0 "keine_Anzeige" 1 "Standard_Stoppvetogrund";
VAL_ 919 LDW_Gong 1 "Chime" 2 "Beep" 0 "None";
VAL_ 919 LDW_Texte 8 "laneAssistTakeOver" 4 "laneAssistTakeOverUrgent" 0 "none";
VAL_ 949 KL_Drehz_Anh 0 "keine_Anhebung" 1 "Drehzahlanhebung_vom_Motor_angefordert";
VAL_ 949 KL_Vorwarn_Komp_ein 0 "Init" 1 "Vorwarnung_Kompressor_ein";
VAL_ 949 KL_AC_Schalter 0 "aus" 1 "ein";
VAL_ 949 KL_Komp_Moment_alt 1 "veraltet_bzw_Ermittlung_des_Moments_nicht_moeglich__zB_keine_Drehzahl_kein_Kaeltemitteldruck";
VAL_ 949 KL_Vorwarn_Zuheizer_ein 0 "keine_Vorwarnung" 1 "Vorwarnung_Zuheizer_ein";
VAL_ 949 KL_Zustand 0 "Aus" 1 "Ein";
VAL_ 949 KL_Kompressorkupplung_linear 253 "volle_Ansteuerung" 254 "Init__nicht_bedient";
VAL_ 949 KL_Charisma_FahrPr 0 "keine_Funktion" 1 "Programm_1" 2 "Programm_2" 3 "Programm_3" 4 "Programm_4" 5 "Programm_5" 6 "Programm_6" 7 "Programm_7" 8 "Programm_8" 9 "Programm_9" 10 "Programm_10" 11 "Programm_11" 12 "Programm_12" 13 "Programm_13" 14 "Programm_14" 15 "Programm_15";
VAL_ 949 KL_Charisma_Status 0 "Init" 1 "verfuegbar" 2 "nicht_verfuegbar" 3 "asynchron_durch_Fahrerwunsch";
VAL_ 949 KL_nachtr_Stopp_Anf 0 "nicht_angefordert" 1 "angefordert";
VAL_ 949 KL_T_Charge 0 "Taster_nicht_betaetigt" 1 "Taster_betaetigt";
VAL_ 949 KL_Last_Kompr 255 "Fehler";
VAL_ 949 KL_Spannungs_Anf 0 "keine_Anforderung" 1 "Anforderung_Stufe_1" 2 "Anforderung_Stufe_2" 3 "Anforderung_Stufe_3";
VAL_ 949 KL_Thermomanagement 0 "keine_Freigabe_TMM__max_Heizbedarf" 1 "kleine_Freigabe_TMM" 2 "mittlere_Freigabe_TMM" 3 "volle_Freigabe_TMM__kein_Heizbedarf";
VAL_ 949 KL_StartStopp_Info 0 "Motorlauf_nicht_notwendig_(Stoppfreigabe)" 1 "Motoranlauf_nicht_zwingend_notwendig_(Stoppverbot,keine_Startanforderung)" 2 "Motoranlauf_zwingend_notwendig_(Startanforderung)" 3 "Systemfehler";
VAL_ 949 KL_Freilauf_Info 0 "Freilauf_freigegeben" 1 "Uebergang_in_Freilauf_unzulaessig" 2 "Freilauf_nicht_freigegeben_Abbruch" 3 "Freilauf_Anforderung";
VAL_ 949 KL_Anf_KL 255 "Fehler";
VAL_ 949 KL_el_Zuheizer_Stufe 0 "Aus" 1 "Stufe_1" 2 "Stufe_2" 3 "Stufe_3";
VAL_ 949 KL_Ausstattung_Klima 0 "Heizung_elektrisch" 1 "Klimamanuell_elektrisch" 2 "Climatic__1_Zone" 3 "Climatronic__1_Zone" 4 "Climatronic__2_Zonen" 5 "Climatronic__3_Zonen" 6 "Climatronic__4_Zonen" 7 "reserviert";
VAL_ 949 KL_Variante_Standheizung 0 "Wasserstandheizung_60_min_Laufzeit" 1 "Luftstandheizung_120_min_Laufzeit" 2 "Wasserstandheizung_120_min_Laufzeit" 3 "Wasser_und_Luftstandheizung_120_min_Laufzeit";
VAL_ 958 MO_StartStopp_Status 0 "System_in_diesem_KL15_Zyklus_nicht_verfuegbar" 1 "System_aktiv_keine_Freigabe_durch_StartStop_Koordinator" 2 "System_aktiv_alle_Freigaben_liegen_vor" 3 "System_aktiv_mindestens_eine_Freigabe_fehlt";
VAL_ 958 MO_StartStopp_Wiederstart 0 "Wiederstart_inaktiv" 1 "Wiederstart_aktiv";
VAL_ 958 MO_StartStopp_Motorstopp 0 "Motor_Stop_inaktiv" 1 "Motor_Stop_aktiv";
VAL_ 958 MO_Freig_Reku 0 "Rekuperations-Modus aus" 1 "Empfehlung Spannungsanhebung" 2 "Empfehlung Spannungsabsenkung" 3 "Rekuperationsmodus aktiv, Spannungsvariation nicht notwendig";
VAL_ 958 MO_Kl_75 0 "Aus" 1 "Ein";
VAL_ 958 MO_Kl_50 0 "aus" 1 "KL50_ein_Startausfuehrung_Fahrer";
VAL_ 958 MO_Gangposition 0 "Gang_N" 1 "Gang_1" 2 "Gang_2" 3 "Gang_3" 4 "Gang_4" 5 "Gang_5" 6 "Gang_6" 7 "Gang_7" 8 "Gang_8" 9 "Automat_P" 10 "Automat_Vorwaerts_S" 11 "Automat_Vorwaerts_D/E" 12 "Zwischenstellung" 13 "Gang_R" 14 "Istgang_nicht_definiert" 15 "Fehler";
VAL_ 958 MO_StartStopp_Fahrerwunsch 0 "Init" 1 "Stoppverbot_durch_Fahrer" 2 "Stoppfreigabe_durch_Fahrer" 3 "Stoppanforderung_durch_Fahrer";
VAL_ 958 MO_HYB_Fahrbereitschaft 0 "keine_Fahrbereitschaft" 1 "Fahrbereitschaft";
VAL_ 958 MO_Ext_E_Fahrt_aktiv 0 "Rueckmeldung_E_Taster_aus" 1 "Rueckmeldung_E_Taster_ein";
VAL_ 958 MO_Fahrer_bremst 0 "kein_Bremsen" 1 "Bremse_betaetigt";
VAL_ 958 MO_QBit_Fahrer_bremst 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 958 MO_BLS 0 "kein_Bremsen" 1 "Bremse_betaetigt";
VAL_ 958 MO_Konsistenz_Bremsped 0 "Bremspedalinformation_plausibel" 1 "Bremspedalinformation_unplausibel";
VAL_ 958 MO_KomFehler_ESP 0 "kein_Fehler" 1 "Fehler";
VAL_ 958 MO_Klima_Eingr 0 "kein Eingriff" 1 "Klimakompressor ausschalten" 2 "Klimakompressor Leistungsreduzierung" 3 "Klimakompressor aufgrund der Heissleuchtenvorwarnung ausschalten";
VAL_ 958 MO_Aussp_Anlass 0 "Anlasser_darf_angesteuert_werden" 1 "Anlasser_ausspuren__Ansteuerung_nicht_moeglich";
VAL_ 958 MO_Freig_Anlass 0 "Start_nicht_zulaessig" 1 "Startfreigabe";
VAL_ 958 MO_Kuppl_schalter 0 "Schalter_sagt_ausgekuppelt" 1 "Schalter_sagt_eingekuppelt";
VAL_ 958 MO_Interlock 0 "Interlockschalter_nicht_betaetigt" 1 "Interlockschalter_betaetigt";
VAL_ 958 MO_Motor_laeuft 0 "Motor_laeuft_nicht" 1 "Motor_laeuft_autark_und_stabil_und_darf_mechanisch_belastet_werden";
VAL_ 958 MO_Kickdown 0 "kein_Kickdown" 1 "Kickdown";
VAL_ 958 MO_QBit_KL_75 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 958 MO_EKlKomLeiRed 0 "keine_Leistungsbegr" 1 "Leistungsbegr_75" 2 "Leistungsbegr_50" 3 "Leistungsbegr_25";
VAL_ 958 MO_Handshake_STH 0 "keine EKP-Ansteuerung durch STH-Anforderung" 1 "EKP-Ansteuerung durch STH-Anforderung";
VAL_ 958 MO_BKV_Unterdruckwarnung 0 "Unterdruckhaushalt_iO" 1 "Unterdruckhaushalt_niO";
VAL_ 958 MO_Freigabe_Segeln 0 "Segelbetrieb_nicht_freigegeben" 1 "Segelbetrieb_freigegeben";
VAL_ 958 MO_PTC_Status 0 "nicht_unterstuetzt" 1 "Stufe_0" 2 "Stufe_1" 3 "Stufe_2" 4 "Stufe_3" 7 "PTC_am_BCM";
VAL_ 958 MO_QBit_Gangposition 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 958 MO_Signalquelle_Gangposition 0 "Sensorsignal" 1 "Modellsignal";
VAL_ 958 MO_Remotestart_Betrieb 0 "MSG_nicht_bereit_fuer_RS_Betrieb" 1 "MSG_bereit_fuer_oder_im_RS_Betrieb";
VAL_ 958 MO_Remotestart_moeglich 0 "Remotestart_nicht_moeglich" 1 "Remotestart_moeglich";
VAL_ 958 MO_FMAus_aktiv 0 "inaktiv" 1 "Segeln_mit_Motor_aus_aktiv";
VAL_ 958 MO_FMAus_Startvariante 0 "kein_Motorstart" 1 "Motorstart_elektrischer_Starter" 2 "GetriebeAnschleppstart" 3 "GetriebeNotAnschleppstart";
VAL_ 958 MO_BMS_NV_Anf_stuetzen 0 "nicht_notwendig" 1 "notwendig";
VAL_ 958 MO_Zylinderabschaltung 0 "Vollmotorbetrieb_VMB" 1 "Uebergang_HMB_in_VMB" 2 "Uebergang_VMB_in_HMB" 3 "Halbmotorbetrieb_HMB";
VAL_ 958 MO_HYB_VM_aktiv 0 "VM_nicht_aktiv" 1 "VM_aktiv";
VAL_ 958 MO_StartVorauss_erfuellt 0 "Signal_nicht_bedient" 1 "StartVorauss_nicht_ueberpruefbar" 2 "StartVorauss_nicht_erfuellt" 3 "StartVorauss_erfuellt";
VAL_ 960 RSt_Fahrerhinweise 0 "Init" 1 "Fahreruebernahme_Hinweis_ZAT_Automat_ohne_Gong" 2 "Fahreruebernahme_Hinweis_ZAT_Automat_mit_Gong" 3 "Fahreruebernahme_Hinweis_ZAT_Handschalter_ohne_Gong" 4 "Fahreruebernahme_Hinweis_ZAT_Handschalter_mit_Gong" 5 "Fahreruebernahme_Hinweis_ZAS_Automat_ohne_Gong" 6 "Fahreruebernahme_Hinweis_ZAS_Automat_mit_Gong" 7 "Fahreruebernahme_Hinweis_ZAS_Handschalter_ohne_Gong" 8 "Fahreruebernahme_Hinweis_ZAS_Handschalter_mit_Gong" 9 "RemoteStart_aktiv_ohne_Gong" 10 "RemoteStart_aktiv_mit_Gong" 11 "void" 12 "void" 13 "void" 14 "void" 15 "void";
VAL_ 960 ZAS_Kl_S 0 "aus" 1 "S_Kontakt_ein";
VAL_ 960 ZAS_Kl_15 0 "aus" 1 "ein";
VAL_ 960 ZAS_Kl_X 0 "aus" 1 "ein";
VAL_ 960 ZAS_Kl_50_Startanforderung 0 "aus" 1 "KL50_ein_Startwunsch_Fahrer";
VAL_ 960 BCM_Remotestart_Betrieb 0 "kein_RS_Betrieb" 1 "RS_Betrieb";
VAL_ 960 ZAS_Kl_Infotainment 0 "inaktiv" 1 "aktiv";
VAL_ 960 BCM_Remotestart_KL15_Anf 0 "inaktiv" 1 "aktiv";
VAL_ 960 BCM_Remotestart_MO_Start 0 "nicht_angefordert" 1 "angefordert";
VAL_ 960 KST_Warn_P1_ZST_def 0 "nicht_defekt" 1 "defekt";
VAL_ 960 KST_Warn_P2_ZST_def 0 "nicht_defekt" 1 "defekt";
VAL_ 960 KST_Fahrerhinweis_1 0 "inaktiv" 1 "aktiv";
VAL_ 960 KST_Fahrerhinweis_2 0 "inaktiv" 1 "aktiv";
VAL_ 960 BCM_Ausparken_Betrieb 0 "kein_Betrieb" 1 "Ausparkvorgang_aktiv";
VAL_ 960 KST_Fahrerhinweis_4 0 "inaktiv" 1 "aktiv";
VAL_ 960 KST_Fahrerhinweis_5 0 "inaktiv" 1 "aktiv";
VAL_ 960 KST_Fahrerhinweis_6 0 "inaktiv" 1 "aktiv";
VAL_ 967 MO_Kuehlerluefter_MUX 0 "Kuehlerluefter_1" 1 "Kuehlerluefter_2";
VAL_ 967 MO_Kuehlerluefter_1 126 "Init" 127 "Fehler";
VAL_ 967 MO_Kuehlerluefter_2 126 "Init" 127 "Fehler";
VAL_ 967 MO_EFLEX_Lampe 0 "Lampe_aus" 1 "Lampe_ein" 2 "Lampe_blinkend" 3 "Lampe_blinkend_mit_Akustik";
VAL_ 967 MO_KJS_nicht_bereit 0 "kein_Fehler" 1 "Fehler";
VAL_ 967 MO_ITM_Warnung_Pumpe 0 "keine_Warnung" 1 "ITM_Warnung";
VAL_ 967 WIV_Anzeige_aktiv 0 "Anzeige aus" 1 "WIV Anzeige aktiv";
VAL_ 967 WIV_Oelmin_Warn 0 "in_Ordnung" 1 "Warnung";
VAL_ 967 WIV_Sensorfehler 0 "in_Ordnung" 1 "Sensor_defekt";
VAL_ 967 WIV_Schieflage 0 "Fahrzeug_gerade" 1 "Fahrzeug_in_Schieflage";
VAL_ 967 MO_Zustand_HWP 0 "HWP_nicht_schaltbar" 1 "HWP_foerdert_nicht" 2 "HWP_foerdert" 3 "reserviert";
VAL_ 967 OLEV_Systemstoerung 0 "֬system i.O." 1 "Systemstörung ֬system";
VAL_ 967 MO_Oelwarnung_max 0 "keine_Warnung" 1 "Warnfall_aktiv";
VAL_ 967 WIV_Oelsystem_aktiv 0 "Anzeige_aus" 1 "Anzeige_aktiv";
VAL_ 967 WIV_nicht_betriebswarm 0 "Motor_warm" 1 "Motor_nicht_betriebswarm";
VAL_ 967 WIV_Ueberfuell_Warn 0 "in_Ordnung" 1 "Ueberfuellwarnung";
VAL_ 967 WIV_laufender_Motor 0 "Messung_moeglich" 1 "Messung_nicht_moeglich";
VAL_ 967 MO_E_Warnungen 0 "keine_Anzeige" 1 "Fehler_Elektrosystem_Anhalten" 2 "Fehler_Elektrosystem_Werkstatt" 3 "Elektrosystem_ueberhitzt_Stopp" 4 "Fehler_Hybridsystem_Anhalten" 5 "Fehler_Hybridsystem_Werkstatt" 6 "Fehler_Wasserstoffsystem_Anhalte" 7 "Fehler_Wasserstoffsystem_Werksta" 8 "reserviert_keine_Anzeige" 9 "reserviert_keine_Anzeige" 10 "reserviert_keine_Anzeige" 11 "reserviert_keine_Anzeige" 12 "reserviert_keine_Anzeige" 13 "reserviert_keine_Anzeige" 14 "reserviert_keine_Anzeige" 15 "reserviert_keine_Anzeige";
VAL_ 967 MO_Text_Motorstart 0 "keine_Anzeige" 1 "Motor_im_Stoppbetrieb" 2 "StartStopp_sicherheitsbedingt_deaktiviert" 3 "System_fordert_Wiederstart" 4 "Aufforderung_Motorstart" 5 "Motorlauf_noetig" 6 "Motorlaufwarnung" 9 "Unerwuenschter_Motorstillstand" 10 "Motorstart_nicht_moeglich" 11 "Fehler_Kupplungsschalter" 12 "Motor_startet" 13 "Kupplung_betaetigen" 14 "Waehlhebel_in_PN_Position" 15 "Bremse_treten";
VAL_ 967 MO_E_Texte 0 "keine_Anzeige" 1 "Batterie_fast_leer" 2 "Ladestecker_nicht_fahrbereit" 3 "VM_Betrieb_erforderlich" 4 "Batterie_laedt_Nicht_ausschalten" 5 "Bitte_Bremse_treten" 6 "manueller_Neustart_erforderlich" 7 "Stopp_Fahrzeug_nicht_abschleppen" 8 "kein_Neustart_Haube_nicht_oeffnen" 9 "Motorstart_im_naechsten_Zyklus" 10 "VM_erforderlich_EVMode_abwaehlen" 11 "laengerer_VMBetrieb_Bordbuch" 12 "Tank_leer_VM_nicht_verfuegbar" 13 "Bitte_warten_Motor_startet" 14 "kein_Start_Batterietemperatur" 15 "Ende_elektrische_Reichweite_erreicht";
VAL_ 967 WIV_Oeldyn_avl 0 "Oeldyn_nicht_vorhanden" 1 "Oeldyn_vorhanden";
VAL_ 967 OLEV_Oelstand_nicht_vorhanden 0 "֬stand vorhanden" 1 "֬stand nicht vorhanden";
VAL_ 967 MO_Systemlampe 0 "Lampe aus" 1 "Lampe ein";
VAL_ 967 MO_OBD2_Lampe 0 "Lampe aus" 1 "Lampe ein";
VAL_ 967 MO_Heissleuchte 0 "Lampe aus" 1 "Lampe ein";
VAL_ 967 MO_Partikel_Lampe 0 "Lampe aus" 1 "Lampe ein";
VAL_ 967 MO_RedFahrleistung_Lampe 0 "Lampe_aus" 1 "Lampe_ein";
VAL_ 967 WIV_Oelstand_nicht_vorhanden 0 "֬stand vorhanden" 1 "֬stand nicht vorhanden";
VAL_ 967 WIV_nachfuellanzeige_ein 0 "keine_Nachfuellanzeige" 1 "Nachfuellanzeige";
VAL_ 967 WIV_Ueberfuell_deaktiv 0 "Ueberfuellwarnung_am_Kombi_aktiv" 1 "Ueberfuellwarnung_am_Kombi_deaktiv";
VAL_ 967 WIV_Unterfuell_Warn 0 "in_Ordnung" 1 "Unterfuellwarnung";
VAL_ 967 MO_Tankdeckel_Lampe 0 "Lampe aus" 1 "Lampe ein";
VAL_ 967 MO_Text_Tankdeckelwarn 0 "kein_Text" 1 "Anzeige_Text_Tankdeckelwarnung_im_Kombi";
VAL_ 967 MO_Vorglueh_Lampe 0 "Lampe_aus" 1 "Lampe_ein";
VAL_ 967 WIV_Oeldr_Warn_Motor 0 "keine_Warnung" 1 "niedrige_Oeldruckstufe_nicht_erreicht";
VAL_ 967 MO_E_Mode 0 "keine_Anzeige" 1 "E_Mode_passiv" 2 "E_Mode_aktiv" 3 "E_Mode_inaktiv_nicht_verfuegbar" 4 "E_Mode_aktiv_nicht_verfuegbar" 5 "reserviert_keine_Anzeige" 6 "reserviert_keine_Anzeige" 7 "reserviert_keine_Anzeige";
VAL_ 974 HFS_Tuer_geoeffnet 0 "geschlossen" 1 "offen";
VAL_ 974 HFS_verriegelt 0 "nicht_verriegelt" 1 "verriegelt";
VAL_ 974 HFS_gesafet 0 "nicht_gesafet" 1 "gesafet";
VAL_ 974 HFS_Heckrollotaster_betaetigt 0 "keine_Verfahranweisung_in_Richtung_Hoch" 1 "In_Richtung_Hoch_Verfahren";
VAL_ 974 HFS_Tuerschloss_defekt 0 "Tuerschloss_funktionsfaehig" 1 "Tuerschloss_defekt";
VAL_ 974 HFS_Unlock_Taster 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 HFS_Lock_Taster 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 HFS_Sperrklinke 0 "Schloss_in_Vorraste_und_Hauptraste" 1 "Tuer_auf_oder_Tuer_Position_zwischen_Vor_und_Hauptraste";
VAL_ 974 HFS_TAG_betaetigt 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 HFS_TIG_betaetigt 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 HFS_FH_S_HBFS_AutoHoch 0 "keine_Bedienung" 1 "Automatiklauf_Angefordert";
VAL_ 974 HFS_FH_S_HBFS_AutoTief 0 "keine_Bedienung" 1 "Automatiklauf_Angefordert";
VAL_ 974 HFS_FH_S_HBFS_ManHoch 0 "keine_Bedienung" 1 "Manueller_Lauf_Angefordert";
VAL_ 974 HFS_FH_S_HBFS_ManTief 0 "keine_Bedienung" 1 "Manueller_Lauf_Angefordert";
VAL_ 974 HFS_Tuer_Status 0 "Init" 1 "Tuer_geschlossen" 2 "Tuer_offen" 3 "Fehler";
VAL_ 974 HFS_SAD_Schalter 0 "nicht_betaetigt" 5 "AUF_manuell" 6 "AUF_automatik" 7 "ZU_manuell" 8 "ZU_automatik" 13 "nicht_verfuegbar" 14 "Init" 15 "Fehler";
VAL_ 974 HFS_FH_S_ManHoch 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 HFS_FH_S_AutoHoch 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 HFS_FH_S_ManTief 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 HFS_FH_S_AutoTief 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 HFS_FH_Bew_hoch 0 "inaktiv" 1 "aktiv";
VAL_ 974 HFS_FH_Bew_tief 0 "inaktiv" 1 "aktiv";
VAL_ 974 HFS_FH_Fang 0 "ausserhalb_Fangbereich" 1 "innerhalb_Fangbereich";
VAL_ 974 HFS_FH_Block 0 "inaktiv" 1 "aktiv";
VAL_ 974 HFS_FH_Thermo 0 "inaktiv" 1 "aktiv";
VAL_ 974 HFS_UEKB_aktiviert 0 "inaktiv" 1 "aktiv";
VAL_ 974 HFS_Tueroeffnen_Warnung 0 "Tuerwarnung_nicht_aktiv" 1 "Tuerwarnung_aktiv";
VAL_ 974 HFS_FH_normiert 0 "inaktiv" 1 "aktiv";
VAL_ 974 ASW_Warnung_aktiv_HFS 0 "nicht_angefordert" 1 "angefordert";
VAL_ 974 HFS_Zuziehhilfe_aktiv 0 "Init" 1 "Zuziehhilfe_aktiv";
VAL_ 974 HFS_Seitenrollo_hoch 1 "Seitenrollo_in_Bewegung_hoch";
VAL_ 974 HFS_Seitenrollo_tief 1 "Seitenrollo_in_Bewegung_tief";
VAL_ 974 HFS_Status_KiSi 0 "inaktiv" 1 "aktiv";
VAL_ 974 SSR_HFS_Pos_Unten 0 "SSR_Oben" 1 "SSR_Unten";
VAL_ 974 HFS_Oben_Block_erw 0 "Fensterheber_nicht_in_Blockerwar" 1 "Fensterheber_in_Blockerwartung_o";
VAL_ 974 HFS_Unten_Block_erw 0 "Fensterheber_nicht_in_Blockerwar" 1 "Fensterheber_in_Blockerwartung_u";
VAL_ 974 MTHFS_M_Taste 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 MTHFS_Pos1 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 MTHFS_Pos2 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 MTHFS_Pos3 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 974 HFS_MRollo_Schalter 0 "nicht_betaetigt" 1 "ZU_manuell" 2 "ZU_automatik" 3 "AUF_manuell" 4 "AUF_automatik" 5 "nicht_verfuegbar" 6 "Init" 7 "Fehler";
VAL_ 974 HFS_Lock_Taster_inv 0 "Lock Taster bet䴧it" 1 "Lock Taster nicht bet䴩gt";
VAL_ 974 HFS_Status_eTAG 0 "Init" 1 "Griff_ausgefahren" 2 "Griff_eingefahren" 3 "Fehler";
VAL_ 974 HFS_Tuer_Status_QBit 0 "Status_Tuerkontakt_sicher" 1 "Status_Tuerkontakt_unsicher";
VAL_ 974 HFS_TCR_Mode_aktiv 0 "TCR_Mode_deaktiv" 1 "TCR_Mode_aktiv";
VAL_ 975 HBFS_Tuer_geoeffnet 0 "geschlossen" 1 "offen";
VAL_ 975 HBFS_verriegelt 0 "nicht_verriegelt" 1 "verriegelt";
VAL_ 975 HBFS_gesafet 0 "nicht_gesafet" 1 "gesafet";
VAL_ 975 HBFS_Heckrollotaster_betaetigt 0 "keine_Verfahranweisung_in_Richtung_Hoch" 1 "In_Richtung_Hoch_Verfahren";
VAL_ 975 HBFS_Tuerschloss_defekt 0 "Tuerschloss_funktionsfaehig" 1 "Tuerschloss_defekt";
VAL_ 975 HBFS_Unlock_Taster 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 HBFS_Lock_Taster 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 HBFS_Sperrklinke 0 "Schloss_in_Vorraste_und_Hauptraste" 1 "Tuer_auf_oder_Tuer_Position_zwischen_Vor_und_Hauptraste";
VAL_ 975 HBFS_TAG_betaetigt 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 HBFS_TIG_betaetigt 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 HBFS_FH_S_HFS_AutoHoch 0 "keine_Bedienung" 1 "Automatiklauf_Angefordert";
VAL_ 975 HBFS_FH_S_HFS_AutoTief 0 "keine_Bedienung" 1 "Automatiklauf_Angefordert";
VAL_ 975 HBFS_FH_S_HFS_ManHoch 0 "keine_Bedienung" 1 "Manueller_Lauf_Angefordert";
VAL_ 975 HBFS_FH_S_HFS_ManTief 0 "keine_Bedienung" 1 "Manueller_Lauf_Angefordert";
VAL_ 975 HBFS_Tuer_Status 0 "Init" 1 "Tuer_geschlossen" 2 "Tuer_offen" 3 "Fehler";
VAL_ 975 HBFS_SAD_Schalter 0 "nicht_betaetigt" 5 "AUF_manuell" 6 "AUF_automatik" 7 "ZU_manuell" 8 "ZU_automatik" 13 "nicht_verfuegbar" 14 "Init" 15 "Fehler";
VAL_ 975 HBFS_FH_S_ManHoch 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 HBFS_FH_S_AutoHoch 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 HBFS_FH_S_ManTief 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 HBFS_FH_S_AutoTief 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 HBFS_FH_Bew_hoch 0 "inaktiv" 1 "aktiv";
VAL_ 975 HBFS_FH_Bew_tief 0 "inaktiv" 1 "aktiv";
VAL_ 975 HBFS_FH_Fang 0 "ausserhalb_Fangbereich" 1 "innerhalb_Fangbereich";
VAL_ 975 HBFS_FH_Block 0 "inaktiv" 1 "aktiv";
VAL_ 975 HBFS_FH_Thermo 0 "inaktiv" 1 "aktiv";
VAL_ 975 HBFS_UEKB_aktiviert 0 "inaktiv" 1 "aktiv";
VAL_ 975 HBFS_Tueroeffnen_Warnung 0 "Tuerwarnung_nicht_aktiv" 1 "Tuerwarnung_aktiv";
VAL_ 975 HBFS_FH_normiert 0 "inaktiv" 1 "aktiv";
VAL_ 975 ASW_Warnung_aktiv_HBFS 0 "nicht_angefordert" 1 "angefordert";
VAL_ 975 HBFS_Zuziehhilfe_aktiv 0 "Init" 1 "Zuziehhilfe_aktiv";
VAL_ 975 HBFS_Seitenrollo_hoch 1 "Seitenrollo_in_Bewegung_hoch";
VAL_ 975 HBFS_Seitenrollo_tief 1 "Seitenrollo_in_Bewegung_tief";
VAL_ 975 HBFS_Status_KiSi 0 "inaktiv" 1 "aktiv";
VAL_ 975 SSR_HBFS_Pos_Unten 0 "SSR_Oben" 1 "SSR_Unten";
VAL_ 975 HBFS_Oben_Block_erw 0 "Fensterheber_nicht_in_Blockerwar" 1 "Fensterheber_in_Blockerwartung_o";
VAL_ 975 HBFS_Unten_Block_erw 0 "Fensterheber_nicht_in_Blockerwar" 1 "Fensterheber_in_Blockerwartung_u";
VAL_ 975 MTHBFS_M_Taste 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 MTHBFS_Pos1 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 MTHBFS_Pos2 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 MTHBFS_Pos3 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 975 HBFS_MRollo_Schalter 0 "nicht_betaetigt" 1 "ZU_manuell" 2 "ZU_automatik" 3 "AUF_manuell" 4 "AUF_automatik" 5 "nicht_verfuegbar" 6 "Init" 7 "Fehler";
VAL_ 975 HBFS_Lock_Taster_inv 0 "Lock Taster bet䴧it" 1 "Lock Taster nicht bet䴩gt";
VAL_ 975 HBFS_Status_KiSi_inv 0 "aktiv" 1 "inaktiv";
VAL_ 975 HBFS_Status_eTAG 0 "Init" 1 "Griff_ausgefahren" 2 "Griff_eingefahren" 3 "Fehler";
VAL_ 975 HBFS_Tuer_Status_QBit 0 "Status_Tuerkontakt_sicher" 1 "Status_Tuerkontakt_unsicher";
VAL_ 975 HBFS_TIG_betaetigt_schliessen 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 976 FT_Tuer_geoeffnet 0 "Init" 1 "Tuer offen";
VAL_ 976 FT_verriegelt 0 "Init" 1 "verriegelt";
VAL_ 976 FT_gesafet 0 "Init" 1 "gesafet";
VAL_ 976 FT_Schluesselschalter_auf 0 "Init" 1 "Schluesselschalter_auf_betaetigt";
VAL_ 976 FT_Schluesselschalter_zu 1 "Schluesselschalter zu betaetigt";
VAL_ 976 FT_Unlock_Taster 0 "Init" 1 "Unlock_Taster_betaetigt";
VAL_ 976 FT_Lock_Taster 0 "Init" 1 "Lock_Taster_betaetigt";
VAL_ 976 FT_Sperrklinke 0 "Schloss_in_Vorraste_und_Hauptraste" 1 "Tuer_auf_oder_Tuer_Position_zwischen_Vor_und_Hauptraste";
VAL_ 976 FT_TAG_betaetigt 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 976 FT_TIG_betaetigt 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 976 FT_IRUE_Taste 0 "Init" 1 "IRUE_Taste_betaetigt";
VAL_ 976 FT_HD_Taste 0 "Init" 1 "Heckdeckel_oeffnen";
VAL_ 976 FT_TD_Taste_Fehler 0 "iO" 1 "defekt";
VAL_ 976 FT_TD_Taste 1 "Tankdeckelentriegelungs Taster gedrückt";
VAL_ 976 SSR_Temp_Freigabe 0 "SSR_Deaktiviert" 1 "SSR_Freigegeben";
VAL_ 976 FT_HD_Taste_2 0 "Taste_nicht_gedrueckt" 1 "Taste_gedrueckt";
VAL_ 976 FT_TSG_hinten_verbaut 0 "nicht verbaut" 1 "verbaut";
VAL_ 976 FT_Sp_Blk_def 0 "iO" 1 "defekt";
VAL_ 976 FT_FH_S_ManHoch 0 "Init" 1 "FH_Schalter_man_hoch_betaetigt";
VAL_ 976 FT_FH_S_AutoHoch 0 "Init" 1 "FH_Schalter_auto_hoch_betaetigt";
VAL_ 976 FT_FH_S_ManTief 0 "Init" 1 "FH_Schalter_man_tief_betaetigt";
VAL_ 976 FT_FH_S_AutoTief 0 "Init" 1 "FH_Schalter_auto_tief_betaetigt";
VAL_ 976 FT_FH_Bew_hoch 0 "Init" 1 "FH_in_Bewegung_hoch";
VAL_ 976 FT_FH_Bew_tief 0 "Init" 1 "FH_in_Bewegung_tief";
VAL_ 976 FT_FH_Fang 0 "ausserhalb_Fangbereich" 1 "innerhalb_Fangbereich";
VAL_ 976 FT_FH_Block 0 "Init" 1 "Fenster_Block";
VAL_ 976 FT_FH_Thermo 0 "Init" 1 "Thermoschutz_aktiv";
VAL_ 976 FT_UEKB_aktiviert 0 "Init" 1 "UEKB_aktiv";
VAL_ 976 ASW_HMI_defekt 0 "iO" 1 "defekt";
VAL_ 976 FT_FH_normiert 0 "Init" 1 "Fenster_normiert";
VAL_ 976 FT_Schliesstaster 1 "Schliesstaster_betaetigt";
VAL_ 976 FT_Zuziehhilfe_aktiv 0 "Init" 1 "Zuziehilfe_aktiv";
VAL_ 976 FT_SP_ausgerastet 0 "Init" 1 "Spiegel_ausgerastet";
VAL_ 976 FT_SP_lr_aktiv 0 "Init" 1 "Spiegel_xAchse_aktiv";
VAL_ 976 FT_SP_ht_aktiv 0 "Init" 1 "Spiegel_yAchse_aktiv";
VAL_ 976 ASW_wakeup 0 "Sleep" 1 "Wakeup";
VAL_ 976 FT_Oben_Block_erw 0 "Fensterheber_nicht_in_Blockerwar" 1 "Fensterheber_in_Blockerwartung_o";
VAL_ 976 FT_Unten_Block_erw 0 "Fensterheber_nicht_in_Blockerwar" 1 "Fensterheber_in_Blockerwartung_u";
VAL_ 976 FT_Kisi_li_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 976 FT_Kisi_re_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 976 FT_Kisi_Taster_li 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 976 FT_Kisi_Taster_re 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 976 FT_BFS_Fond_Freigabe 0 "Init" 1 "Freigabe_fuer_Fondbedienung_BFS";
VAL_ 976 FT_Kisi_Fehler 0 "kein_Fehler" 1 "Fehler";
VAL_ 976 FT_Daemmglas 0 "Normalglas_verbaut" 1 "Daemmglas_verbaut";
VAL_ 976 FT_SP_Heizung_Status 0 "Init" 1 "Status_Spiegelheizung_ein";
VAL_ 976 ASW_Warnung_aktiv_FS 0 "nicht_angefordert" 1 "angefordert";
VAL_ 976 FT_FH_Pos_oben 1 "Scheibe oben (nach Absenken auf Position)";
VAL_ 976 FT_Tuerschloss_defekt 0 "Tuerschloss_funktionsfaehig" 1 "Tuerschloss_defekt";
VAL_ 976 FT_SWA_Taster 0 "Taster_nicht_betaetigt" 1 "Taster_betaetigt";
VAL_ 976 SWA_HMI_Diagnose 0 "kein_Fehler_erkannt" 1 "Fehler_Taster_erkannt" 2 "Fehler_LED_erkannt" 3 "Fehler_LED_und_Taster_erkannt";
VAL_ 976 FS_Status_eTAG 0 "Init" 1 "Griff_ausgefahren" 2 "Griff_eingefahren" 3 "Fehler";
VAL_ 980 BH_Blinker_li 0 "nicht_betaetigt" 1 "Blinkerhebel_Pos_li_betaetigt";
VAL_ 980 BH_Blinker_re 0 "nicht_betaetigt" 1 "Blinkerhebel_Pos_re_betaetigt";
VAL_ 980 BH_Lichthupe 0 "nicht_betaetigt" 1 "Blinkerhebel in Richtung Lichthupe betaetigt";
VAL_ 980 BH_Fernlicht 0 "nicht_betaetigt" 1 "Blinkerhebel_in_Richtung_Fernlicht_betaetigt";
VAL_ 980 WH_Tipwischen 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 980 WH_Intervall 0 "aus" 1 "Wischerhebel in Stellung Intervall";
VAL_ 980 WH_WischerStufe1 0 "aus" 1 "Wischerhebel in Stufe Wischen 1";
VAL_ 980 WH_WischerStufe2 0 "aus" 1 "Wischerhebel in Stufe Wischen 2";
VAL_ 980 WH_Frontwaschen 0 "aus" 1 "Wischerhebel in Stellung Wisch Wasch vorne";
VAL_ 980 WH_Heckintervall 0 "nicht_betaetigt" 1 "Wischerhebel in Stellung Heckintervall";
VAL_ 980 WH_Heckwaschen 0 "nicht_betaetigt" 1 "Wischerhebel in Stellung Wisch Wasch hinten";
VAL_ 980 WH_Intervallstufen 0 "Init / Default" 1 "Intervallstufe 1" 5 "Intervallstufe 2" 9 "Intervallstufe 3" 13 "Intervallstufe 4" 15 "Fehler";
VAL_ 980 FAS_Taster 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 980 FAS_Taster_Fehler 0 "kein_Fehler" 1 "Fehler";
VAL_ 980 SMLS_Hupe 0 "nicht_betaetigt" 1 "Hupe_gedrueckt";
VAL_ 980 LRH_On_Off 0 "LRH_aus__keine Anzeige" 1 "LRH_aus" 2 "LRH_ein" 3 "Fehler";
VAL_ 980 LRH_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 980 SMLS_P_verriegelt_plausibel 0 "unplausibel" 1 "plausibel";
VAL_ 980 WH_SRA 0 "SRA_Aus" 1 "SRA_Ein";
VAL_ 980 WH_Wischer_Fehler 0 "kein_Fehler" 1 "Fehler";
VAL_ 980 BH_Blinker_Fehler 0 "kein_Fehler" 1 "Fehler";
VAL_ 980 SMLS_PTT 0 "nicht_betaetigt" 1 "PTT_gedrueckt";
VAL_ 980 WH_Tipwischen_lang 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 981 BCM1_Kurvenlicht_links_Anf 0 "linkes_Kurvenlicht/Abbiegelicht_durch_BCM1_nicht_angesteuert" 1 "linkes_Kurven/Abbiegelicht_durch_BCM1_angesteuert";
VAL_ 981 BCM1_Kurvenlicht_rechts_Anf 0 "rechtes_Kurvenlicht/Abbiegelicht_durch_BCM1_nicht_angesteuert" 1 "rechtes_Kurvenlicht/Abbiegelicht_durch_BCM1_angesteuert";
VAL_ 981 BCM1_Standlicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Abblendlicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Fernlicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Nebellicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Parklicht_li_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Parklicht_re_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Nebelschluss_Ahg_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Nebelschluss_Fzg_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Schlusslicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM_Rueckfahrlicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht_angefordert";
VAL_ 981 BCM1_Signaturlicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht_ist_einzuschalten";
VAL_ 981 BCM1_Umfeldleuchten_Anf 0 "Licht_nicht_angefordert" 1 "Licht_ist_einzuschalten";
VAL_ 981 BCM1_Tagfahrlicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Regenlicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Autobahnlicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht ist einzuschalten";
VAL_ 981 BCM1_Touristen_Licht_Anf 0 "Licht_nicht_angefordert" 1 "Anforderung von Touristenlicht";
VAL_ 981 BCM1_CH_aktiv 0 "Inaktiv" 1 "Aktiv";
VAL_ 981 BCM1_LH_aktiv 0 "Inaktiv" 1 "Aktiv";
VAL_ 981 BCM1_Gleitende_Leuchtw_Anf 0 "Aus" 1 "Ein";
VAL_ 981 BCM1_GLW_Fernlicht_Anf 0 "Aus" 1 "Ein";
VAL_ 981 BCM1_Adaptive_Lichtvert_Anf 0 "Aus" 1 "Ein";
VAL_ 981 BCM1_FoD_Sperrung_WiBli 0 "Freigabe_WiBli" 1 "Sperrung_WiBli" 2 "Init";
VAL_ 981 BCM1_FOD_Sperrung_Animationen_HL 0 "Freigabe_Animationen" 1 "Sperrung_Animationen" 2 "Init";
VAL_ 981 BCM1_Animationssperrung 0 "keine_Sperrung" 1 "Sperrung";
VAL_ 981 BCM1_CH_LH_aktiv 0 "Comming_Home_Bzw._Leaving_Home_nicht_aktiv" 1 "Comming Home bzw. Leaving Home aktiv";
VAL_ 981 BCM1_Allwetterlicht_Anf 0 "Licht_nicht_angefordert" 1 "Licht_ist_einzuschalten";
VAL_ 981 BCM1_Schlusslicht_Signatur 0 "keine_Signatur" 1 "Signatur_1" 2 "Signatur_2" 3 "Signatur_3" 4 "Signatur_4" 5 "Signatur_5" 6 "Signatur_6" 7 "Signatur_7" 8 "Signatur_8" 9 "Signatur_9" 10 "Signatur_10" 11 "Signatur_11" 12 "Signatur_12" 13 "Signatur_13" 14 "Signatur_14" 15 "Signatur_15";
VAL_ 982 BCM2_Bremsl_durch_ECD 0 "Bremslicht_ist_aus" 1 "Bremslicht_durch_ECD_aktiv";
VAL_ 982 LH_Aussenlicht_def 0 "OK" 1 "defekt";
VAL_ 982 LH_Standlicht_H_aktiv 1 "Standlicht hinten aktiv";
VAL_ 982 LH_Parklicht_HL_aktiv 0 "nicht aktiv" 1 "Parklicht hinten links aktiv";
VAL_ 982 LH_Parklicht_HR_aktiv 0 "nicht aktiv" 1 "Parklicht hinten rechts aktiv";
VAL_ 982 LH_Bremslicht_H_aktiv 1 "Bremslicht hinten aktiv";
VAL_ 982 LH_Nebelschluss_aktiv 0 "nicht aktiv" 1 "aktiv";
VAL_ 982 LH_Rueckfahrlicht_aktiv 0 "nicht aktiv" 1 "aktiv";
VAL_ 982 LH_Blinker_HL_akt 1 "Blinker hinten links aktiv";
VAL_ 982 LH_Blinker_HR_akt 1 "Blinker hinten rechts aktiv";
VAL_ 982 LH_Blinker_li_def 0 "OK" 1 "Blinker hinten links defekt";
VAL_ 982 LH_Bremsl_li_def 0 "OK" 1 "mindestens ein Bremslicht hinten links defekt";
VAL_ 982 LH_Schlusslicht_li_def 0 "OK" 1 "mindestens ein Schlusslicht hinten links defekt";
VAL_ 982 LH_Rueckf_li_def 0 "OK" 1 "Rückfahrlicht hinten links defekt";
VAL_ 982 LH_Nebel_li_def 0 "OK" 1 "Nebelschlusslicht hinten links defekt";
VAL_ 982 LH_Schluss_Brems_Nebel_li_def 0 "iO" 1 "defekt";
VAL_ 982 LH_Schluss_Brems_Nebel_re_def 0 "iO" 1 "defekt";
VAL_ 982 LH_Zusatzschlussl_def 0 "OK" 1 "mindestens_ein_Zusatzschlusslicht_defekt";
VAL_ 982 LH_Schluss_Brems_li_def 0 "OK" 1 "defekt";
VAL_ 982 LH_Schluss_Nebel_li_def 0 "OK" 1 "defekt";
VAL_ 982 LH_SL_BRL_BLK_li_def 0 "OK" 1 "defekt";
VAL_ 982 LH_Brems_Blk_li_def 0 "OK" 1 "defekt";
VAL_ 982 LH_Diag_Status_re_def 0 "OK" 1 "Aussenlict_Diagnose_Systemstoerung_rechts";
VAL_ 982 LH_Diag_Status_li_def 0 "OK" 1 "Aussenlicht_Diagnose_Systemstoerung_links";
VAL_ 982 LH_Diag_LED_li_def 0 "OK" 1 "Heckleuchte_links_defekt";
VAL_ 982 LH_Diag_LED_re_def 0 "OK" 1 "Heckleuchte_rechts_defekt";
VAL_ 982 LH_Blinker_re_def 0 "OK" 1 "Blinker hinten rechts defekt";
VAL_ 982 LH_Bremsl_re_def 0 "OK" 1 "mindestens ein Bremslicht hinten rechts defekt";
VAL_ 982 LH_Schlusslicht_re_def 0 "OK" 1 "mindestens ein Schlusslicht hinten rechts defekt";
VAL_ 982 LH_Rueckf_re_def 0 "OK" 1 "Rückfahrlicht hinten rechts defekt";
VAL_ 982 LH_Nebel_re_def 0 "OK" 1 "Nebelschlusslicht hinten rechts defekt";
VAL_ 982 LH_Schluss_Brems_mi_def 0 "OK" 1 "Defekt";
VAL_ 982 LH_Schluss_Brems_re_def 0 "OK" 1 "defekt";
VAL_ 982 LH_Schluss_Nebel_re_def 0 "OK" 1 "defekt";
VAL_ 982 LH_SL_BRL_BLK_re_def 0 "OK" 1 "defekt";
VAL_ 982 LH_Brems_Blk_re_def 0 "OK" 1 "defekt";
VAL_ 982 LH_Kennzl_def 0 "OK" 1 "Kennzeichenbeleuchtung hinten defekt";
VAL_ 982 LH_3_Bremsl_def 0 "OK" 1 "hochgesetzte  Bremsleuchte defekt";
VAL_ 982 LH_Nebel_mi_def 0 "OK" 1 "Nebelschlusslicht hinten Mitte defekt";
VAL_ 982 LH_Rueckf_mi_def 0 "OK" 1 "Rückfahllicht Mitte defekt";
VAL_ 982 LH_Schlusslicht_mi_def 0 "OK" 1 "defekt";
VAL_ 982 LH_Bremsl_mi_def 0 "OK" 1 "defekt";
VAL_ 982 LH_Bremsl_li_ges_def 0 "OK" 1 "Alle Bremslichter hinten links defekt";
VAL_ 982 LH_Bremsl_re_ges_def 0 "OK" 1 "Alle Bremslichter hinten rechts defekt";
VAL_ 988 EPB_Status 0 "offen" 1 "geschlossen_Parken" 2 "teilgespannt_Halten" 3 "im_Lauf_oeffnen" 4 "im_Lauf_schliessen" 5 "tbd" 6 "Init" 7 "unbekannt";
VAL_ 988 GE_Fahrstufe 0 "Zwischenstellung" 1 "Init" 5 "P" 6 "R" 7 "N" 8 "D" 9 "D" 10 "E" 13 "T" 14 "T" 15 "Fehler";
VAL_ 997 FT_Tuer_Status 0 "Init" 1 "Tuer_geschlossen" 2 "Tuer_offen" 3 "Fehler";
VAL_ 997 FT_Tuer_Status_QBit 0 "Status_Tuerkontakt_sicher" 1 "Status_Tuerkontakt_unsicher";
VAL_ 997 FT_Lock_Taster_02 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 997 FT_Schluesselschalter_zu_02 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 997 FT_BFS_Tuer_Status 0 "Init" 1 "Tuer_geschlossen" 2 "Tuer_offen" 3 "Fehler";
VAL_ 997 FT_HBFS_Tuer_Status 0 "Init" 1 "Tuer_geschlossen" 2 "Tuer_offen" 3 "Fehler";
VAL_ 997 FT_HFS_Tuer_Status 0 "Init" 1 "Tuer_geschlossen" 2 "Tuer_offen" 3 "Fehler";
VAL_ 997 FT_Tueroeffnen_Warnung 0 "Tuerwarnung_nicht_aktiv" 1 "Tuerwarnung_aktiv";
VAL_ 997 FT_SP_Heizung_ein 0 "Aus" 1 "Ein";
VAL_ 997 FT_Kisi_Taster_li_02 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 997 FT_Kisi_Taster_re_02 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 997 FT_TD_Taste_Status 0 "kein_Fehler" 1 "Kurzschluss_nach_Minus" 2 "Kurzschluss_nach_Plus" 3 "Leitungsunterbrechung";
VAL_ 997 FT_TCR_Mode_aktiv 0 "TCR_Mode_deaktiv" 1 "TCR_Mode_aktiv";
VAL_ 997 FS_Push_Tuergriff 0 "nicht_erkannt" 1 "erkannt";
VAL_ 1004 TSK_State 0 "init" 1 "disabled" 2 "enabled" 3 "regulating" 4 "accel_pedal_override" 5 "brake_only" 6 "temp_fault" 7 "perm_fault";
VAL_ 1122 PSD_Segment_ID 0 "keine Segmentinformationen vorhanden" 1 "Fehlerwert";
VAL_ 1122 PSD_Vorgaenger_Segment_ID 0 "keine Segmentinformation vorhanden" 1 "Fehlerwert";
VAL_ 1122 PSD_Strassenkategorie 0 "Rest_Feldweg_Schotterweg_Privatweg" 1 "Ortsstraߥ" 2 "Kreisstraߥ" 3 "Landstraߥ" 4 "Bundesstraߥ" 5 "Autobahn" 7 "Init";
VAL_ 1122 PSD_Endkruemmung 255 "Gerade";
VAL_ 1122 PSD_Endkruemmung_Vorz 0 "Kruemmung_positiv" 1 "Kruemmung_negativ";
VAL_ 1122 PSD_Idenditaets_ID 0 "keine_Segment_Informationen_vorhanden" 1 "Fehler";
VAL_ 1122 PSD_ADAS_Qualitaet 0 "keine_ADAS_Qualitaet" 1 "ADAS_Qualitaet";
VAL_ 1122 PSD_wahrscheinlichster_Pfad 0 "Pfad_mit_geringer_Wahrscheinlichkeit" 1 "wahrscheinlichster_Pfad";
VAL_ 1122 PSD_Geradester_Pfad 0 "nicht_geradester_PFad" 1 "geradester_Pfad";
VAL_ 1122 PSD_Fahrspuren_Anzahl 0 "Einbahnstrasse_in_falsche_Richtung" 1 "eine_Fahrspur" 2 "zwei_Fahrspuren" 3 "drei_Fahrspuren" 4 "vier_Fahrspuren" 5 "fuenf_Fahrspuren" 6 "sechs_Fahrspuren" 7 "mehr_als_sechs_Fahrspuren";
VAL_ 1122 PSD_Bebauung 0 "ausserhalb_bebauten_Gebietes" 1 "innerhalb_bebauten_Gebietes";
VAL_ 1122 PSD_Segment_Komplett 0 "Segment_Attribute_nicht_komplett" 1 "Segment_Attribute_komplett";
VAL_ 1122 PSD_Rampe 0 "Strasse_mit_Gegenverkehr" 1 "Auffahrt_Einbahnstr" 2 "Abfahrt_Einbahnstr" 3 "Einbahnstrasse";
VAL_ 1122 PSD_Anfangskruemmung 255 "Gerade";
VAL_ 1122 PSD_Anfangskruemmung_Vorz 0 "positiv" 1 "negativ";
VAL_ 1122 PSD_Abzweigerichtung 0 "rects_abzweigende_Strasse" 1 "links_abzweigende_Strasse";
VAL_ 1123 PSD_Pos_Segment_ID 0 "keine Position gegeben" 1 "Fehlerwert";
VAL_ 1123 PSD_Pos_Standort_Eindeutig 0 "mehrdeutiger_Standort" 1 "eindeutiger_Standort";
VAL_ 1123 PSD_Pos_Fehler_Laengsrichtung 0 "Init" 1 "< 2m" 2 "< 5m" 3 "< 10m" 4 "< 20m" 5 "< 50m" 6 "> 50m" 7 "Off-Road";
VAL_ 1123 PSD_Pos_Fahrspur 0 "unbekannt" 1 "Erste_Spur_von_rechts" 2 "Zweite_Spur_von_rechts" 3 "Dritte_Spur_von_rechts" 4 "Vierte_Spur_von_rechts" 5 "Fuenfte_Spur_von_rechts" 6 "Sechte_Spur_von_rechts" 7 "Siebte_oder_weitere_Spur_von_rechts";
VAL_ 1123 PSD_Attribut_Segment_ID_05 0 "keine_Segment_Information" 1 "Fehler";
VAL_ 1123 PSD_Attribut_1_ID 0 "keine_Information";
VAL_ 1123 PSD_Attribut_2_ID 0 "keine_Information";
VAL_ 1123 PSD_Attribute_Komplett_05 0 "Attribute_nicht_komplett" 1 "Attribute_komplett";
VAL_ 1124 PSD_06_Mux 0 "Init";
VAL_ 1124 PSD_Sys_Segment_ID 0 "keine Segmentinformationen vorhanden" 1 "nicht zulaessig";
VAL_ 1124 PSD_Sys_Geschwindigkeit_Einheit 0 "km/h" 1 "mph";
VAL_ 1124 PSD_Sys_Verkehrsrichtung 0 "Rechtsverkehr" 1 "Linksverkehr";
VAL_ 1124 PSD_Sys_Geometrieguete 0 "Geringe_Guete" 1 "tbd" 2 "tbd" 3 "Hohe_Guete";
VAL_ 1124 PSD_Sys_Mapmatchingguete 0 "geringe_Guete" 1 "res" 2 "res" 3 "hohe_Guete";
VAL_ 1124 PSD_Sys_Alter_Karte 0 "kleiner_1_Jahr" 1 "1_Jahr" 2 "2_Jahre" 3 "3_Jahre" 4 "4_Jahre" 5 "5_Jahre" 6 "6_Jahre" 7 "groesser_7_Jahre";
VAL_ 1124 PSD_Sys_Zielfuehrung 0 "Zielfuehrung nicht aktiv" 1 "Zielfuehrung aktiv";
VAL_ 1124 PSD_Sys_US_State 0 "kein_US_State" 1 "Alabama" 2 "Alaska" 3 "Arkansas" 4 "Arizona" 5 "California" 6 "Colorado" 7 "Conneticut" 8 "District_of_Columbia" 9 "Delaware" 10 "Florida" 11 "Georgia" 12 "Hawaii" 13 "Idaho" 14 "Illinois" 15 "Iowa" 16 "Indiana" 17 "Kansas" 18 "Kentucky" 19 "Louisiana" 20 "Massachusetts" 21 "Maryland" 22 "Maine" 23 "Michigan" 24 "Minnesota" 25 "Missouri" 26 "Mississippi" 27 "Montana" 28 "North_Carolina" 29 "North_Dakota" 30 "Nebraska" 31 "Nevada" 32 "New_Hampshire" 33 "New_Jersey" 34 "New_Mexico" 35 "New_York" 36 "Ohio" 37 "Oklahoma" 38 "Oregon" 39 "Pennsylvania" 40 "Puerto_Rico" 41 "Rhode_Island" 42 "South_Carolina" 43 "South_Dakota" 44 "Tennessee" 45 "Texas" 46 "Utah" 47 "Virginia" 48 "Virgin_Islands" 49 "Vermont" 50 "Washington" 51 "Wisconsin" 52 "West_Virginia" 53 "Wyoming";
VAL_ 1124 PSD_Sys_Quali_verfuegbar 0 "Qualitaetskriterien_nicht_verfuegbar" 1 "Qualitaetskriterien_verfuegbar";
VAL_ 1124 PSD_Sys_Zielfuehrung_geaendert 0 "Zielfuehrung_nicht_geaendert" 1 "Zielfuehrung_geaendert";
VAL_ 1124 PSD_Sys_Geometrieguete_erweitert 0 "Init";
VAL_ 1124 PSD_Attribut_Segment_ID 0 "keine_Segment_Information_vorhanden" 1 "Fehler";
VAL_ 1124 PSD_Attribut_3_ID 0 "keine_Information";
VAL_ 1124 PSD_Attribut_4_ID 0 "keine_Information";
VAL_ 1124 PSD_Attribut_5_ID 0 "keine_Information";
VAL_ 1124 PSD_Attribute_Komplett_06 0 "Attribute_nicht_komplett" 1 "Attribute_komplett";
VAL_ 1124 PSD_Ges_Segment_ID 0 "keine Segmentinformationen vorhanden" 1 "nicht zulaessig";
VAL_ 1124 PSD_Ges_Geschwindigkeit 0 "Kein Geschwindigkeitsgebot" 1 "0 km/h < v_max <  5 km/h" 2 "5 km/h < v_max < 10km/h" 3 "10 km/h < v_max < 15 km/h" 4 "15 km/h < v_max < 20 km/h" 5 "20 km/h < v_max < 25 km/h" 6 "25 km/h < v_max < 30 km/h" 7 "30 km/h < v_max < 35 km/h" 8 "35 km/h < v_max < 40 km/h" 9 "40 km/h < v_max < 45 km/h" 10 "45 km/h < v_max < 50 km/h" 11 "50 km/h < v_max < 60 km/h" 12 "60 km/h < v_max < 70 km/h" 13 "70 km/h < v_max < 80 km/h" 14 "80 km/h < v_max < 90 km/h" 15 "90 km/h < v_max < 100 km/h" 16 "100 km/h < v_max < 110 km/h" 17 "110 km/h < v_max < 120 km/h" 18 "120 km/h < v_max < 130 km/h" 19 "130 km/h < v_max < 140 km/h" 20 "140 km/h < v_max < 150 km/h" 21 "150 km/h < v_max < 160 km/h" 22 "160 km/h < v_max" 23 "Geschwindigkeitsgebot aufgehoben" 24 "..0x1F ungültig";
VAL_ 1124 PSD_Ges_Typ 0 "Geschw_Klasse_des_Kartendatensuppliers" 1 "Explizit_abgeleiteten_Begrenzung" 2 "Durch_Gesetzg_vorgeg_allgem_Gebot_fuer_uebertr_Rahmenbed" 3 "Init";
VAL_ 1124 PSD_Ges_Spur_Geschw_Begrenzung 0 "Gebot_fuer_alle_spuren_gueltig";
VAL_ 1124 PSD_Ges_Geschwindigkeit_Gespann 0 "alle Fahrzeuge" 1 "PKW mit Gespann" 2 "LKW, Busse, etc.";
VAL_ 1124 PSD_Ges_Geschwindigkeit_Witter 0 "Witterungsunabh䮧ig" 1 "N䳳e, Regen, Niederschlag" 2 "Gl䴴e" 3 "Nebel";
VAL_ 1124 PSD_Ges_Geschwindigkeit_Tag_Anf 0 "kein Beginn definiert" 1 "Montag" 2 "Dienstag" 3 "Mittwoch" 4 "Donnerstag" 5 "Freitag" 6 "Samstag" 7 "Sonntag";
VAL_ 1124 PSD_Ges_Geschwindigkeit_Tag_Ende 0 "kein Ende definiert" 1 "Montag" 2 "Dienstag" 3 "Mittwoch" 4 "Donnerstag" 5 "Freitag" 6 "Samstag" 7 "Sonntag";
VAL_ 1124 PSD_Ges_Geschwindigkeit_Std_Anf 25 "kein Beginn für stundenweise Einschr䮫ungen";
VAL_ 1124 PSD_Ges_Geschwindigkeit_Std_Ende 25 "kein Ende für stundenweise Einschr䮫ungen";
VAL_ 1124 PSD_Ges_Ueberholverbot 0 "kein ܢerholverbot" 1 "alle Fahrzeuge" 2 "ܢerholverbot für PKW mit Gespann" 3 "ܢerholverbot für LKW, Busse, etc.";
VAL_ 1124 PSD_Ges_Wechselverkehrszeichen 0 "Kein Wechselverkehrszeichen" 1 "Wechselverkehrszeichen links" 2 "Wechselverkehrszeichen rechts" 3 "Wechselverkehrszeichen links und rechts" 4 "Wechselverkerhszeichen über der Fahrbahn";
VAL_ 1124 PSD_Wechselverkehrszeichen_Typ 0 "kein_Wechselverkehrszeichen" 1 "LED_Wechselverkehrszeichen" 2 "nicht_LED_Wechselverkehrszeichen";
VAL_ 1124 PSD_Ges_Gesetzlich_Kategorie 0 "kein_legales_Verbot" 1 "innerorts" 2 "ausserorts" 3 "Autobahn";
VAL_ 1124 PSD_Ges_Gesetzlich_Zusatz 0 "kein_Zusatz_zu_legalem_Gebot" 1 "Anhaenger_Klasse_1" 2 "Anhaenger_Klasse_2";
VAL_ 1124 PSD_Ges_Verkehrszeichen_Quelle 0 "VZA_kein_Onlinedienst" 1 "nur_VZA" 2 "nur_VZO" 3 "VZA_und_VZO";
VAL_ 1124 PSD_Ges_Attribute_Komplett 0 "Attribute_nicht_komplett" 1 "Attribute_komplett";
VAL_ 1124 PSD_Baum_Laenge_VZ 0 "Ost" 1 "West";
VAL_ 1124 PSD_Baum_Breite_VZ 0 "Nord" 1 "Sued";
VAL_ 1124 PSD_Steigung_1_Segment_ID 0 "keine_Segment_Information_vorhanden" 1 "Fehler";
VAL_ 1124 PSD_Steigung_1_A_Steigung 126 "mehr_als_15_Prozent" 127 "Steigung_unbekannt";
VAL_ 1124 PSD_Steigung_1_A_Vorz 0 "Gefaelle" 1 "Steigung";
VAL_ 1124 PSD_Steigung_1_B_Steigung 126 "mehr_als_15_Prozent" 127 "Steigung_unbekannt";
VAL_ 1124 PSD_Steigung_1_B_Vorz 0 "Gefaelle" 1 "Steigung";
VAL_ 1124 PSD_Steigung_1_Attribute_kompl 0 "Steigungen_nicht_komplett" 1 "Steigungen_komplett";
VAL_ 1124 PSD_Steigung_2_Segment_ID 0 "keine_Segment_Information_vorhanden" 1 "Fehler";
VAL_ 1124 PSD_Steigung_2_Steigung 126 "mehr_als_15_Prozent" 127 "Steigung_unbekannt";
VAL_ 1124 PSD_Steigung_2_Vorz 0 "Gefaelle" 1 "Steigung";
VAL_ 1124 PSD_Steigung_2_Attribute_kompl 0 "Steigungen_nicht_komplett" 1 "Steigungen_komplett";
VAL_ 1153 ZR_LoGeWa_Event_Kombiwarnung 0 "Init" 1 "Unfall" 2 "Traktionsverlust" 3 "Panne" 4 "Sichtbehinderung" 5 "Aquaplaning" 6 "EEBL" 7 "SEF_stat" 8 "SEF_dyn_allgemein" 9 "SEF_dyn_vorne" 10 "SEF_dyn_hinten" 11 "SEF_dyn_links" 12 "SEF_dyn_rechts" 13 "Stauende";
VAL_ 1153 Nav_FoD_Status 0 "Init" 1 "Nav_permanently_available" 2 "FoD_Nav_not_activated" 3 "FoD_Nav_activated" 4 "R4N_Nav_not_activated";
VAL_ 1153 MIB_Tongenerator_PH_verfuegbar 0 "Tongenerator_nicht_verfuegbar" 1 "Tongenerator_vorne_verfuegbar" 2 "Tongenerator_hinten_verfuegbar" 3 "Tongenerator_gesamt_verfuegbar" 4 "Tongenerator_im_AMP";
VAL_ 1153 DSSS_Warning 0 "No_Warning" 1 "Red_traffic_light_guidance" 2 "Stop_sign_warning" 3 "Rear_end_collision_avoidance_warning" 4 "Intersection_collision_avoidance_warning_right" 5 "Intersection_collision_avoidance_warning_left" 6 "turn_right_collision_avoidance" 7 "turn_left_collision_avoidance" 8 "pedestrian_crossing_right" 9 "pedestrian_crossing_left" 10 "bicycle_collision_right" 11 "bicycle_collision_left";
VAL_ 1153 ZR_Kindersicherung_RSE 0 "inaktiv" 1 "aktiv";
VAL_ 1153 ZR_RSE_aktivieren 0 "inaktiv" 1 "aktiv";
VAL_ 1153 MMI_SDS_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 1153 MU_SecondDisplay 0 "Init" 1 "gueltige_Karte" 2 "ungueltiges_Signal" 3 "Nicht_Verbaut";
VAL_ 1153 MMI_Telefon_aktiv 0 "Telefongespraech_nicht_aktiv" 1 "Telefongespraech_aktiv";
VAL_ 1153 MMI_Gurt_Mic_ref 0 "Downlink_inaktiv" 1 "Downlink_aktiv";
VAL_ 1153 ZR_Sta_Inszenierung 0 "Init" 1 "Inszenierung_deaktiviert" 2 "Inszenierung_Start" 3 "Inszenierung_Stop";
VAL_ 1153 MMI_Gauges_active 0 "gauges_inactive" 1 "gauges_active";
VAL_ 1153 ZR_MXB_Manoever_Ansage 0 "keine_Ansage" 1 "Ansage_ohne_Richtung" 2 "Ansage_rechte_Richtung" 3 "Ansage_linke_Richtung";
VAL_ 1153 ZR_LAPP_Sondermodus_Status 0 "keine_Anforderung" 1 "Sondermodus_aktiv" 2 "Aktivierung_nicht_moeglich";
VAL_ 1153 MMI_StartStopp_Info 0 "Stoppfreigabe" 1 "Stoppverbot" 2 "Startanforderung" 3 "Fehler";
VAL_ 1153 ZR_Parken_Sondermodus 0 "Init" 1 "Sondermodus_aktiv" 2 "Sondermodus_nicht_verfuegbar" 3 "WLAN_aus" 4 "WLAN_auscodiert" 15 "keine_WLAN_HW";
VAL_ 1153 ZR_Rundenbewertung 0 "Bewertung_nicht_aktiv" 1 "langsamer" 2 "gleich_schnell" 3 "schneller" 4 "reserviert" 5 "reserviert" 6 "reserviert" 7 "reserviert" 8 "reserviert" 9 "reserviert" 10 "reserviert" 11 "reserviert" 12 "reserviert" 13 "reserviert" 14 "Init" 15 "Funktion_nicht_verbaut";
VAL_ 1153 ZR_Rundenfortschritt 254 "Init" 255 "Funktion_nicht_verbaut";
VAL_ 1155 Mo_Powermeter_Grenze 4093 "OFF";
VAL_ 1155 MO_Text_Aktivierung_Antrieb 0 "keine_Anzeige" 1 "Ladestecker_nicht_fahrbereit" 2 "erneute_Aktivierung_erforderlich" 3 "Warten_Antrieb_wird_aktiviert" 4 "zum_Fahren_Bremse_und_Fahrstufe" 5 "h2_Tankklappe_nicht_fahrbereit" 6 "zum_Starten_Bremse_treten" 7 "zum_Starten_Kupplung_treten" 15 "Init";
VAL_ 1155 MO_Powermeter_Inszenierung_aktiv 0 "keine_Anzeige" 1 "Inszenierung_aktiv";
VAL_ 1155 MO_Powermeter_Charge_Grenze 1022 "Init" 1023 "Fehler";
VAL_ 1155 MO_Powermeter_Grenze_strategisch 4094 "Init" 4095 "Fehler";
VAL_ 1155 MO_Powermeter_untere_E_Grenze 4094 "Init" 4095 "Fehler";
VAL_ 1155 MO_Powermeter_obere_E_Grenze 4094 "Init" 4095 "Fehler";
VAL_ 1175 PH_Visualisierung 0 "Aus" 1 "Angefragt" 2 "InAnzeige" 3 "Abgefordert" 6 "Init" 7 "Fehler";
VAL_ 1175 PDC_Tonausgabe_Front 0 "kein_Ton" 1 "Intervallton_1" 2 "Intervallton_2" 3 "Intervallton_3" 4 "Intervallton_4" 5 "Intervallton_5" 6 "Dauerton" 7 "Fehlerton" 8 "RCTATon";
VAL_ 1175 PDC_Tonausgabe_Heck 0 "kein_Ton" 1 "Intervallton_1" 2 "Intervallton_2" 3 "Intervallton_3" 4 "Intervallton_4" 5 "Intervallton_5" 6 "Dauerton" 7 "Fehlerton" 8 "Erstwarnton_hinten" 9 "RCTATon";
VAL_ 1175 PH_nachtr_Stopp_Anf 0 "nicht_angefordert" 1 "angefordert";
VAL_ 1175 PH_Abschaltursache 0 "keine, bzw. System aktiv" 1 "Abschaltung durch Herausnahme R-Gang" 2 "Abschaltung ueber Geschwindigkeit" 3 "Abschaltung ueber PDC-Taster" 4 "Abschaltung wegen PLA" 5 "Abschaltung durch KL 15 Bit = 0" 6 "tbd." 7 "Fehler-/gestört Zustand, System noch aktiv";
VAL_ 1175 PH_Opt_Anzeige_V_ein 0 "optische Anzeige vorne nicht aktiv" 1 "optische Anzeige vorne aktiviert";
VAL_ 1175 PH_Opt_Anzeige_H_ein 0 "Optische Anzeige aus" 1 "optische Anzeige hinten aktiviert";
VAL_ 1175 PH_Opt_Anz_V_Hindernis 0 "optische Anzeige vorne meldet kein Hindernis" 1 "optische Anzeige vorne meldet Hindernis im Warnbereich";
VAL_ 1175 PH_Opt_Anz_H_Hindernis 0 "optische Anzeige hinten meldet kein Hindernis" 1 "optische Anzeige hinten meldet Hindernis im Warnbereich";
VAL_ 1175 PH_Tongeber_V_aktiv 0 "Tongeber vorne nicht aktiv" 1 "Tongeber vorne  aktiv";
VAL_ 1175 PH_Tongeber_H_aktiv 0 "Tongeber hinten nicht aktiv" 1 "Tongeber hinten aktiv";
VAL_ 1175 PH_Tongeber_mute 0 "Tongeber nicht stummgeschaltet" 1 "Tongeber stummgeschaltet";
VAL_ 1175 PH_Anf_Audioabsenkung 0 "Keine_Audioabsenkung" 1 "Anforderung_Audioabsenkung";
VAL_ 1175 PH_Tongeber_H_verfuegbar 0 "nicht verfügbar" 1 "verfügbar";
VAL_ 1175 PLA_Anf_Aufschaltung_RVC 0 "Keine_RVC_Aufschalteanf" 1 "RVC_Aufschalteanforderung";
VAL_ 1175 PH_Taster 0 "Taster_nicht_gedrueckt" 1 "Taster_gedrueckt" 2 "reserviert" 3 "Fehler";
VAL_ 1175 PH_Anf_Verdeck 0 "Verdeckbetrieb_freigegeben" 1 "Verdeckbetrieb_sperren" 2 "reserviert" 3 "reserviert";
VAL_ 1175 PH_StartStopp_Info 0 "Motorlauf_nicht_notwendig_(Stoppfreigabe)" 1 "Motoranlauf_nicht_zwingend_notwendig_(Stoppverbot,keine_Startanforderung)" 2 "Motoranlauf_zwingend_notwendig_(Startanforderung)" 3 "Systemfehler";
VAL_ 1175 PH_Stoermeldung 0 "keine_Anzeige_kein_Gong" 1 "Textmeldung_PDC_hinten_gestoert_mit_Gong" 2 "Textmeldung_PDC_hinten_gestoert_ohne_Gong" 3 "Textmeldung_PDC_vorn_gestoert_mit_Gong" 4 "Textmeldung_PDC_vorn_gestoert_ohne_Gong" 5 "Textmeldung_PDC_gestoert_mit_Gong" 6 "Textmeldung_PDC_gestoert_ohne_Gong" 7 "Textmeldung_PDC_seitlich_gestoert_mit_Gong" 8 "Textmeldung_PDC_seitlich_gestoert_ohne_Gong";
VAL_ 1175 PH_defekt 0 "PH aktiv" 1 "PH wurde aufgrund eines Defektes deaktiviert";
VAL_ 1175 PH_gestoert 0 "PH aktiv" 1 "PH wurde auf Grund einer Stoerung tempraer deaktiviert";
VAL_ 1175 PH_Systemzustand 0 "Anlage aus" 1 "Anlage durch einlegen R-Gang aktiviert" 2 "Anlage manuell durch Taster aktiviert" 3 "Anlage automatisch aktiviert" 4 "Anlage durch ARA aktiviert" 6 "Init - Initialisierungsphase nach Kl. 15 ein / Reset" 7 "Fehler-/gestört Zustand, System noch aktiv";
VAL_ 1175 PH_Display_Kundenwunsch 0 "Anzeige aus" 1 "Grafik" 2 "Rear View" 3 "automatisch";
VAL_ 1283 HVK_Istmodus_Anf 0 "nicht_angefordert" 1 "angefordert";
VAL_ 1283 HVK_TN1_Sollmodus 0 "HV_Off" 1 "HV_On" 2 "reserviert" 3 "Initialisierung";
VAL_ 1283 HVK_MO_EmSollzustand 0 "HvOff" 1 "HvStbyReq" 2 "HvStbyWait" 3 "HvBattOnReq" 4 "HvBattOnWait" 10 "HvOnIdle" 18 "HvOnDrvReq" 19 "HvOnDrvWait" 20 "HvOnDrvRdy" 28 "HvStepUpReq" 29 "HvStepUpWait" 30 "HvStepUp" 38 "HvStepDownReq" 39 "HvStepDownWait" 40 "HvStepDown" 46 "HvAcChPreReq" 47 "HvAcChPreWait" 48 "HvAcChReq" 49 "HvAcChWait" 50 "HvAcCh" 56 "HvDcChPreReq" 57 "HvDcChPreWait" 58 "HvDcChReq" 59 "HvDcChWait" 60 "HvDcCh" 67 "HvChOffReq" 68 "HvChOffWait" 69 "HvOnIdleReq" 70 "HvOnIdleWait" 96 "HvCpntOffReq" 97 "HvCpntOffWait" 98 "HvBattOffReq" 99 "HvBattOffWait" 109 "HvDcDcFailOffReq" 110 "HvDcDcFail" 119 "HvElmOffReq" 120 "HvElmOff" 126 "HvFailCpntOffReq" 127 "HvFailCpntOffWait" 128 "HvFailBattOffReq" 129 "HvFailBattOffWait" 130 "HvFailBattOff" 138 "HvFailUCtlReq" 139 "HvFailUCtlWait" 140 "HvFailUCtl" 150 "HvEmgcyOff" 255 "Init";
VAL_ 1283 HVK_BMS_Sollmodus 0 "HV_Off" 1 "HV_On" 3 "AC_Laden_erw" 4 "AC_Laden" 6 "DC_Laden" 7 "Init";
VAL_ 1283 HVK_DCDC_Sollmodus 0 "Standby" 1 "HV_On_Vorladen" 2 "Tiefsetzen" 3 "Hochsetzen" 4 "Pruefpuls_12V" 7 "Initialisierung";
VAL_ 1283 HVK_EKK_Sollmodus 0 "Keine_Freigabe" 1 "Freigabe" 2 "Freigabe_ausgesetzt" 7 "Init";
VAL_ 1283 HVK_HVPTC_Sollmodus 0 "Keine_Freigabe" 1 "Freigabe" 2 "Freigabe_ausgesetzt" 7 "Init";
VAL_ 1283 HVK_HVLM_Sollmodus 0 "keine_Freigabe" 1 "Freigabe_Lademanager" 2 "Vorladung_AC_Pfad" 7 "Init";
VAL_ 1283 HVK_HV_Netz_Warnungen 0 "Keine_Warnung" 1 "Warntext_HV_Netz_Anf" 2 "Fehlertext_HV_Netz_Anf" 3 "Warnung_Kein_Wiederstart_moeglich";
VAL_ 1283 HV_Bordnetz_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 1283 HV_Bordnetz_Fehler 0 "kein_Fehler" 1 "Fehler";
VAL_ 1283 HVK_Gesamtst_Spgfreiheit 0 "Funktion_Init__ohne_Funktion" 1 "HV_System_spannungsfrei" 2 "HV_System_nicht_spannungsfrei" 3 "Fehler";
VAL_ 1283 HVK_AktiveEntladung_Anf 0 "nicht_angefordert" 1 "angefordert";
VAL_ 1283 HVK_Iso_Messung_Start 0 "keine_Messung" 1 "Messung_HV_Netz_1" 2 "Messung_HV_Netz_2" 4 "deaktiviert";
VAL_ 1283 HVK_DCDC_EKK_Sollmodus 0 "aus" 1 "ein" 2 "reserviert" 3 "Init";
VAL_ 1312 LoGeWa_Event_Kombiwarnung 0 "Init" 1 "Unfall" 2 "Traktionsverlust" 3 "Panne" 4 "Sichtbehinderung" 5 "Aquaplaning";
VAL_ 1312 AB_Anforderung_eCall 0 "keine_Anforderung" 1 "Anforderung";
VAL_ 1312 AB_Anprall_Seite_Beifahrer 0 "kein_Anprall" 1 "Anprall_erkannt";
VAL_ 1312 AB_Anprall_Rollover 0 "kein_Anprall" 1 "Anprall_erkannt";
VAL_ 1312 AB_Anprall_FGS 0 "kein_Anprall" 1 "Anprall_erkannt";
VAL_ 1312 AB_Anprall_Front_Beifahrer 0 "kein_Anprall" 1 "Anprall_erkannt";
VAL_ 1312 AB_Anprall_Front_Fahrer 0 "kein_Anprall" 1 "Anprall_erkannt";
VAL_ 1312 AB_Anprall_Heck_Beifahrer 0 "kein_Anprall" 1 "Anprall_erkannt";
VAL_ 1312 AB_Anprall_Heck_Fahrer 0 "kein_Anprall" 1 "Anprall_erkannt";
VAL_ 1312 AB_Wickelklappung_Reihe2_MI 0 "nicht_verbaut" 1 "nicht_verfuegbar__Fehler_oder_Init" 2 "nicht_verriegelt" 3 "verriegelt";
VAL_ 1312 AB_Belegung_VB 0 "nicht_verfuegbar" 1 "Fehler" 2 "nicht_belegt" 3 "belegt";
VAL_ 1312 AB_Abschaltanf_SIH_BF 0 "Normalbetrieb" 1 "Abschaltung_angefordert";
VAL_ 1312 AB_Anprall_Seite_Fahrer 0 "kein_Anprall" 1 "Anprall_erkannt";
VAL_ 1312 SC_PAO_Schriftzug_Anf 0 "LED aus" 1 "LED an" 2 "LED blinken" 3 "reserviert";
VAL_ 1312 SC_PAO_ON_Anf 0 "LED aus" 1 "LED an" 2 "LED blinken" 3 "reserviert";
VAL_ 1312 SC_PAO_OFF_Anf 0 "LED aus" 1 "LED an" 2 "LED blinken" 3 "reserviert";
VAL_ 1312 AB_Crashschwere 0 "kein_Ereignis" 1 "Crashschwere_1" 2 "Crashschwere_2" 3 "Crashschwere_3" 4 "Crashschwere_4" 5 "Crashschwere_5" 6 "Crashschwere_2_bis_5" 7 "Fehler";
VAL_ 1312 AB_Anforderung_USM 0 "keine_Anforderung" 1 "Anforderung";
VAL_ 1312 AB_Gurtschloss_FA 0 "nicht_verbaut" 1 "nicht_verfügbar (Fehler oder Init)" 2 "nicht_gesteckt" 3 "gesteckt";
VAL_ 1312 AB_Gurtschloss_BF 0 "nicht_verbaut" 1 "nicht_verfügbar (Fehler oder Init)" 2 "nicht_gesteckt" 3 "gesteckt";
VAL_ 1312 AB_Gurtschloss_Reihe2_FA 0 "nicht_verbaut" 1 "nicht_verfügbar (Fehler oder Init)" 2 "nicht_gesteckt" 3 "gesteckt";
VAL_ 1312 AB_Gurtschloss_Reihe2_MI 0 "nicht_verbaut" 1 "nicht_verfügbar (Fehler oder Init)" 2 "nicht_gesteckt" 3 "gesteckt";
VAL_ 1312 AB_Gurtschloss_Reihe2_BF 0 "nicht_verbaut" 1 "nicht_verfügbar (Fehler oder Init)" 2 "nicht_gesteckt" 3 "gesteckt";
VAL_ 1312 AB_Gurtschloss_Reihe3_FA 0 "nicht_verbaut" 1 "nicht_verfügbar (Fehler oder Init)" 2 "nicht_gesteckt" 3 "gesteckt";
VAL_ 1312 AB_Gurtschloss_Reihe3_MI 0 "nicht_verbaut" 1 "nicht_verfügbar (Fehler oder Init)" 2 "nicht_gesteckt" 3 "gesteckt";
VAL_ 1312 AB_Gurtschloss_Reihe3_BF 0 "nicht_verbaut" 1 "nicht_verfügbar (Fehler oder Init)" 2 "nicht_gesteckt" 3 "gesteckt";
VAL_ 1312 AB_Sitzpos_Sens_FA 0 "nicht verfügbar" 1 "Fehler" 2 "Sitz nicht vorne" 3 "Sitz vorne";
VAL_ 1312 AB_Sitzpos_Sens_BF 0 "nicht verfügbar" 1 "Fehler" 2 "Sitz nicht vorne" 3 "Sitz vorne";
VAL_ 1312 AB_Wickelklappung_Reihe2_BF 0 "nicht_verbaut" 1 "nicht_verfuegbar__Fehler_oder_Init" 2 "nicht_verriegelt" 3 "verriegelt";
VAL_ 1312 AB_Wickelklappung_Reihe2_FA 0 "nicht_verbaut" 1 "nicht_verfuegbar__Fehler_oder_Init" 2 "nicht_verriegelt" 3 "verriegelt";
VAL_ 1349 AB_Gurtwarn_Reihe2_FA 0 "nicht_verbaut" 1 "keine_Gurtwarnung_Sitz_leer" 2 "keine_Gurtwarnung_Sitz_belegt" 3 "Gurtwarnung_Sitz_belegt";
VAL_ 1349 AB_Gurtwarn_Reihe2_BF 0 "nicht_verbaut" 1 "keine_Gurtwarnung_Sitz_leer" 2 "keine_Gurtwarnung_Sitz_belegt" 3 "Gurtwarnung_Sitz_belegt";
VAL_ 1349 AbstWarn_MV_FAS_Fkt_Status 0 "Init" 1 "Funktion_Ist_Ein" 2 "Funktion_Ist_Aus" 3 "Fehler";
VAL_ 1349 WarnBrems_Charisma_Status 0 "Init" 1 "verfügbar" 2 "nicht verfügbar" 3 "asynchron durch Fahrerwunsch";
VAL_ 1349 WarnBrems_Charisma_FahrPr 0 "keine_Funktion" 1 "Programm_1" 2 "Programm_2" 3 "Programm_3" 4 "Programm_4" 5 "Programm_5" 6 "Programm_6" 7 "Programm_7" 8 "Programm_8" 9 "Programm_9" 10 "Programm_10" 11 "Programm_11" 12 "Programm_12" 13 "Programm_13" 14 "Programm_14" 15 "Programm_15";
VAL_ 1349 AB_Gurtwarn_Reihe2_MI 0 "nicht_verbaut" 1 "keine_Gurtwarnung_Sitz_leer" 2 "keine_Gurtwarnung_Sitz_belegt" 3 "Gurtwarnung_Sitz_belegt";
VAL_ 1349 AB_Gurtwarn_Reihe3_FA 0 "nicht_verbaut" 1 "keine_Gurtwarnung_Sitz_leer" 2 "keine_Gurtwarnung_Sitz_belegt" 3 "Gurtwarnung_Sitz_belegt";
VAL_ 1349 AB_Gurtwarn_Reihe3_MI 0 "nicht_verbaut" 1 "keine_Gurtwarnung_Sitz_leer" 2 "keine_Gurtwarnung_Sitz_belegt" 3 "Gurtwarnung_Sitz_belegt";
VAL_ 1349 AB_Gurtwarn_Reihe3_BF 0 "nicht_verbaut" 1 "keine_Gurtwarnung_Sitz_leer" 2 "keine_Gurtwarnung_Sitz_belegt" 3 "Gurtwarnung_Sitz_belegt";
VAL_ 1349 LGI_FAS_Fkt_Status 0 "Init" 1 "Funktion_Ist_Ein" 2 "Funktion_Ist_Aus" 3 "Fehler";
VAL_ 1349 PreCrash_FAS_Fkt_Status 0 "Init" 1 "Funktion_Ist_Ein" 2 "Funktion_Ist_Aus" 3 "Fehler";
VAL_ 1349 AB_SBR_hinten_verbau 0 "ohne_SBR_hinten" 1 "mit_SBR_hinten" 2 "nicht_definiert" 3 "Init";
VAL_ 1349 AWV_Einstellung_System_ASG 0 "deaktiviert" 1 "aktiviert";
VAL_ 1349 AWV_Einstellung_Warnung_ASG 0 "Aus" 1 "Setting_2" 2 "Setting_3" 3 "Setting_4" 4 "Setting_5" 5 "Ein";
VAL_ 1349 SC_PreSense_Modus_Warnung_NV 0 "Aus" 1 "Setting_2" 2 "Setting_3" 3 "Setting_4" 4 "Setting_5" 5 "Ein";
VAL_ 1349 SC_PreSense_Modus_Warnung_MV 0 "Aus" 1 "Setting_2" 2 "Setting_3" 3 "Setting_4" 4 "Setting_5" 5 "Ein";
VAL_ 1349 SC_PreSense_Modus_System_MV 0 "deaktiviert" 1 "aktiviert";
VAL_ 1349 SC_PreSense_Modus_System_NV 0 "deaktiviert" 1 "aktiviert";
VAL_ 1349 SC_PreSense_Modus_System_KAS 0 "deaktiviert" 1 "aktiviert";
VAL_ 1355 PH_Verschmutzungsmeldung 0 "keine_Anzeige_kein_Gong" 1 "Textmeldung_PDC_hinten_verschmutzt_mit_Gong" 2 "Textmeldung_PDC_hinten_verschmutzt_ohne_Gong" 3 "Textmeldung_PDC_vorn_verschmutzt_mit_Gong" 4 "Textmeldung_PDC_vorn_verschmutzt_ohne_Gong" 5 "Textmeldung_PDC_verschmutzt_mit_Gong" 6 "Textmeldung_PDC_verschmutzt_ohne_Gong" 7 "Textmeldung_PDC_seitlich_verschmutzt_mit_Gong" 8 "Textmeldung_PDC_seitlich_verschmutzt_ohne_Gong";
VAL_ 1355 PH_Aufschaltursache 0 "Anlage aus" 1 "Anlage durch einlegen R-Gang aktiviert" 2 "Anlage manuell durch Taster aktiviert" 3 "Anlage automatisch aktiviert" 4 "Anlage durch ARA aktiviert" 5 "Rueckwaertsrollen" 6 "Anlage_durch_PLA_aktiviert" 7 "Anlage_durch_IPA_aktiviert" 8 "Anlage_durch_FA_aktiviert" 9 "Anlage_durch_RBF_oder_MA_aktiviert" 10 "Anlage_durch_Smartphone_aktiviert" 11 "Anlage_durch_Jokertaste_aktiviert" 12 "Anlage_durch_Favoritentasten_aktiviert" 13 "Anlage_durch_SideViewTaster_aktiviert" 14 "Anlage_durch_RCTA_aktiviert" 15 "Anlage_durch_KAS_aktiviert" 16 "Anlage_durch_AWC_aktiviert" 30 "Init_Initialisierungsphase_nach_Kl_15_ein_oder_Reset" 31 "Fehler_gestoerter_Zustand_System_noch_aktiv";
VAL_ 1355 PH_Ton_Ausgabe 0 "Init" 1 "Intervallton_vorne" 2 "Intervallton_hinten" 3 "Dauerton_vorne" 4 "Dauerton_hinten" 5 "Quittierungston" 6 "Reserviert" 7 "Fehler";
VAL_ 1355 PH_Ton_Pausenlaenge 0 "Init" 1 "Pause_5ms" 2 "Pause_10ms" 3 "Pause_15ms" 100 "Pause_500ms" 101 "Reserviert" 126 "Reserviert" 127 "Fehler";
VAL_ 1355 PH_Ton_Richtung 0 "vorne" 1 "vorne_rechts" 2 "rechts" 3 "rechts_hinten" 4 "hinten" 5 "hinten_links" 6 "links" 7 "vorne_links";
VAL_ 1355 PH_Ton_Lautstaerke 0 "Minimallautstaerke" 1 "Lautstaerke_1" 2 "Lautstaerke_2" 3 "Lautstaerke_3" 4 "Lautstaerke_4" 5 "Lautstaerke_5" 6 "Lautstaerke_6" 7 "Maximallautstaerke";
VAL_ 1355 BCM_WAH_Meldung 0 "Init" 1 "Fehlermeldung_bei_Nichtverfuegbarkeit_mit_Gong" 2 "Fehlermeldung_bei_Nichtverfuegbarkeit_ohne_Gong" 3 "Anzeige_dass_Fahrzeug_erkannt_wurde_und_Funktion_aktiv_ist" 4 "Wiederanfahrhinweis_mit_Gong" 5 "Wiederanfahrhinweis_ohne_Gong";
VAL_ 1355 PDC_Charisma_Status 0 "Init" 1 "verfuegbar" 2 "nicht_verfuegbar" 3 "asynchron_durch_Fahrerwunsch";
VAL_ 1355 PDC_Charisma_FahrPr 0 "keine_Funktion" 1 "Programm_1" 2 "Programm_2" 3 "Programm_3" 4 "Programm_4" 5 "Programm_5" 6 "Programm_6" 7 "Programm_7" 8 "Programm_8" 9 "Programm_9" 10 "Programm_10" 11 "Programm_11" 12 "Programm_12" 13 "Programm_13" 14 "Programm_14" 15 "Programm_15";
VAL_ 1361 WFS_Schluessel_Fahrberecht 0 "kein authorisierter Schluessel erkannt" 1 "aktive_Funkschluessel_Nr_01" 2 "aktive_Funkschluessel_Nr_02" 3 "aktive_Funkschluessel_Nr_03" 4 "aktive_Funkschluessel_Nr_04" 5 "aktive_Funkschluessel_Nr_05" 6 "aktive_Funkschluessel_Nr_06" 7 "aktive_Funkschluessel_Nr_07" 8 "aktive_Funkschluessel_Nr_08" 9 "ungueltig_09" 10 "ungueltig_10" 11 "ungueltig_11" 12 "ungueltig_12" 13 "ungueltig_13" 14 "ungueltig_14" 15 "digitaler_Schluessel_aktiv";
VAL_ 1361 WFS_ID_Geb_autorisiert 0 "Schluessel_nicht_authorisiert" 1 "Schluessel authorisiert";
VAL_ 1361 WFS_ID_Geb_autorisiert_in_Kl15 0 "nicht_authorisiert_in_Kl15" 1 "authorisiert_in_Kl15";
VAL_ 1361 WFS_ID_Geb_steckt 0 "kein_Schluessel_im_Zuendschloss" 1 "Schluessel steckt im Zuendschloss";
VAL_ 1361 WFS_Schluessel_Soll 0 "Init";
VAL_ 1361 WFS_Schluessel_Ist 0 "Init";
VAL_ 1361 WFS_Safe 0 "Init" 1 "WFS nicht deaktiviert";
VAL_ 1361 WFS_LZ 0 "nicht def." 1 "LZ 1" 2 "LZ 2" 3 "LZ 3";
VAL_ 1361 WFS_ELV_authorisiert 0 "ELV_nicht_authorisiert" 1 "ELV_authorisiert";
VAL_ 1361 WFS_LF_Aktiv 0 "Funktempfang_nicht_aktiv" 1 "LF_Funkempfang_aktiv";
VAL_ 1361 WFS_Betrieb_Lesespule 0 "keine_Schluesselkomm_Lesespule" 1 "Schluesselkomm_Lesespule";
VAL_ 1361 WFS_Klemmenfreigabe 0 "keine_Freigabe" 1 "Freigabe";
VAL_ 1361 WFS_Fahrerhinweise 0 "Keine_Anzeigeanforderung" 1 "Schuessel_Auth_niO" 2 "Notlauf_aktiv" 3 "Schluessel_Kessy_nicht_gefunden";
VAL_ 1361 WFS_Parken_Status 0 "Limitierung_inaktiv" 1 "Limitierung_aktiv" 2 "Abbruch";
VAL_ 1411 BCM_FH_Freigabe 1 "Funktionsfreigabe Fensterheber";
VAL_ 1411 BCM_Komfortfkt_Freigabe 0 "Komfortfunktion gesperrt" 1 "globale Freigabe der Komfortfunktion erteilt";
VAL_ 1411 BCM_HSK_Freigabe 0 "erlauben" 1 "sperren";
VAL_ 1411 BCM_Verdeck_Freigabe 0 "Verdeckbetrieb nicht erlaubt" 1 "Verdeckbetrieb erlaubt";
VAL_ 1411 ZV_verriegelt_intern_ist 1 "Fahrzeug innen verriegelt; Istzustand";
VAL_ 1411 ZV_verriegelt_extern_ist 1 "Fahrzeug_aussen_verriegelt_Istzustand";
VAL_ 1411 ZV_verriegelt_intern_soll 1 "Fahrzeug verriegelt intern; Sollzusstand";
VAL_ 1411 ZV_verriegelt_extern_soll 1 "Fahrzeug verriegelt extern; Sollzusstand";
VAL_ 1411 ZV_gesafet_extern_ist 1 "Fahrzeug aussen gesafet; Istzustand";
VAL_ 1411 ZV_gesafet_extern_soll 1 "Fahrzeug_aussen_gesafet_Sollzustand";
VAL_ 1411 ZV_Einzeltuerentriegelung 1 "Fahrzeug an Einzeltuer entriegelt";
VAL_ 1411 ZV_Heckeinzelentriegelung 1 "Heckdeckeleinzelentriegelung";
VAL_ 1411 ZV_FT_offen 0 "FT_geschlossen" 1 "FT_geoeffnet";
VAL_ 1411 ZV_BT_offen 0 "BT_geschlossen" 1 "BT_geoeffnet";
VAL_ 1411 ZV_HFS_offen 0 "geschlossen" 1 "offen";
VAL_ 1411 ZV_HBFS_offen 0 "geschlossen" 1 "offen";
VAL_ 1411 ZV_HD_offen 1 "Heckdeckel_auf";
VAL_ 1411 ZV_HS_offen 0 "Heckscheibe_geschlossen" 1 "Heckscheibe auf";
VAL_ 1411 IRUE_aktiv 0 "unscharf" 1 "scharf";
VAL_ 1411 DWA_aktiv 0 "unscharf" 1 "scharf";
VAL_ 1411 HD_Hauptraste 0 "geschlossen" 1 "offen";
VAL_ 1411 HD_Vorraste 0 "geschlossen" 1 "offen";
VAL_ 1411 FFB_CarFinder 0 "nicht aktiv" 1 "aktiv";
VAL_ 1411 FFB_Komfortoeffnen 0 "kein_Komfortoeffnen_empfangen" 1 "Komfortoeffnen ueber Funk empfangen";
VAL_ 1411 FFB_Komfortschliessen 0 "kein_Komfortschliessen_empfangen" 1 "Komfortschliessen ueber Funk empfangen";
VAL_ 1411 ZV_Schluessel_Zugang 0 "kein_auth_Schluessel_erkannt" 1 "aktive_Funkschluessel_Nr_01" 2 "aktive_Funkschluessel_Nr_02" 3 "aktive_Funkschluessel_Nr_03" 4 "aktive_Funkschluessel_Nr_04" 5 "aktive_Funkschluessel_Nr_05" 6 "aktive_Funkschluessel_Nr_06" 7 "aktive_Funkschluessel_Nr_07" 8 "aktive_Funkschluessel_Nr_08" 9 "ungueltig_09" 10 "ungueltig_10" 11 "ungueltig_11" 12 "ungueltig_12" 13 "ungueltig_13" 14 "ungueltig_14" 15 "ungueltig_15";
VAL_ 1411 ZV_SafeFunktion_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 1411 FBS_Warn_Schluessel_Batt 0 "i.O." 1 "defekt";
VAL_ 1411 ZV_Oeffnungsmodus 0 "globale_Oeffnung" 1 "ZV_mit_Einzeltuerentr" 2 "ZV_individuell_selektiv" 3 "ZV_seitenselektiv";
VAL_ 1411 HFS_verriegelt 0 "nicht_verriegelt" 1 "verriegelt";
VAL_ 1411 HFS_gesafet 0 "nicht_gesafet" 1 "gesafet";
VAL_ 1411 HBFS_verriegelt 0 "nicht_verriegelt" 1 "verriegelt";
VAL_ 1411 HBFS_gesafet 0 "nicht_gesafet" 1 "gesafet";
VAL_ 1411 ZV_ist_Zustand_verfuegbar 0 "nicht_verfuegbar" 1 "verfuegbar_alle_TSGs_am_Bus";
VAL_ 1411 IRUE_Taster_Fkts_LED 0 "LED_aus" 1 "LED_ein";
VAL_ 1411 ZV_Tankklappe_offen 0 "Tankklappe_geschlossen" 1 "Tankklappe_offen";
VAL_ 1411 ZV_Rollo_auf 0 "inaktiv" 1 "aktiv";
VAL_ 1411 ZV_Rollo_zu 0 "inaktiv" 1 "aktiv";
VAL_ 1411 ZV_SAD_auf 0 "inaktiv" 1 "aktiv";
VAL_ 1411 ZV_SAD_zu 0 "inaktiv" 1 "aktiv";
VAL_ 1411 BCM_Tankklappensteller_Fehler 0 "kein_Fehler" 1 "Fehler";
VAL_ 1411 ZV_verriegelt_soll 0 "Init" 1 "nicht_verriegelt" 2 "verriegelt";
VAL_ 1413 SI_Sammel_SG_Fehler 61 "Reserviert" 62 "Overflow" 63 "ungueltig";
VAL_ 1413 SI_Diagnose_Aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 1413 SI_QRS_Mode 0 "QRS_Messmodus_nicht_aktiv" 1 "QRS_Messmodus_aktiv";
VAL_ 1413 SI_T_Mode 0 "Transportmodus_nicht_aktiv" 1 "Transportmodus_aktiv";
VAL_ 1413 SI_NWDF 0 "Ueberwachung_nicht_freigegeben" 1 "Ueberwachung_freigegeben";
VAL_ 1413 SI_NWDF_gueltig 0 "NWDF_wird_nicht_unterstuetzt" 1 "NWDF_wird_unterstuetzt";
VAL_ 1413 SI_Sammelfehler 0 "kein_Sammelfehler" 1 "Sammelfehler";
VAL_ 1413 GW_KD_Fehler 0 "kein_KD_Fehler" 1 "KD_Fehler";
VAL_ 1413 SI_T_Schutz 0 "Transportschutz_nicht_aktiv" 1 "Transportschutz_aktiv";
VAL_ 1413 SI_BUS_01 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_02 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_03 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_04 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_05 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_06 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_07 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_08 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_09 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_10 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_11 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_12 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_13 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_14 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_BUS_15 0 "keine_Busruhe" 1 "Busruhe";
VAL_ 1413 SI_Bus_Identifikation 16 "CAN_Diagnose" 17 "CAN_Antrieb" 18 "CAN_Komfort" 19 "CAN_Infotainment" 20 "CAN_Fahrwerk" 21 "CAN_Extended" 22 "CAN_Hybrid" 23 "CAN_Lade" 24 "CAN_Kombi" 25 "CAN_Komfort_2" 26 "CAN_AFS" 33 "CAN_FahrerAssistenzSysteme" 34 "CAN_Migration" 35 "CAN_Connect" 36 "CAN_AnzeigeBedienung" 37 "CAN_ElectricalVehicle" 40 "CAN_Telematik" 41 "CAN_MFL" 42 "CAN_FahrerAssistenzSysteme_2" 48 "FlexRay_A" 49 "FlexRay_B" 97 "VLAN_FAS" 98 "VLAN_Infotainment" 99 "VLAN_Connect" 100 "VLAN_Komfort" 101 "VLAN_Antrieb" 102 "VLAN_Diagnose" 103 "VLAN_Internet" 104 "VLAN_Gateway" 105 "VLAN_Remote_FC" 106 "VLAN_Connect_2" 107 "VLAN_Licht" 108 "VLAN_Charge" 109 "VLAN_Cockpit" 176 "HCP1_CANFD01" 177 "HCP1_CANFD02" 178 "HCP1_CANFD03" 179 "HCP1_CANFD04" 180 "HCP1_CANFD05" 181 "HCP1_CANFD06" 182 "HCP1_CANFD07" 183 "HCP1_CANFD08" 186 "HCP4_CANFD03" 187 "HCP4_CANFD04" 188 "HCP1_CANFD09" 189 "HCP4_CANFD06" 190 "HCP4_CANFD07" 191 "HCP4_CANFD08" 192 "HCP4_CANFD09" 193 "HCP4_CANFD10" 194 "HCP1_CANFD10" 195 "ICAS1_CANFD2_HCP5_CANFD02" 196 "HCP1_CANFD11" 197 "WU_HCP1_CANFD" 198 "WU_HCP2_1_CANFD" 199 "WU_HCP_2_CANFD" 200 "WU_HCP_3_CANFD" 201 "WU_HCP4_CANFD" 202 "WU_HCP3_CANFD02" 204 "ESC_CANFD" 205 "HCP4_CANFD11" 206 "HCP4_CANFD12" 207 "HCP5_CANFD04" 208 "HCP5_CANFD05" 212 "WU_ConMod_CANFD" 213 "WU_PASD_CANFD";
VAL_ 1413 SI_CAB 0 "inaktiv" 1 "CAB_01__Tueren" 2 "CAB_02__Anhaenger" 4 "CAB_03__Nightvision" 8 "CAB_04__Sitzverstellung" 16 "CAB_05__Klappen" 32 "CAB_06__Infotainment" 64 "CAB_07__Sub_Infotainment" 128 "CAB_08__Anzeige" 256 "CAB_09__Laden" 512 "CAB_10__Klima" 1024 "CAB_11__FlexRay" 262144 "CAB_19_VLAN_Remote_FC" 524288 "CAB_20_VLAN_Komfort";
VAL_ 1420 KST_Txt_P_Gang 0 "Aus" 1 "Ein";
VAL_ 1420 KST_Txt_Panikabschaltung 0 "Aus" 1 "Ein";
VAL_ 1420 KST_Anf_Klemmenfreigabe_ELV 0 "nicht_angefordert" 1 "angefordert";
VAL_ 1420 KST_Txt_Komfortabschaltung 0 "inaktiv" 1 "aktiv";
VAL_ 1420 KST_ZAT_betaetigt 0 "Aus" 1 "Ein";
VAL_ 1420 KST_Unterdr_Zuendungsmeldung 0 "keine_Unterdr_der_Zuendungsmeldung" 1 "Unterdr_der_Zuendungsmeldung_aktiv";
VAL_ 1420 KST_aut_Abschaltung_Zuendung 0 "Zuendung_wird_bei_Verl_nicht_deaktiviert" 1 "Zuendung_wird_bei_Verl_deaktiviert";
VAL_ 1420 KST_Anf_ZV_Verriegelung 0 "inaktiv" 1 "aktiv";
VAL_ 1420 Relais_VoKo_angesteuert 0 "nicht_angesteuert" 1 "angesteuert";
VAL_ 1420 RSt_Anforderung_HMS 0 "keine_Anforderung" 1 "halten" 2 "parken" 3 "halten_Standby" 4 "anfahren" 5 "Loesen_ueber_Rampe" 6 "Parken_mit_P";
VAL_ 1440 LS_Helligkeit_IR 254 "Init" 255 "Fehler";
VAL_ 1440 LS_Helligkeit_FW 1022 "Init" 1023 "Fehler";
VAL_ 1440 LS_defekt 0 "OK" 1 "defekt";
VAL_ 1440 LS_Verbau 0 "Init" 1 "Lichtsensor_verbaut";
VAL_ 1440 RS_Regenmenge 14 "Init" 15 "Fehler";
VAL_ 1440 RS_Verbau 0 "Init" 1 "Regensensor verbaut";
VAL_ 1440 RS_Verglasung_schliessen 0 "Verglasung nicht schliessen" 1 "Verglasung schliessen";
VAL_ 1440 RS_defekt 0 "OK" 1 "defekt";
VAL_ 1440 RS_Wischergeschwindigkeit 0 "kein Wischen" 1 "42 Huebe/min" 2 "45 Huebe/min" 3 "48 Huebe/min" 4 "51 Huebe/min" 5 "54 Huebe/min" 6 "57 Huebe/min" 7 "60 Huebe/min";
VAL_ 1440 RLS_Vorfeldhelligkeit_Boost 0 "groesser_24413_Lux" 1 "bis_24413_Lux" 2 "bis_22193_Lux" 3 "bis_20176_Lux" 4 "bis_18342_Lux" 5 "bis_16647_Lux" 6 "bis_15158_Lux" 7 "bis_13780_Lux" 8 "bis_12527_Lux" 9 "bis_11388_Lux" 10 "bis_10353_Lux" 11 "bis_9412_Lux" 12 "bis_8556_Lux" 13 "bis_7778_Lux" 14 "bis_7071_Lux" 15 "bis_6428_Lux";
VAL_ 1442 BMS_Status_ServiceDisconnect 0 "gesteckt" 1 "gezogen";
VAL_ 1442 BMS_Status_Spgfreiheit 0 "Init" 1 "HV_Komponente_spannungsfrei" 2 "HV_Komp_nicht_spannungsfrei" 3 "Fehler_nicht_spannungsfrei";
VAL_ 1442 BMS_OBD_Lampe_Anf 0 "kein_MIL_Request" 1 "MIL_Request";
VAL_ 1442 BMS_IstModus 0 "HV_inaktiv" 1 "Fahren_HV_aktiv" 2 "stBalancing" 3 "externes_Laden" 4 "AC_Laden" 5 "Error_Batt" 6 "DC_Laden" 7 "Init";
VAL_ 1442 BMS_Fehlerstatus 0 "Komponente_IO" 1 "Eingeschr_KompFkt_Isofehler_I" 2 "Eingeschr_KompFkt_Isofehler_II" 3 "Eingeschr_KompFkt_Interlock" 4 "Eingeschr_KompFkt_SD" 5 "Eingeschr_KompFkt_Leistungsred" 6 "Keine_Komponentenfunktion" 7 "Init";
VAL_ 1442 BMS_Kapazitaet_02 2047 "Init";
VAL_ 1442 BMS_Soll_SOC_HiRes 2046 "Init" 2047 "Fehler";
VAL_ 1447 TM_Spiegel_Anklappen 0 "Init" 1 "Spiegel_anklappen";
VAL_ 1447 TM_Nur_Hupen 0 "inaktiv" 1 "aktiv";
VAL_ 1447 TM_Door_Lock 0 "Init" 1 "door_lock";
VAL_ 1447 TM_Door_Unlock 0 "Init" 1 "door_unlock";
VAL_ 1447 TM_Warnblinken 0 "Init" 1 "Warnblinken_on";
VAL_ 1447 TM_Panik_Alarm 0 "Init" 1 "panik_alarm_on";
VAL_ 1447 TM_ZV_Signatur 0 "Init";
VAL_ 1452 HVEM_IstStrom_HVVerbraucher 4094 "Init" 4095 "Fehler";
VAL_ 1452 HVEM_Energie_Klima_Vorgabe_HighR 6 "Init" 7 "Fehler";
VAL_ 1452 HVEM_IstLeistungNA 510 "Init" 511 "Fehler";
VAL_ 1452 HVEM_Leistung_Klima_Vorgabe 254 "Init" 255 "Fehler";
VAL_ 1452 HVEM_Nutzbare_Energie 2045 "Max" 2046 "Init" 2047 "Fehler";
VAL_ 1452 HVEM_Energie_Klima_Vorgabe 254 "Init" 255 "Fehler";
VAL_ 1452 HVEM_MO_MaxLeistungIgnoriert 0 "Max_Leistung_nicht_ignoriert" 1 "Max_Leistung_ignoriert";
VAL_ 1485 DC_Fehlerstatus 0 "Komponente_IO" 1 "Eingeschr_KompFkt_Entlad_defekt" 3 "Eingeschr_KompFkt_Interlock" 6 "Keine_Komponentenfunktion" 7 "Init";
VAL_ 1485 DC_Peakstrom_verfuegbar 0 "Peakstrom_nicht_verfuegbar" 1 "Peakstrom_verfuegbar";
VAL_ 1485 DC_Abregelung_Temperatur 0 "keine_Abregelung" 1 "Abregelung_Temp";
VAL_ 1485 DC_IstModus_02 0 "Standby" 1 "HV_On_Vorladen" 2 "Tiefsetzen" 3 "Hochsetzen" 4 "Pruefimpuls_12V" 5 "Fehler" 7 "Initialisierung";
VAL_ 1485 DC_HV_EKK_IstModus 0 "Standby" 2 "Tiefsetzen" 3 "Hochsetzen" 5 "Fehler" 7 "Initialisierung";
VAL_ 1485 DC_Status_Spgfreiheit_HV 0 "Init" 1 "HV_Komponente_spannungsfrei" 2 "HV_Komp_nicht_spannungsfrei" 3 "Fehler_nicht_spannungsfrei";
VAL_ 1485 DC_IstSpannung_EKK_HV 255 "Init";
VAL_ 1485 DC_Temperatur 254 "Init" 255 "Fehler";
VAL_ 1505 BCM1_Aussen_Temp_ungef 253 "nicht_verbaut" 254 "Initwert" 255 "Fehler";
VAL_ 1505 BCM_Heizungsabsperrventil_Status 0 "HAV_offen" 1 "HAV_geschlossen" 2 "Init_oder_nicht_verbaut" 3 "Fehler";
VAL_ 1505 BCM_Heizungspumpe_Status 0 "Pumpe_aus" 1 "Pumpe_aktiv_und_Stauts_iO" 2 "Init_oder_nicht_verbaut" 3 "Fehler";
VAL_ 1505 BCM_Kompressorkupplung_Status 0 "Kupplung_offen" 1 "Kupplung_geschlossen" 2 "Init_oder_nicht_verbaut" 3 "Fehler";
VAL_ 1505 BCM1_PTC_stufig_Status 0 "Aus" 1 "Stufe_1_aktiv" 2 "Stufe_2_aktiv" 3 "Stufe_3_aktiv" 6 "Init_oder_nicht_verbaut" 7 "Fehler";
VAL_ 1505 BCM1_FStatus_Aussentemp_ungef 0 "iO" 1 "niO";
VAL_ 1505 BCM1_Kompressorstrom_ist 254 "Init, bzw. nicht verfuegbar" 255 "Fehler";
VAL_ 1505 BCM1_OBD_FStatus_ATemp 0 "kein Fehler / Init / nicht verbaut" 1 "KS- (Kurzschluss nach minus)" 2 "KS+ (Kurzschluss nach plus)" 3 "OC (Leitungsunterbrechung)" 4 "OOR+ (Signal zu gross)" 5 "OOR- (Signal zu klein)" 6 "RC+ (Signal unplausibel zu gross)" 7 "RC- (Signal unplausibel zu klein)" 8 "KS+/OC (Kurzschluss nach plus/Unterbrechung)" 9 "KS-/OC  (Kurzschluss nach minus/Unterbrechung)" 10 "KS-/KS+  (Kurzschluss nach minus/plus)" 11 "KS-/KS+/OC (Kurzschluss nach minus/plus/Unterbrechung)" 12 "frei" 13 "frei" 14 "frei" 15 "frei";
VAL_ 1513 DS_Kaeltemittel_P 2046 "Init" 2047 "Fehler";
VAL_ 1513 DS_Status 0 "i.O." 1 "t.b.d." 2 "Init bzw. nicht verfügbar" 3 "Fehler";
VAL_ 1513 ION_Status 0 "Hochspannung_ausgeschaltet" 1 "Hochspannung_eingeschaltet" 2 "Init";
VAL_ 1513 ION_Status_LED 0 "Aus" 1 "Ein";
VAL_ 1513 AAU_Geblaese 126 "Init" 127 "Fehler";
VAL_ 1513 ION_Status_Taster 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1520 DI_KL_58xd 254 "Init" 255 "Fehler";
VAL_ 1520 DI_KL_58xs 126 "Init" 127 "Fehler";
VAL_ 1520 DI_KL_58xt 126 "Init" 127 "Fehler";
VAL_ 1524 IL_Bel_FS_Ausstieg 0 "inaktiv" 1 "aktiv";
VAL_ 1524 IL_Bel_BFS_Ausstieg 0 "inaktiv" 1 "aktiv";
VAL_ 1524 IL_Bel_HFS_Ausstieg 0 "inaktiv" 1 "aktiv";
VAL_ 1524 IL_Bel_HBFS_Ausstieg 0 "inaktiv" 1 "aktiv";
VAL_ 1524 BCM1_Innenlicht_gedimmt_V 0 "100% Innenlicht" 1 "gedimmtes Innenlicht";
VAL_ 1524 BCM1_Innenlicht_gedimmt_H 0 "100% Innenlicht" 1 "gedimmtes Innenlicht";
VAL_ 1524 IL_Innenlicht_aktiv 0 "Innenlicht inaktiv" 1 "Innenlicht aktiv";
VAL_ 1524 IL_Klemme_30G_aktiv 0 "Kl_30G_abgeschaltet" 1 "eingeschaltet";
VAL_ 1524 BCM1_DI_Rampe_Innenlicht 0 "Dimmrampe 0" 1 "Dimmrampe 1";
VAL_ 1524 BCM1_DI_Rampe_Leselicht 0 "Dimmrampe 0" 1 "Dimmrampe 1";
VAL_ 1524 BCM1_Innenlicht_H 0 "Aus" 1 "Ein";
VAL_ 1524 BCM1_Innenlicht_V 0 "Aus" 1 "Ein";
VAL_ 1524 BCM1_Leselicht_Anf_hl 0 "Aus" 1 "Ein";
VAL_ 1524 BCM1_Leselicht_Anf_hr 0 "Aus" 1 "Ein";
VAL_ 1524 BCM1_Leselicht_Anf_vl 0 "Aus" 1 "Ein";
VAL_ 1524 BCM1_Leselicht_Anf_vr 0 "Aus" 1 "Ein";
VAL_ 1524 BCM1_Leuchten_Aus 0 "keine Aenderung" 1 "Aus";
VAL_ 1524 AMB_Charisma_FahrPr 0 "keine_Funktion" 1 "Programm_1" 2 "Programm_2" 3 "Programm_3" 4 "Programm_4" 5 "Programm_5" 6 "Programm_6" 7 "Programm_7" 8 "Programm_8" 9 "Programm_9" 10 "Programm_10" 11 "Programm_11" 12 "Programm_12" 13 "Programm_13" 14 "Programm_14" 15 "Programm_15";
VAL_ 1524 AMB_Charisma_Status 0 "Init" 1 "verfuegbar" 2 "nicht_verfuegbar" 3 "asynchron_durch_Fahrerwunsch";
VAL_ 1600 MO_QBit_Ansaugluft_Temp 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 1600 MO_QBit_Oel_Temp 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 1600 MO_QBit_Kuehlmittel_Temp 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 1600 MO_HYB_Fehler_HV_Netz 0 "i_O" 1 "kein_generatorischer_Betrieb_moeglich";
VAL_ 1600 MO_aktives_Getriebeheizen 0 "kein_Ventil_ansteuern" 1 "Ventil_muss_angesteuert_werden";
VAL_ 1600 MO_Absperrventil_oeffnen 0 "nicht_verfuegbar" 1 "Ansteuerung_Ventil_zulaessig" 2 "Ventil_oeffnen_oder_geoeffnet" 3 "Ventil_schliessen_oder_geschlossen";
VAL_ 1600 MO_Ansaugluft_Temp 254 "Init" 255 "Fehler";
VAL_ 1600 MO_Oel_Temp 253 "nicht_verbaut" 254 "Init" 255 "Fehler";
VAL_ 1600 MO_Kuehlmittel_Temp 254 "Init" 255 "Fehler";
VAL_ 1600 MO_Hoeheninfo 255 "Fehler";
VAL_ 1600 MO_Kennfeldk 0 "keine Kennfeldkühlung vorhanden" 1 "Kennfeldkühlung vorhanden";
VAL_ 1600 MO_Getriebe_kuehlen 0 "nicht_kuehlen" 1 "kuehlen";
VAL_ 1600 MO_Heizungspumpenansteuerung 13 "Fehler" 14 "Init" 15 "nicht_verbaut";
VAL_ 1600 MO_SpannungsAnf 0 "keine_Anforderung" 1 "Anforderung";
VAL_ 1601 MO_Faktor_Momente_02 0 "nicht_belegt";
VAL_ 1601 MO_Hybridfahrzeug 0 "kein_Hybridfahrzeug" 1 "Mild_Hybrid" 2 "Full_Hybrid" 3 "PlugIn_Hybrid";
VAL_ 1601 MO_Getriebe_Code 0 "Init" 2 "DL501" 3 "DL800" 4 "VL381" 5 "DL382_Front" 6 "DL382_Allrad" 7 "DL702" 8 "AL552_Allrad" 9 "AL552_Front" 10 "AL651" 11 "AL551__AL951__AL1000_8A" 12 "PDK_PAG" 13 "AL551_Hybrid" 15 "Handschalter_konventionell" 16 "AQ250_160" 17 "AQ450" 18 "DQ200" 19 "DQ250" 20 "DQ500" 21 "SQ100" 22 "SQ200" 23 "DQ400E" 24 "DQ381" 25 "AL550" 26 "AQ300" 31 "DL800E" 32 "Single_Gear__ohne_GSG_WH_am_MSG" 33 "EQ550_2P";
VAL_ 1601 MO_StartStopp_Codiert 0 "Start_Stopp_nicht_verbaut" 1 "Start_Stopp_verbaut";
VAL_ 1601 MO_Anzahl_Zyl 0 "kein_Zylinder" 1 "1_Zylinder" 2 "2_Zylinder" 3 "3_Zylinder" 4 "4_Zylinder" 5 "5_Zylinder" 6 "6_Zylinder" 7 "7_Zylinder" 8 "8_Zylinder" 9 "9_Zylinder" 10 "10_Zylinder" 11 "11_Zylinder" 12 "12_Zylinder" 13 "15_Zylinder" 14 "16_Zylinder" 15 "18_Zylinder";
VAL_ 1601 MO_Kraftstoffart 0 "Diesel" 1 "Benzin_inkl_E25_E85" 2 "CNG" 3 "LPG" 4 "Wasserstoff" 5 "E100_Ethanol" 15 "kein_Verbrennungskraftstoff";
VAL_ 1601 MO_Ansaugsystem 0 "Turbo" 1 "Sauger";
VAL_ 1601 MO_DPF_verbaut 0 "kein_DPF_verbaut" 1 "DPF_verbaut";
VAL_ 1601 TSK_Codierung 0 "kein_Fahrgeschwindigkeitsregler" 1 "GRA" 2 "GRA_Plus" 3 "Basis_ACC" 4 "ACC_Follow_to_Stop" 5 "ACC_Stop_and_Go" 7 "Codierung_in_Plausibilisierungsphase";
VAL_ 1603 KBI_Einheit_Datum 0 "Tag/Monat/Jahr" 1 "Monat/Tag/Jahr" 2 "Jahr/Monat/Tag" 3 "reserviert";
VAL_ 1603 KBI_Einheit_Druck 0 "Druckangabe in bar" 1 "Druckangabe in psi" 2 "Druckangabe in kPa" 3 "tbd.";
VAL_ 1603 KBI_Einheit_Streckenanz 0 "Kilometer" 1 "Meilen";
VAL_ 1603 KBI_MFA_v_Einheit_02 0 "kmh" 1 "mph";
VAL_ 1603 KBI_Einheit_Temp 0 "Grad Celsius °C" 1 "Grad Fahrenheit °F";
VAL_ 1603 KBI_Einheit_Uhrzeit 0 "24h" 1 "12h AM/PM";
VAL_ 1603 KBI_Einheit_Verbrauch 0 "mpg UK" 1 "mpg USA" 2 "Liter/100km" 3 "km/Liter";
VAL_ 1603 KBI_Einheit_Volumen 0 "Liter" 1 "Gallonen UK" 2 "Gallonen USA" 3 "reserviert";
VAL_ 1603 KBI_Einheit_Verbrauch_elektr 0 "kWh_pro_100km" 1 "km_pro_kWh" 2 "kWh_pro_100mls" 3 "mls_pro_kWh" 4 "MPGe";
VAL_ 1603 KBI_Einheit_Verbrauch_Gas 0 "kg_per_100km" 1 "km_per_kg" 2 "m3_per_100km" 3 "km_per_m3" 4 "miles_per_lbs" 5 "miles_per_yard3" 6 "miles_per_kg" 7 "miles_per_m3" 8 "miles_per_gallon_equivalent_US";
VAL_ 1603 KBI_Einheit_Masse 0 "kg" 1 "lbs" 2 "reserviert";
VAL_ 1622 ELV_Anf_Klemme_S 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Anf_Klemme_15 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Anf_Klemme_50 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_01_Sendestatus 0 "ELV_sendet_mit_1000ms" 1 "ELV_sendet_mit_50ms";
VAL_ 1622 ELV_Verriegelt 0 "nicht_verriegelt" 1 "verriegelt";
VAL_ 1622 ELV_Entriegelt 0 "ELV_nicht_entriegelt" 1 "ELV_entriegelt";
VAL_ 1622 ELV_ZAT_betaetigt 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1622 ELV_Lebenszustand 0 "Lebenszustand_0" 1 "Lebenszustand_4";
VAL_ 1622 ELV_Anlernmodus 0 "Normalbetrieb" 1 "Anlernmodus";
VAL_ 1622 ELV_Klemmenfreigabe 0 "keine_Freigabe" 1 "Freigabe";
VAL_ 1622 ELV_Abbruch_Anf_Klemmenfreigabe 0 "kein_Abbruch" 1 "Abbruch";
VAL_ 1622 ELV_LED_Rot 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_LED_Gelb 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Txt_Panikabschaltung 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Txt_Lkg_Bewegen 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Txt_Werkstatt 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Txt_Defekt 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Txt_P_Gang 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Txt_PN_Gang 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Txt_Kupplung 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_Txt_Bremse 0 "Aus" 1 "Ein";
VAL_ 1622 ELV_P_verriegelt 0 "nicht_verriegelt" 1 "verriegelt";
VAL_ 1624 LV_Standlicht_Anzeige 0 "nicht aktiv" 1 "aktiv";
VAL_ 1624 LV_Abblendlicht_Anzeige 0 "inaktiv" 1 "aktiv";
VAL_ 1624 LV_Fernlicht_Anzeige 0 "nicht aktiv" 1 "aktiv";
VAL_ 1624 LV_Nebellicht_Anzeige 0 "nicht aktiv" 1 "aktiv";
VAL_ 1624 LV_Nebelschlusslicht_Anzeige 0 "nicht aktiv" 1 "aktiv";
VAL_ 1624 LV_Tagfahrlicht_Anzeige 0 "nicht aktiv" 1 "aktiv";
VAL_ 1624 LV_AFL_aktiv_Anzeige 0 "nicht aktiv" 1 "aktiv";
VAL_ 1624 LV_AFL_defekt 0 "OK" 1 "defekt";
VAL_ 1624 LV_Blinker_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Standlicht_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Abblendlicht_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Fernlicht_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Nebellicht_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Blk_li_Seite_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Tagfahrlicht_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_FLA_aktiv_Anzeige 0 "aus" 1 "ein";
VAL_ 1624 LV_FLA_defekt 0 "i.O." 1 "FLA defekt";
VAL_ 1624 LV_FLA_Sensor_blockiert 0 "i.O." 1 "FLA-Sensor blockiert";
VAL_ 1624 LV_Blinker_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Standlicht_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Abblendlicht_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Fernlicht_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Nebellicht_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Blk_re_Seite_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Tagfahrlicht_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Aussenlicht_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Abblendlicht_TFL_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Nebellicht_TFL_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Standlicht_TFL_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Abblend_Fernlicht_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Abblendlicht_TFL_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Nebellicht_TFL_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Standlicht_TFL_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Abblend_Fernlicht_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Abbiegelicht_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Abbiegelicht_re_def 0 "OK" 1 "defekt";
VAL_ 1624 BCM1_Linksverkehr 0 "Rechtsverkehr" 1 "Linksverkehr";
VAL_ 1624 BCM1_Licht_Dunkelheit_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 1624 LV_LED_Scheinwerfer_li_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_LED_Scheinwerfer_re_def 0 "OK" 1 "defekt";
VAL_ 1624 LV_Blinker_VL_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 1624 LV_Blinker_VR_aktiv 0 "inaktiv" 1 "aktiv";
VAL_ 1624 LV_MXB_Status_Anzeige 0 "MXB_aus" 1 "MXB_regelt" 2 "MXB_volles_Fernlicht" 3 "MXB_und_Laser_aktiv";
VAL_ 1629 BR_Systemart 0 "ABS" 1 "ABS_ASR" 2 "ESP" 3 "ESP_mit_integrierter_EPB";
VAL_ 1629 ESP_SpannungsAnf_02 0 "keine_Anforderung" 1 "Anforderung_Stufe1" 2 "Anforderung_Stufe2" 3 "Anforderung_Stufe3";
VAL_ 1629 ESP_Charisma_FahrPr 0 "keine_Funktion" 1 "Programm_1" 2 "Programm_2" 3 "Programm_3" 4 "Programm_4" 5 "Programm_5" 6 "Programm_6" 7 "Programm_7" 8 "Programm_8" 9 "Programm_9" 10 "Programm_10" 11 "Programm_11" 12 "Programm_12" 13 "Programm_13" 14 "Programm_14" 15 "Programm_15";
VAL_ 1629 ESP_Charisma_Status 0 "Init" 1 "verfuegbar" 2 "nicht_verfuegbar" 3 "asynchron_durch_Fahrerwunsch";
VAL_ 1629 ESP_Wiederstart_Anz_01 0 "keine_Anzeige" 1 "Rueckwaertsrollen";
VAL_ 1629 ESP_Wiederstart_Anz_02 0 "keine_Anzeige" 1 "Autohold_Rutschen";
VAL_ 1629 ESP_Wiederstart_Anz_03 0 "keine_Anzeige" 1 "Offroad_HDC";
VAL_ 1629 ESP_Wiederstart_Anz_04 0 "keine_Anzeige" 1 "ESC_Off";
VAL_ 1629 ESP_Stoppverbot_Anz_01 0 "keine_Anzeige" 1 "Notbremsung_aktiv";
VAL_ 1629 ESP_Stoppverbot_Anz_02 0 "keine_Anzeige" 1 "Autohold_AVH_Rutschen";
VAL_ 1629 ESP_Stoppverbot_Anz_03 0 "keine_Anzeige" 1 "Rueckwaertsrollen";
VAL_ 1629 ESP_Stoppverbot_Anz_04 0 "keine_Anzeige" 1 "ESP_Pumpenlauf";
VAL_ 1629 ESP_Stoppverbot_Anz_05 0 "keine_Anzeige" 1 "ESP_OFF";
VAL_ 1629 ESP_Stoppverbot_Anz_06 0 "keine_Anzeige" 1 "Offroad_HDC";
VAL_ 1629 ESP_Stoppverbot_Anz_07 0 "keine_Anzeige" 1 "ESP_Haltefunktion_nicht_verfuegbar";
VAL_ 1629 ESP_Stoppverbot_Anz_Std 0 "keine_Anzeige" 1 "Standard_Stoppvetogrund";
VAL_ 1629 ESP_Dachrelingsensor 0 "Sensor_nicht_verbaut" 1 "Quertraeger_montiert" 2 "Quertraeger_nicht_montiert" 3 "Fehler";
VAL_ 1629 ESP_Stoppverbot_Anz_08 0 "keine_Anzeige" 1 "Neigungsbegrenzung";
VAL_ 1629 HDC_Charisma_FahrPr 0 "keine_Funktion" 1 "Programm_1" 2 "Programm_2" 3 "Programm_3" 4 "Programm_4" 5 "Programm_5" 6 "Programm_6" 7 "Programm_7" 8 "Programm_8" 9 "Programm_9" 10 "Programm_10" 11 "Programm_11" 12 "Programm_12" 13 "Programm_13" 14 "Programm_14" 15 "Programm_15";
VAL_ 1629 HDC_Charisma_Status 0 "Init" 1 "verfuegbar" 2 "nicht_verfuegbar" 3 "asynchron_durch_Fahrerwunsch";
VAL_ 1629 BR_QBit_Reifenumfang 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 1631 TSK_QBit_Steigung 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 1631 TSK_QBit_Fahrzeugmasse 0 "gueltiger_Wert" 1 "Ersatz_Init_oder_Fehlerwert";
VAL_ 1631 MO_SpannungsAnf_02 0 "keine_Anforderung" 1 "Anforderung_Stufe_1" 2 "Anforderung_Stufe_2" 3 "Anforderung_Stufe_3";
VAL_ 1631 MO_DPF_reg 0 "DPF_regeneriert_nicht" 1 "DPF_regeneriert";
VAL_ 1631 MO_Heizstrom_EKAT 127 "Fehler";
VAL_ 1631 MO_Heizstrom_SCR 63 "Fehler";
VAL_ 1631 MO_Anzeige_Kaltleuchte 0 "Lampe_aus" 1 "Lampe_an";
VAL_ 1631 MO_P_Generator_ungefiltert_Anf 0 "nicht_angefordert" 1 "angefordert";
VAL_ 1631 TSK_Getriebeinfo 0 "Handschalter" 1 "AL_AQ_Getriebe" 2 "DL_DQ_Getriebe" 3 "CVT_Getriebe";
VAL_ 1631 MO_Energieinhalt_BMS 4094 "Init" 4095 "Fehler";
VAL_ 1631 TSK_Fahrzeugmasse_02 255 "Fehler";
VAL_ 1631 TSK_Steigung_02 0 "Init_oder_nicht_verbaut" 255 "Fehler";
VAL_ 1640 KL_LRH_Taster 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1640 KL_LRH_Stufe 0 "Stufe_niedrig" 1 "Stufe_mittel" 2 "Stufe_hoch" 3 "AUS";
VAL_ 1640 HSH_Taster 0 "nicht_betaetigt" 1 "short_push" 2 "long_push" 3 "tbd";
VAL_ 1640 FSH_Taster 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1640 KL_Zuheizer_Freigabe 0 "keine_Freigabe_Zuheizer" 1 "Zuheizerfreigabe";
VAL_ 1640 KL_Beschlagsgefahr 0 "keine_Beschlagsgefahr" 1 "Beschlagsgefahr";
VAL_ 1640 KRH_Soll_li 0 "aus" 1 "Stufe_1" 2 "Stufe_2" 3 "Stufe_3";
VAL_ 1640 KRH_Soll_re 0 "aus" 1 "Stufe_1" 2 "Stufe_2" 3 "Stufe_3";
VAL_ 1640 KL_Geblspng_Soll 0 "0V_Motorspannung" 252 "0V_sofort_Abschalten" 253 "0V_senken_mit_Rampe" 254 "reserviert" 255 "Fehler";
VAL_ 1640 KL_Geblspng_Fond_Soll 0 "0V_Motorspannung" 252 "0V_sofort_Abschalten" 253 "0V_senken_mit_Rampe" 254 "reserviert" 255 "Fehler";
VAL_ 1640 KL_I_Geblaese 255 "Fehler";
VAL_ 1640 KL_Kompressorstrom_soll 1022 "Init" 1023 "Fehler";
VAL_ 1640 KL_Umluftklappe_Status 0 "Frischluft" 1 "Teilumluft_10" 2 "Teilumluft_20" 3 "Teilumluft_30" 4 "Teilumluft_40" 5 "Teilumluft_50" 6 "Teilumluft_60" 7 "Teilumluft_70" 8 "Teilumluft_80" 9 "Teilumluft_90" 10 "Umluft" 14 "Init" 15 "Fehler";
VAL_ 1640 KL_PTC_Verbauinfo 0 "kein_PTC" 1 "600W_geschaltet" 2 "1000W_geschaltet" 3 "1400W_LIN";
VAL_ 1648 MO_ANC_Kennfeld_Anf 0 "Kennfeld_1" 1 "Uebergang_Kennfeld_2_nach_1" 2 "Uebergang_Kennfeld_1_nach_2" 3 "Kennfeld_2";
VAL_ 1648 MO_Bremslicht_Reku 0 "Aus" 1 "Ein";
VAL_ 1648 MO_StartStopp_PopUp 0 "Statuswechsel_ohne_Taster (Init)" 1 "StSt_per_Taster_deaktiviert" 2 "StSt_per_Taster_aktiviert" 3 "nicht verwendet";
VAL_ 1648 MO1_Sperr_Info_WFS 0 "nicht_gesperrt" 1 "gesperrt";
VAL_ 1648 MO1_Freigabe_Info_WFS 0 "ungueltig" 1 "gueltig";
VAL_ 1648 MO_EPCL 0 "EPCL_aus_kein_Text" 1 "EPCL_gelb_Stoerung" 2 "EPCL_gelb_Leistungsbeschraenkung" 3 "EPCL_rot_Fzg_sicher_abstellen";
VAL_ 1648 MO_Zylabsch_Texte_02 0 "ein_ZAS_generell_keine_ZAS_Anzeigen" 1 "ZAS_im_VMB" 2 "ZAS_im_ASB_1_Zyl_aktiv" 3 "ZAS_im_ASB_2_Zyl_aktiv" 4 "ZAS_im_ASB_3_Zyl_aktiv" 5 "ZAS_im_ASB_4_Zyl_aktiv" 6 "ZAS_im_ASB_5_Zyl_aktiv" 7 "ZAS_im_ASB_6_Zyl_aktiv" 8 "ZAS_im_ASB_8_Zyl_aktiv" 9 "ZAS_im_ASB_12_Zyl_aktiv" 13 "Uebergangsbetrieb" 14 "keine_ZAS_Anzeige_ggf_Stopp_Schub_Betrieb" 15 "ZAS_Stoerungs_Anzeige";
VAL_ 1648 MO_Fahrzeugtyp 0 "Verbrenner_Fahrzeug" 1 "Hybrid" 2 "E_Fahrzeug" 3 "E_Fahrzeug_mit_Range_Extender" 4 "Brennstoffzellenfahrzeug" 5 "tbd" 6 "tbd" 7 "tbd";
VAL_ 1648 MO_Abstellzeit 254 "Init" 255 "reserviert";
VAL_ 1648 MO_Abstellzeit_Status 0 "Abstellzeit_nicht_berechnet" 1 "Abstellzeit_berechnet" 2 "Abstellzeit_Minimalwert" 3 "Abstellzeit_ungueltig";
VAL_ 1648 MO1_Freigabe_Verfallsinfo_WFS 0 "verfaellt_nicht" 1 "verfaellt";
VAL_ 1648 MO_Hybrid_StartStopp_LED 0 "LED_aus_und_Blinken_aus" 1 "LED_ein_und_Blinken_aus" 2 "LED_ein_und_Blinken_ein_50_percent" 3 "reserviert";
VAL_ 1648 MO_Fehler_Zylabsch 0 "kein_Fehler" 1 "Notlauf_Vollmotorbetrieb" 2 "Notlauf_mindestens_einem_abgesch_Zylinder";
VAL_ 1648 MO_Anzahl_Abgesch_Zyl 0 "Vollmotorbetrieb" 1 "1_Zylinder_abgeschaltet" 2 "2_Zylinder_abgeschaltet" 3 "3_Zylinder_abgeschaltet" 4 "4_Zylinder_abgeschaltet" 5 "5_Zylinder_abgeschaltet" 6 "6_Zylinder_abgeschaltet" 7 "8_Zylinder_abgeschaltet";
VAL_ 1648 MO_Zylabsch_Texte 0 "keine_Texte" 1 "Zylinderabschaltung_deaktivieren" 2 "Zylinderabschaltung_aktivieren" 3 "Zylinderabschaltung_unruhig";
VAL_ 1648 MO_Ethanol_BS_Texte 0 "kein_Text" 1 "E85_Warm_Up" 2 "FlexFuel_Warnung_Stufe_3" 3 "Warmfahrempfehlung_MQB__FlexFuel_Warnung_Stufe_1" 4 "FlexFuel_Warnung_Stufe_2" 5 "FlexFuel_Warnung_Stufe_4" 6 "E25_Warm_Up";
VAL_ 1648 MO_Drehzahl_Warnung 0 "keinen_Warnhinweis_anzeigen" 1 "Warnhinweis_anzeigen";
VAL_ 1648 MO_obere_Drehzahlgrenze 0 "Init";
VAL_ 1710 SP_FT_oben 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1710 SP_FT_unten 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1710 SP_FT_links 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1710 SP_FT_rechts 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1710 SP_BT_oben 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1710 SP_BT_unten 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1710 SP_BT_links 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1710 SP_BT_rechts 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1710 SP_abklappen 0 "Spiegel_abklappen_nicht_aktiv" 1 "Spiegel_abklappen_aktiv";
VAL_ 1710 SP_anklappen 0 "Spiegle_anklappen_nicht_aktiv" 1 "Spiegel_anklappen_aktiv";
VAL_ 1710 SP_normieren 0 "Spiegel_nicht_normiert" 1 "Spiegel_normiert";
VAL_ 1710 SP_Hzg_Taster 0 "nicht_betaetigt" 1 "betaetigt";
VAL_ 1710 SP_S_oben 1 "Spiegelschalter in Stellung oben";
VAL_ 1710 SP_S_unten 1 "Spiegelschalter in Stellung unten";
VAL_ 1710 SP_S_links 1 "Spiegelschalter in Stellung links";
VAL_ 1710 SP_S_rechts 1 "Spiegelschalter in Stellung rechts";
VAL_ 1710 SP_Auswahl_li 1 "Spiegelschalter in Stellung Auswahl links";
VAL_ 1710 SP_Auswahl_re 1 "Spiegelschalter in Stellung Auswahl rechts";
VAL_ 1710 SP_ARA_Status 0 "TSG nicht im ARA-Modus" 1 "TSG im ARA-Modus";
VAL_ 1710 SP_S_Klappen 1 "Spiegelschalter in Stellung Klappen";
VAL_ 1710 SP_Verstellschalter_Fehler 0 "kein_Fehler" 1 "Fehler";
VAL_ 1711 RV_Video_on 0 "Kamerabild wird nicht angezeigt" 1 "Kamerabild wird angezeigt";
VAL_ 1711 RV_Dark_Screen 0 "Kamerabild nicht verdunkeln" 1 "Kamerabild verdunkeln";
VAL_ 1711 RV_HMI_Mode 0 "Parkluecke  (Modus 1)" 1 "Parallel zur Straߥ (Modus 2)" 2 "reserviert" 3 "Kalibrierung (nur Diagnose)";
VAL_ 1711 RV_GL_side 0 "Guidelines_abgeschaltet" 1 "Guidelines_Anzeige_rechte_Seite" 2 "Guiedelines_Anzeige_linke_Seite" 3 "Guiedelines_Anzeige_beide_Seiten";
VAL_ 1711 RV_System_aktiv 0 "System nicht betriebsbereit" 1 "System betriebsbereit";
VAL_ 1711 RV_Reinigung_Anf 0 "nicht_reinigen" 1 "reinigen";
VAL_ 1711 RV_Settings_enabled 0 "Menu 'Einstellungen' nicht aktiviert" 1 "Menu 'Einstellungen' aktiviert";
VAL_ 1711 RV_Menu_Item 0 "HMI Mode als ausgewaehlt markiert" 1 "Helligkeit als ausgewaehlt markiert" 2 "Kontrast als ausgewaehlt markiert" 3 "Farbsaettigung als ausgewaehlt markiert";
VAL_ 1711 SV_Video_on 0 "SideView_aus_/_nicht_verbaut" 1 "SideView_an";
VAL_ 1711 RV_Calib_Fehler 0 "Kalibrierung_i.O." 1 "RearView_nicht_kalibriert";
VAL_ 1711 RV_GL_Trailer_connect 0 "Guidelines_werden_angezeigt" 1 "Guidelines_wegen_angekoppeltem_Anh䮧er_deaktiviert";
VAL_ 1711 RV_GL_Trunk_open 0 "Guidelines_werden_angezeigt" 1 "Guidelines_wegen_geöffneter_Heckklappe_deaktiviert";
VAL_ 1711 RV_GL_LWS_Fehler 0 "Guidelines_werden_angezeigt" 1 "Guidelines_wegen_nicht_kalibriertem_Lenkwinkelsensor_deaktiviert";
VAL_ 1711 ZFAS_Umfeldbeleuchtung_Anf 0 "nicht_angefordert" 1 "angefordert";
VAL_ 1714 DGN_Verlernzaehler 255 "ungültiger Z䨬erstand oder kein gültiger Fahrzyklus";
VAL_ 1714 UH_Monat 0 "Init" 14 "Relatives_Datum" 15 "Fehler";
VAL_ 1714 UH_Tag 0 "Init";
VAL_ 1714 Kombi_02_alt 0 "aktuell" 1 "veraltet";
VAL_ 1714 Uhrzeit_01_alt 0 "aktuell" 1 "veraltet";
