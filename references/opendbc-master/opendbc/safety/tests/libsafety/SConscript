import platform

CC = 'gcc'
system = platform.system()
mac_ver = platform.mac_ver()

# gcc installed by homebrew has version suffix (e.g. gcc-12) in order to be
# distinguishable from system one - which acts as a symlink to clang
# clang works on macOS 15 and greater but has issues on earlier macOS versions.
# see: https://github.com/commaai/openpilot/issues/35093
if system == 'Darwin' and mac_ver[0] and mac_ver[0] < '15':
  CC += '-13'

env = Environment(
  CC=CC,
  CFLAGS=[
    '-Wall',
    "-Wextra",
    '-Werror',
    '-nostdlib',
    '-fno-builtin',
    '-std=gnu11',
    '-Wfatal-errors',
    '-Wno-pointer-to-int-cast',
    '-DCANFD',
    # GCC coverage flags
    '-fprofile-arcs',
    '-ftest-coverage',
  ],
  CPPPATH=["#", "../../board/"],
  LIBS=["gcov"],
)
if system == "Darwin":
  env.PrependENVPath('PATH', '/opt/homebrew/bin')

if GetOption('mutation'):
  env['CC'] = 'clang-17'
  flags = [
    '-fprofile-instr-generate',
    '-fcoverage-mapping',
    '-fpass-plugin=/usr/lib/mull-ir-frontend-17',
    '-g',
    '-grecord-command-line',
  ]
  env['CFLAGS'] += flags
  env['LINKFLAGS'] += flags

if GetOption('ubsan'):
  flags = [
    "-fsanitize=undefined",
    "-fno-sanitize-recover=undefined",
  ]
  env['CFLAGS'] += flags
  env['LINKFLAGS'] += flags

safety = env.SharedObject("safety.os", "safety.c")
libsafety = env.SharedLibrary("libsafety.so", [safety])

# GCC note file is generated by compiler, allow scons to clean it up
env.SideEffect("safety.gcno", safety)
