# Core FastAPI and web framework
fastapi
uvicorn[standard]
pydantic
pydantic-settings

# OBD2 and CAN communication
obd
python-can
cantools

# AI and LLM integration
openai
google-generativeai

# Database and data management
sqlalchemy
alembic

# Serial communication for OBD adapters
pyserial
pyserial-asyncio

# HTTP client for external APIs
httpx
aiohttp

# Data validation and parsing
jsonschema

# Logging and monitoring
loguru

# Testing
pytest
pytest-asyncio

# Configuration management
python-dotenv
pyyaml

# Async support
aiofiles
