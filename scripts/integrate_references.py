#!/usr/bin/env python3
"""
Reference Integration Script

Integrates knowledge from all reference projects and CAN dump data
into the main diagnostic database.

Source Attribution:
- Integration patterns based on automotive diagnostic standards
- Data processing inspired by open-source automotive tools
- Database migration following SQLAlchemy best practices

Usage:
    python scripts/integrate_references.py [--can-dump-file FILE] [--dry-run]
"""

import sys
import argparse
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.can_dump_integration.can_parser import CANDumpParser
from app.can_dump_integration.mock_obd_connection import MockOBDConnection
from app.reference_integration.opendbc_extractor import OpenDBCExtractor
from app.reference_integration.reference_extractor import ReferenceExtractor
from app.database.migration_manager import migration_manager
from app.database.database import db_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_argument_parser():
    """Setup command line argument parser"""
    parser = argparse.ArgumentParser(
        description='Integrate reference projects and CAN dump data'
    )
    
    parser.add_argument(
        '--can-dump-file',
        type=str,
        default='can_dump_test/nissan_leaf_candump.log',
        help='Path to CAN dump file for testing'
    )
    
    parser.add_argument(
        '--references-path',
        type=str,
        default='references',
        help='Path to references directory'
    )
    
    parser.add_argument(
        '--opendbc-path',
        type=str,
        default='references/opendbc-master',
        help='Path to OpenDBC project'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Perform dry run without database changes'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    return parser


def analyze_can_dump(can_dump_file: str) -> dict:
    """Analyze CAN dump file and extract diagnostic data"""
    logger.info(f"Analyzing CAN dump file: {can_dump_file}")
    
    if not Path(can_dump_file).exists():
        logger.warning(f"CAN dump file not found: {can_dump_file}")
        return {}
    
    try:
        # Parse CAN dump
        parser = CANDumpParser()
        messages = parser.parse_candump_file(can_dump_file)
        
        if not messages:
            logger.warning("No CAN messages found in dump file")
            return {}
        
        # Extract OBD responses
        obd_responses = parser.extract_obd_responses(messages)
        
        # Get statistics
        stats = parser.get_statistics(messages)
        
        logger.info(f"CAN dump analysis complete:")
        logger.info(f"  - Total messages: {len(messages)}")
        logger.info(f"  - OBD responses: {len(obd_responses)}")
        logger.info(f"  - Unique CAN IDs: {stats.get('unique_can_ids', 0)}")
        logger.info(f"  - Time span: {stats.get('time_span', 0):.2f} seconds")
        
        return {
            'messages': messages,
            'obd_responses': obd_responses,
            'statistics': stats,
            'success': True
        }
        
    except Exception as e:
        logger.error(f"Error analyzing CAN dump: {e}")
        return {'success': False, 'error': str(e)}


def extract_opendbc_knowledge(opendbc_path: str) -> dict:
    """Extract diagnostic knowledge from OpenDBC project"""
    logger.info(f"Extracting knowledge from OpenDBC: {opendbc_path}")
    
    if not Path(opendbc_path).exists():
        logger.warning(f"OpenDBC path not found: {opendbc_path}")
        return {}
    
    try:
        extractor = OpenDBCExtractor(opendbc_path)
        extracted_data = extractor.extract_all_brands()
        
        if not extracted_data:
            logger.warning("No data extracted from OpenDBC")
            return {}
        
        summary = extractor.get_extraction_summary(extracted_data)
        
        logger.info(f"OpenDBC extraction complete:")
        logger.info(f"  - Brands extracted: {summary['brands_extracted']}")
        logger.info(f"  - Total messages: {summary['total_messages']}")
        logger.info(f"  - Total PIDs: {summary['total_pids']}")
        logger.info(f"  - Total DTC mappings: {summary['total_dtc_mappings']}")
        
        return {
            'extracted_data': extracted_data,
            'summary': summary,
            'success': True
        }
        
    except Exception as e:
        logger.error(f"Error extracting OpenDBC knowledge: {e}")
        return {'success': False, 'error': str(e)}


def extract_reference_knowledge(references_path: str) -> dict:
    """Extract knowledge from all reference projects"""
    logger.info(f"Extracting knowledge from references: {references_path}")
    
    if not Path(references_path).exists():
        logger.warning(f"References path not found: {references_path}")
        return {}
    
    try:
        extractor = ReferenceExtractor(references_path)
        extracted_data = extractor.extract_all_references()
        
        if not extracted_data:
            logger.warning("No data extracted from references")
            return {}
        
        summary = extracted_data.get('summary', {})
        
        logger.info(f"Reference extraction complete:")
        logger.info(f"  - Total DTCs: {summary.get('total_dtcs', 0)}")
        logger.info(f"  - Total PIDs: {summary.get('total_pids', 0)}")
        logger.info(f"  - Total procedures: {summary.get('total_procedures', 0)}")
        logger.info(f"  - Brands covered: {', '.join(summary.get('brands_covered', []))}")
        
        return {
            'extracted_data': extracted_data,
            'summary': summary,
            'success': True
        }
        
    except Exception as e:
        logger.error(f"Error extracting reference knowledge: {e}")
        return {'success': False, 'error': str(e)}


def test_mock_obd_connection(can_dump_file: str) -> dict:
    """Test mock OBD connection with CAN dump data"""
    logger.info("Testing mock OBD connection")
    
    try:
        # Create mock connection
        connection = MockOBDConnection(can_dump_file)
        
        # Test connection
        if not connection.connect():
            logger.warning("Failed to establish mock OBD connection")
            return {'success': False, 'error': 'Connection failed'}
        
        # Test basic functionality
        vehicle_info = connection.get_vehicle_info()
        supported_pids = connection.get_supported_pids()
        dtcs = connection.get_dtcs()
        stats = connection.get_statistics()
        
        # Test some queries
        test_results = {}
        for pid in ['0C', '0D', '05', '10']:  # RPM, Speed, Coolant temp, MAF
            response = connection.query_command(pid)
            if response:
                test_results[pid] = response
        
        connection.disconnect()
        
        logger.info(f"Mock OBD connection test complete:")
        logger.info(f"  - Vehicle: {vehicle_info.get('make', 'Unknown')} {vehicle_info.get('model', 'Unknown')}")
        logger.info(f"  - Supported PIDs: {len(supported_pids)}")
        logger.info(f"  - DTCs found: {len(dtcs)}")
        logger.info(f"  - Test queries successful: {len(test_results)}")
        
        return {
            'vehicle_info': vehicle_info,
            'supported_pids': supported_pids,
            'dtcs': dtcs,
            'test_results': test_results,
            'statistics': stats,
            'success': True
        }
        
    except Exception as e:
        logger.error(f"Error testing mock OBD connection: {e}")
        return {'success': False, 'error': str(e)}


def migrate_to_database(extracted_data: dict, opendbc_data: dict, dry_run: bool = False) -> dict:
    """Migrate extracted data to database"""
    if dry_run:
        logger.info("DRY RUN: Skipping database migration")
        return {'success': True, 'dry_run': True}
    
    logger.info("Migrating extracted data to database")
    
    try:
        # Reset migration statistics
        migration_manager.reset_statistics()
        
        # Migrate reference data
        if extracted_data.get('success') and extracted_data.get('extracted_data'):
            ref_result = migration_manager.migrate_extracted_data(
                extracted_data['extracted_data']
            )
            logger.info(f"Reference migration result: {ref_result}")
        
        # Migrate OpenDBC data
        if opendbc_data.get('success') and opendbc_data.get('extracted_data'):
            dbc_result = migration_manager.migrate_opendbc_data(
                opendbc_data['extracted_data']
            )
            logger.info(f"OpenDBC migration result: {dbc_result}")
        
        # Verify migration
        verification = migration_manager.verify_migration()
        
        logger.info(f"Database migration complete:")
        logger.info(f"  - Total DTCs: {verification['total_dtcs']}")
        logger.info(f"  - PID definitions: {verification['pid_definitions']}")
        logger.info(f"  - Diagnostic procedures: {verification['diagnostic_procedures']}")
        logger.info(f"  - ECU addresses: {verification['ecu_addresses']}")
        
        return {
            'verification': verification,
            'migration_stats': migration_manager.get_migration_statistics(),
            'success': True
        }
        
    except Exception as e:
        logger.error(f"Error migrating to database: {e}")
        return {'success': False, 'error': str(e)}


def main():
    """Main integration function"""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("Starting reference integration process")
    logger.info(f"Configuration:")
    logger.info(f"  - CAN dump file: {args.can_dump_file}")
    logger.info(f"  - References path: {args.references_path}")
    logger.info(f"  - OpenDBC path: {args.opendbc_path}")
    logger.info(f"  - Dry run: {args.dry_run}")
    
    results = {}
    
    # Step 1: Analyze CAN dump
    logger.info("\n" + "="*50)
    logger.info("STEP 1: Analyzing CAN dump data")
    logger.info("="*50)
    results['can_analysis'] = analyze_can_dump(args.can_dump_file)
    
    # Step 2: Test mock OBD connection
    logger.info("\n" + "="*50)
    logger.info("STEP 2: Testing mock OBD connection")
    logger.info("="*50)
    results['mock_obd_test'] = test_mock_obd_connection(args.can_dump_file)
    
    # Step 3: Extract OpenDBC knowledge
    logger.info("\n" + "="*50)
    logger.info("STEP 3: Extracting OpenDBC knowledge")
    logger.info("="*50)
    results['opendbc_extraction'] = extract_opendbc_knowledge(args.opendbc_path)
    
    # Step 4: Extract reference knowledge
    logger.info("\n" + "="*50)
    logger.info("STEP 4: Extracting reference knowledge")
    logger.info("="*50)
    results['reference_extraction'] = extract_reference_knowledge(args.references_path)
    
    # Step 5: Migrate to database
    logger.info("\n" + "="*50)
    logger.info("STEP 5: Migrating to database")
    logger.info("="*50)
    results['database_migration'] = migrate_to_database(
        results['reference_extraction'],
        results['opendbc_extraction'],
        args.dry_run
    )
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("INTEGRATION SUMMARY")
    logger.info("="*50)
    
    success_count = sum(1 for result in results.values() if result.get('success', False))
    total_steps = len(results)
    
    logger.info(f"Completed {success_count}/{total_steps} steps successfully")
    
    for step_name, result in results.items():
        status = "✓ SUCCESS" if result.get('success', False) else "✗ FAILED"
        logger.info(f"  {step_name}: {status}")
        if not result.get('success', False) and 'error' in result:
            logger.info(f"    Error: {result['error']}")
    
    if success_count == total_steps:
        logger.info("\n🎉 All integration steps completed successfully!")
        return 0
    else:
        logger.warning(f"\n⚠️  {total_steps - success_count} steps failed. Check logs for details.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
