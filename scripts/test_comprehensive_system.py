#!/usr/bin/env python3
"""
Comprehensive System Test Script
Tests all new features: knowledge import, AI integration, live monitoring, safety
"""
import asyncio
import logging
import sys
import os
from pathlib import Path

# Add app directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.reference_integration.knowledge_extractor import knowledge_extractor
from app.reference_integration.database_migrator import database_migrator
from app.ai_engine.gemini_integration import gemini_integration
from app.live_data.enhanced_monitor import EnhancedLiveMonitor, SensorType
from app.obd_interface.connection_manager import connection_manager
from app.obd_interface.safety_manager import safety_manager
from app.database.database import DatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComprehensiveSystemTest:
    """Comprehensive test suite for all new features"""
    
    def __init__(self):
        self.test_results = {}
        self.failed_tests = []
    
    async def run_all_tests(self):
        """Run all test suites"""
        logger.info("Starting comprehensive system tests")
        
        test_suites = [
            ("Database Health Check", self.test_database_health),
            ("Knowledge Extraction", self.test_knowledge_extraction),
            ("Database Migration", self.test_database_migration),
            ("Google Gemini AI", self.test_gemini_integration),
            ("Connection Manager", self.test_connection_manager),
            ("Safety Manager", self.test_safety_manager),
            ("Live Data Monitor", self.test_live_monitor),
            ("Integration Test", self.test_full_integration)
        ]
        
        for test_name, test_func in test_suites:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                self.test_results[test_name] = result
                if result.get('success', False):
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
                    self.failed_tests.append(test_name)
            except Exception as e:
                logger.error(f"❌ {test_name} FAILED with exception: {e}")
                self.failed_tests.append(test_name)
                self.test_results[test_name] = {'success': False, 'error': str(e)}
        
        # Print summary
        self.print_test_summary()
    
    async def test_database_health(self):
        """Test database health and structure"""
        try:
            # Test database connection
            db_manager = DatabaseManager()
            with db_manager.get_session() as session:
                result = session.execute("SELECT COUNT(*) FROM dtc_codes").scalar()
                dtc_count = result if result else 0
            
            # Check table structure
            tables = ['dtc_codes', 'vehicle_profiles', 'diagnostic_sessions', 'brand_profiles']
            existing_tables = []

            with db_manager.get_session() as session:
                for table in tables:
                    try:
                        session.execute(f"SELECT 1 FROM {table} LIMIT 1")
                        existing_tables.append(table)
                    except:
                        pass
            
            return {
                'success': True,
                'dtc_count': dtc_count,
                'existing_tables': existing_tables,
                'database_accessible': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_knowledge_extraction(self):
        """Test knowledge extraction from references"""
        try:
            # Scan references folder
            diagnostic_files = await knowledge_extractor.scan_references_folder()
            
            # Test extraction from a few files
            extracted_knowledge = []
            for file_path in diagnostic_files[:5]:  # Test first 5 files
                knowledge = await knowledge_extractor.extract_knowledge_from_file(file_path)
                if knowledge:
                    extracted_knowledge.append(knowledge)
            
            return {
                'success': True,
                'files_found': len(diagnostic_files),
                'files_processed': len(extracted_knowledge),
                'sample_extraction': {
                    'dtc_codes': sum(len(k.dtc_codes) for k in extracted_knowledge),
                    'pid_definitions': sum(len(k.pid_definitions) for k in extracted_knowledge),
                    'can_messages': sum(len(k.can_messages) for k in extracted_knowledge)
                }
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_database_migration(self):
        """Test database migration functionality"""
        try:
            # Run a limited migration test
            stats = await database_migrator.migrate_all_references()
            
            return {
                'success': True,
                'migration_stats': stats
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_gemini_integration(self):
        """Test Google Gemini AI integration"""
        try:
            # Initialize Gemini
            initialized = await gemini_integration.initialize()
            
            if not initialized:
                return {
                    'success': False,
                    'error': 'Failed to initialize Gemini AI',
                    'api_key_configured': bool(os.getenv('GOOGLE_AI_API_KEY'))
                }
            
            # Test DTC analysis
            test_dtcs = ['P0171', 'P0300']
            test_vehicle = {
                'make': 'Toyota',
                'model': 'Prius',
                'year': 2015
            }
            
            response = await gemini_integration.analyze_dtc_codes(
                test_dtcs, test_vehicle
            )
            
            # Test live data explanation
            test_sensor_data = {
                'engine_rpm': {'value': 2500, 'unit': 'rpm'},
                'engine_load': {'value': 75, 'unit': '%'}
            }
            
            live_response = await gemini_integration.explain_live_data(
                test_sensor_data, test_vehicle
            )
            
            return {
                'success': True,
                'initialized': True,
                'dtc_analysis_length': len(response.content),
                'live_data_analysis_length': len(live_response.content),
                'model_used': response.model_used
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'api_key_configured': bool(os.getenv('GOOGLE_AI_API_KEY'))
            }
    
    async def test_connection_manager(self):
        """Test OBD connection manager"""
        try:
            # Test port listing
            available_ports = connection_manager.list_available_ports()
            
            # Test connection (will use mock if no real adapter)
            connected = await connection_manager.connect()
            
            # Test connection info
            conn_info = connection_manager.get_connection_info()
            
            # Test statistics
            stats = connection_manager.get_statistics()
            
            # Test health check
            health = await connection_manager.health_check()
            
            # Disconnect
            await connection_manager.disconnect()
            
            return {
                'success': True,
                'available_ports': len(available_ports),
                'connection_established': connected,
                'connection_method': conn_info.get('method'),
                'health_status': health.get('status'),
                'statistics': stats
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_safety_manager(self):
        """Test OBD safety manager"""
        try:
            # Test safe command
            safe_check = safety_manager.check_command_safety("01")
            
            # Test dangerous command
            dangerous_check = safety_manager.check_command_safety("04")
            
            # Test expert mode
            expert_enabled = safety_manager.enable_expert_mode("EXPERT_MODE_2024")
            expert_status = safety_manager.get_expert_mode_status()
            
            # Test dangerous command with expert mode
            expert_dangerous_check = safety_manager.check_command_safety("04")
            
            # Test confirmation prompt
            confirmation = safety_manager.create_confirmation_prompt("04")
            
            # Disable expert mode
            safety_manager.disable_expert_mode()
            
            return {
                'success': True,
                'safe_command_allowed': safe_check['allowed'],
                'dangerous_command_blocked': not dangerous_check['allowed'],
                'expert_mode_enabled': expert_enabled,
                'expert_mode_allows_dangerous': expert_dangerous_check['allowed'],
                'confirmation_created': bool(confirmation),
                'safety_summary': safety_manager.get_safety_summary()
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_live_monitor(self):
        """Test enhanced live data monitor"""
        try:
            # This test requires a mock OBD reader
            from app.obd_interface.mock_obd import MockOBDReader
            
            mock_reader = MockOBDReader()
            await mock_reader.connect()
            
            # Create enhanced monitor
            monitor = EnhancedLiveMonitor(mock_reader)
            
            # Test sensor definitions
            test_sensors = [
                SensorType.ENGINE_RPM,
                SensorType.ENGINE_LOAD,
                SensorType.FUEL_TRIM_SHORT_BANK1
            ]
            
            # Start monitoring briefly
            await monitor.start_monitoring(test_sensors)
            await asyncio.sleep(2)  # Monitor for 2 seconds
            await monitor.stop_monitoring()
            
            # Get results
            current_readings = monitor.get_current_readings()
            stats = monitor.get_monitoring_stats()
            
            await mock_reader.disconnect()
            
            return {
                'success': True,
                'sensors_monitored': len(test_sensors),
                'readings_collected': len(current_readings),
                'monitoring_stats': stats
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def test_full_integration(self):
        """Test full system integration"""
        try:
            # Test the complete workflow
            workflow_steps = []

            # 1. Database check
            db_manager = DatabaseManager()
            with db_manager.get_session() as session:
                dtc_count = session.execute("SELECT COUNT(*) FROM dtc_codes").scalar()
            workflow_steps.append(f"Database accessible: {dtc_count} DTCs")
            
            # 2. AI initialization
            ai_ready = gemini_integration.is_initialized
            if not ai_ready:
                ai_ready = await gemini_integration.initialize()
            workflow_steps.append(f"AI ready: {ai_ready}")
            
            # 3. Connection manager
            conn_health = await connection_manager.health_check()
            workflow_steps.append(f"Connection health: {conn_health['status']}")
            
            # 4. Safety system
            safety_summary = safety_manager.get_safety_summary()
            workflow_steps.append(f"Safety system: {safety_summary['safety_rules_count']} rules")
            
            return {
                'success': True,
                'workflow_steps': workflow_steps,
                'integration_complete': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def print_test_summary(self):
        """Print comprehensive test summary"""
        logger.info(f"\n{'='*60}")
        logger.info("COMPREHENSIVE SYSTEM TEST SUMMARY")
        logger.info(f"{'='*60}")
        
        total_tests = len(self.test_results)
        passed_tests = total_tests - len(self.failed_tests)
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {len(self.failed_tests)}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            logger.info(f"\nFailed Tests:")
            for test in self.failed_tests:
                logger.info(f"  ❌ {test}")
        
        logger.info(f"\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            logger.info(f"  {status} {test_name}")
            
            # Print key metrics
            if result.get('success', False):
                for key, value in result.items():
                    if key != 'success' and not key.startswith('error'):
                        logger.info(f"    {key}: {value}")
        
        logger.info(f"\n{'='*60}")


async def main():
    """Main test execution"""
    test_suite = ComprehensiveSystemTest()
    await test_suite.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
