"""
Test CAN Dump Integration

Tests for CAN dump parsing and mock OBD connection functionality.

Source Attribution:
- Test patterns based on automotive diagnostic testing standards
- Mock data validation inspired by python-OBD testing approaches
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

from app.can_dump_integration.can_parser import <PERSON><PERSON><PERSON><PERSON>arser, CANMessage, OBDResponse
from app.can_dump_integration.mock_obd_connection import MockOBDConnection


class TestCANDumpParser(unittest.TestCase):
    """Test CAN dump parsing functionality"""
    
    def setUp(self):
        self.parser = CANDumpParser()
        
        # Sample CAN dump data for testing
        self.sample_candump_data = """
(1579876676.199507) slcan0 2DE#0000000000000050
(1579876676.199539) slcan0 358#000A800000000000
(1579876676.199547) slcan0 1CA#0000000005005055
(1579876676.199553) slcan0 1CB#00000000000185
(1579876676.203851) slcan0 60D#2006000000000000
(1579876676.203882) slcan0 245#7FE80218E8007FE0
(1579876676.203891) slcan0 292#7EC8008010000000
(1579876676.203898) slcan0 130#003364
(1579876676.203904) slcan0 002#DD00000752
(1579876676.206795) slcan0 180#0000000000002800
(1579876676.206825) slcan0 1D5#00000000D6
(1579876676.211295) slcan0 280#03FFC0000000FFC0
(1579876676.214769) slcan0 285#000000000000E86F
(1579876676.221423) slcan0 1F9#0000000000000000
(1579876676.225189) slcan0 130#003162
(1579876676.227809) slcan0 215#FFF0FF04FFFF
(1579876676.232180) slcan0 1CB#00000000000000
(1579876676.236110) slcan0 130#003263
(1579876676.241167) slcan0 2DE#0000000000000050
(1579876676.245417) slcan0 292#7EC8008010000002
"""
    
    def test_parse_candump_line(self):
        """Test parsing individual CAN dump lines"""
        line = "(1579876676.199507) slcan0 2DE#0000000000000050"
        message = self.parser._parse_candump_line(line)
        
        self.assertIsNotNone(message)
        self.assertEqual(message.timestamp, 1579876676.199507)
        self.assertEqual(message.interface, "slcan0")
        self.assertEqual(message.can_id, "2DE")
        self.assertEqual(message.data, "0000000000000050")
        self.assertEqual(message.dlc, 8)
    
    def test_parse_candump_file(self):
        """Test parsing complete CAN dump file"""
        # Create temporary file with sample data
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
            f.write(self.sample_candump_data)
            temp_file = f.name
        
        try:
            messages = self.parser.parse_candump_file(temp_file)
            
            self.assertGreater(len(messages), 0)
            self.assertIsInstance(messages[0], CANMessage)
            
            # Check that timestamps are in order
            timestamps = [msg.timestamp for msg in messages]
            self.assertEqual(timestamps, sorted(timestamps))
            
        finally:
            os.unlink(temp_file)
    
    def test_extract_obd_responses(self):
        """Test extraction of OBD responses from CAN messages"""
        # Create sample messages including Nissan Leaf specific IDs
        messages = [
            CANMessage(1579876676.1, "slcan0", "1D5", "00000000D6", 5),  # Vehicle speed
            CANMessage(1579876676.2, "slcan0", "54A", "100070062A00002F", 8),  # Battery temp
            CANMessage(1579876676.3, "slcan0", "54B", "007698120C000000", 8),  # Battery voltage
            CANMessage(1579876676.4, "slcan0", "7E8", "04410C0BB800", 6),  # OBD2 RPM response
        ]
        
        responses = self.parser.extract_obd_responses(messages)
        
        self.assertGreater(len(responses), 0)
        
        # Check that we got some responses
        response_pids = [resp.pid for resp in responses]
        self.assertIn('0x0D', response_pids)  # Vehicle speed should be extracted
    
    def test_calculate_pid_value(self):
        """Test PID value calculation"""
        # Test RPM calculation (PID 0C)
        result = self.parser._calculate_pid_value('0C', '0BB8')
        self.assertEqual(result['value'], 750.0)  # ((11*256)+184)/4 = 750
        self.assertEqual(result['unit'], 'rpm')
        
        # Test vehicle speed calculation (PID 0D)
        result = self.parser._calculate_pid_value('0D', '3C')
        self.assertEqual(result['value'], 60)  # 60 km/h
        self.assertEqual(result['unit'], 'km/h')
    
    def test_get_statistics(self):
        """Test statistics generation"""
        messages = [
            CANMessage(1579876676.1, "slcan0", "1D5", "00000000D6", 5),
            CANMessage(1579876676.2, "slcan0", "1D5", "00000000D7", 5),
            CANMessage(1579876676.3, "slcan0", "54A", "100070062A00002F", 8),
        ]
        
        stats = self.parser.get_statistics(messages)
        
        self.assertEqual(stats['total_messages'], 3)
        self.assertEqual(stats['unique_can_ids'], 2)
        self.assertGreater(stats['time_span'], 0)
        self.assertIn('most_frequent_ids', stats)
    
    def test_nissan_leaf_message_decoding(self):
        """Test Nissan Leaf specific message decoding"""
        # Test vehicle speed message
        message = CANMessage(1579876676.1, "slcan0", "1D5", "00000000D6", 5)
        response = self.parser._decode_nissan_leaf_message(message)
        
        self.assertIsNotNone(response)
        self.assertEqual(response.pid, '0x0D')
        self.assertEqual(response.unit, 'km/h')
    
    def test_invalid_candump_line(self):
        """Test handling of invalid CAN dump lines"""
        invalid_lines = [
            "invalid line format",
            "(invalid_timestamp) slcan0 ABC#123",
            "(1579876676.1) slcan0 INVALID_ID#123",
            ""
        ]
        
        for line in invalid_lines:
            result = self.parser._parse_candump_line(line)
            # Should either return None or handle gracefully
            if result is not None:
                self.assertIsInstance(result, CANMessage)


class TestMockOBDConnection(unittest.TestCase):
    """Test mock OBD connection functionality"""
    
    def setUp(self):
        self.mock_connection = MockOBDConnection()
    
    def test_connection_lifecycle(self):
        """Test connection and disconnection"""
        # Initially not connected
        self.assertFalse(self.mock_connection.is_connected_status())
        
        # Connect
        result = self.mock_connection.connect()
        self.assertTrue(result)
        self.assertTrue(self.mock_connection.is_connected_status())
        
        # Disconnect
        result = self.mock_connection.disconnect()
        self.assertTrue(result)
        self.assertFalse(self.mock_connection.is_connected_status())
    
    def test_query_command(self):
        """Test OBD command querying"""
        self.mock_connection.connect()
        
        # Test supported command
        response = self.mock_connection.query_command('0C')  # RPM
        self.assertIsNotNone(response)
        self.assertIn('value', response)
        self.assertIn('unit', response)
        self.assertEqual(response['unit'], 'rpm')
        
        # Test unsupported command
        response = self.mock_connection.query_command('FF')
        self.assertIsNone(response)
    
    def test_get_dtcs(self):
        """Test DTC retrieval"""
        self.mock_connection.connect()
        
        dtcs = self.mock_connection.get_dtcs()
        self.assertIsInstance(dtcs, list)
        
        # Should have some mock DTCs
        self.assertGreater(len(dtcs), 0)
        
        # DTCs should be valid format
        for dtc in dtcs:
            self.assertRegex(dtc, r'^[PBCU]\d{4}$')
    
    def test_clear_dtcs(self):
        """Test DTC clearing"""
        self.mock_connection.connect()
        
        # Get initial DTCs
        initial_dtcs = self.mock_connection.get_dtcs()
        initial_count = len(initial_dtcs)
        
        # Clear DTCs
        result = self.mock_connection.clear_dtcs()
        self.assertTrue(result)
        
        # Check DTCs are cleared
        remaining_dtcs = self.mock_connection.get_dtcs()
        self.assertEqual(len(remaining_dtcs), 0)
    
    def test_get_supported_pids(self):
        """Test supported PIDs retrieval"""
        pids = self.mock_connection.get_supported_pids()
        
        self.assertIsInstance(pids, list)
        self.assertGreater(len(pids), 0)
        
        # Should include common PIDs
        self.assertIn('0C', pids)  # RPM
        self.assertIn('0D', pids)  # Vehicle speed
    
    def test_get_vehicle_info(self):
        """Test vehicle information retrieval"""
        info = self.mock_connection.get_vehicle_info()
        
        self.assertIsInstance(info, dict)
        self.assertIn('vin', info)
        self.assertIn('make', info)
        self.assertIn('model', info)
        self.assertIn('protocol', info)
    
    def test_get_statistics(self):
        """Test statistics retrieval"""
        stats = self.mock_connection.get_statistics()
        
        self.assertIsInstance(stats, dict)
        self.assertIn('connected', stats)
        self.assertIn('supported_commands', stats)
    
    def test_with_can_dump_file(self):
        """Test mock connection with CAN dump file"""
        # Create temporary CAN dump file
        sample_data = """
(1579876676.199507) slcan0 2DE#0000000000000050
(1579876676.203898) slcan0 130#003364
(1579876676.206825) slcan0 1D5#00000000D6
"""
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
            f.write(sample_data)
            temp_file = f.name
        
        try:
            # Create connection with CAN dump
            connection = MockOBDConnection(temp_file)
            
            # Should load CAN data
            self.assertTrue(connection.load_can_dump(temp_file))
            
            # Connect and test
            connection.connect()
            self.assertTrue(connection.is_connected_status())
            
            # Should have loaded messages
            stats = connection.get_statistics()
            self.assertGreater(stats['can_messages_loaded'], 0)
            
        finally:
            os.unlink(temp_file)
    
    def test_live_data_stream(self):
        """Test live data streaming"""
        self.mock_connection.connect()
        
        # Get a few data points from stream
        stream = self.mock_connection.get_live_data_stream()
        data_points = []
        
        for i, data in enumerate(stream):
            data_points.append(data)
            if i >= 5:  # Get 5 data points
                break
        
        self.assertEqual(len(data_points), 6)
        
        # Each data point should have required fields
        for data in data_points:
            self.assertIn('pid', data)
            self.assertIn('value', data)
            self.assertIn('unit', data)
            self.assertIn('timestamp', data)
    
    def test_query_without_connection(self):
        """Test querying without connection"""
        # Should return None when not connected
        response = self.mock_connection.query_command('0C')
        self.assertIsNone(response)
        
        dtcs = self.mock_connection.get_dtcs()
        self.assertEqual(len(dtcs), 0)


class TestCANDumpIntegration(unittest.TestCase):
    """Integration tests for CAN dump functionality"""
    
    def test_end_to_end_can_dump_processing(self):
        """Test complete CAN dump processing workflow"""
        # Create sample CAN dump with realistic Nissan Leaf data
        sample_data = """
(1579876676.199507) slcan0 2DE#0000000000000050
(1579876676.199547) slcan0 1CA#0000000005005055
(1579876676.206825) slcan0 1D5#00000000D6
(1579876676.227809) slcan0 54A#100070062A00002F
(1579876676.232180) slcan0 54B#007698120C000000
(1579876676.245417) slcan0 7E8#04410C0BB800
(1579876676.250000) slcan0 7E8#03410D3C
"""
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
            f.write(sample_data)
            temp_file = f.name
        
        try:
            # Parse CAN dump
            parser = CANDumpParser()
            messages = parser.parse_candump_file(temp_file)
            
            self.assertGreater(len(messages), 0)
            
            # Extract OBD responses
            responses = parser.extract_obd_responses(messages)
            self.assertGreater(len(responses), 0)
            
            # Create mock connection with this data
            connection = MockOBDConnection(temp_file)
            connection.connect()
            
            # Test that we can query data
            rpm_response = connection.query_command('0C')
            self.assertIsNotNone(rpm_response)
            
            speed_response = connection.query_command('0D')
            self.assertIsNotNone(speed_response)
            
            # Get statistics
            stats = connection.get_statistics()
            self.assertGreater(stats['can_messages_loaded'], 0)
            
        finally:
            os.unlink(temp_file)


if __name__ == '__main__':
    unittest.main()
